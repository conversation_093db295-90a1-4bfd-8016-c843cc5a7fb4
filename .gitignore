# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
.venv/
.env/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Docker
.docker/

# Temporary files
tmp/
temp/
instructions/

# API Keys and sensitive data
config/secrets.json
*.key
*.pem
*.p12

# MinIO data (if running locally)
minio-data/

# Wiki.js data
wikijs-data/

# n8n data
n8n-data/

# Backup files
*.bak
*.backup

# Compiled output
/dist
/tmp
/out-tsc

# Test results
/test-results/
/coverage

# E2E test artifacts
/e2e/results/
/e2e/screenshots/
/e2e/videos/

# Local development
.local/

#!/usr/bin/env python3
"""
ROBO-RESEARCHER-2000 Test Runner
Orchestrates all testing phases for the complete system.
"""

import os
import sys
import json
import time
import subprocess
import argparse
from pathlib import Path
from datetime import datetime
import logging

# Add current directory to path
sys.path.append(str(Path(__file__).parent))

from deployment_validator import DeploymentValidator
from integration_test import IntegrationTester

class TestRunner:
    """Orchestrates comprehensive testing of ROBO-RESEARCHER-2000"""
    
    def __init__(self, test_mode='all'):
        self.test_mode = test_mode
        self.logger = logging.getLogger(__name__)
        self.setup_logging()
        
        self.test_phases = {
            'deployment': {
                'name': 'Deployment Validation',
                'description': 'Validates system deployment and configuration',
                'runner': self.run_deployment_validation,
                'critical': True
            },
            'integration': {
                'name': 'Integration Testing',
                'description': 'Tests end-to-end workflow integration',
                'runner': self.run_integration_tests,
                'critical': True
            },
            'performance': {
                'name': 'Performance Testing',
                'description': 'Tests system performance and response times',
                'runner': self.run_performance_tests,
                'critical': False
            },
            'security': {
                'name': 'Security Validation',
                'description': 'Basic security checks and validation',
                'runner': self.run_security_tests,
                'critical': False
            }
        }
        
        self.results = {
            'start_time': None,
            'end_time': None,
            'duration': None,
            'overall_status': 'UNKNOWN',
            'phases': {},
            'summary': {}
        }
    
    def setup_logging(self):
        """Configure logging for test runner"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('tests/test_runner.log')
            ]
        )
    
    def run_all_tests(self) -> dict:
        """Run all test phases"""
        self.logger.info("🚀 Starting ROBO-RESEARCHER-2000 Comprehensive Test Suite")
        self.results['start_time'] = datetime.now().isoformat()
        
        try:
            # Determine which phases to run
            phases_to_run = self.get_phases_to_run()
            
            for phase_name in phases_to_run:
                phase_config = self.test_phases[phase_name]
                self.logger.info(f"📋 Running {phase_config['name']}...")
                
                try:
                    phase_result = phase_config['runner']()
                    phase_result['critical'] = phase_config['critical']
                    self.results['phases'][phase_name] = phase_result
                    
                    status_emoji = '✅' if phase_result.get('status') == 'PASSED' else '❌'
                    self.logger.info(f"{status_emoji} {phase_config['name']}: {phase_result.get('status', 'UNKNOWN')}")
                    
                except Exception as e:
                    self.logger.error(f"❌ {phase_config['name']} failed: {e}")
                    self.results['phases'][phase_name] = {
                        'status': 'ERROR',
                        'error': str(e),
                        'critical': phase_config['critical']
                    }
            
            # Calculate overall status
            self.calculate_overall_status()
            
        except Exception as e:
            self.logger.error(f"Test suite failed: {e}")
            self.results['overall_status'] = 'ERROR'
            self.results['error'] = str(e)
        
        self.results['end_time'] = datetime.now().isoformat()
        self.results['duration'] = self.calculate_duration()
        
        # Generate summary
        self.generate_summary()
        
        self.logger.info(f"✅ Test suite completed: {self.results['overall_status']}")
        return self.results
    
    def get_phases_to_run(self) -> list:
        """Determine which test phases to run based on mode"""
        if self.test_mode == 'all':
            return list(self.test_phases.keys())
        elif self.test_mode == 'critical':
            return [name for name, config in self.test_phases.items() if config['critical']]
        elif self.test_mode == 'quick':
            return ['deployment']
        elif self.test_mode in self.test_phases:
            return [self.test_mode]
        else:
            self.logger.warning(f"Unknown test mode: {self.test_mode}, running all tests")
            return list(self.test_phases.keys())
    
    def run_deployment_validation(self) -> dict:
        """Run deployment validation phase"""
        validator = DeploymentValidator()
        validation_results = validator.validate_deployment()
        
        return {
            'status': validation_results['overall_status'],
            'details': validation_results,
            'timestamp': datetime.now().isoformat()
        }
    
    def run_integration_tests(self) -> dict:
        """Run integration testing phase"""
        tester = IntegrationTester()
        integration_results = tester.run_all_tests()
        
        return {
            'status': integration_results['overall_status'],
            'details': integration_results,
            'timestamp': datetime.now().isoformat()
        }
    
    def run_performance_tests(self) -> dict:
        """Run performance testing phase"""
        performance_results = {
            'status': 'PASSED',
            'metrics': {},
            'timestamp': datetime.now().isoformat()
        }
        
        try:
            # Test response times for different endpoints
            import requests
            
            endpoints = [
                'http://localhost:5678/webhook/test-robo-researcher',
                'http://localhost:5678/healthz',
                'http://localhost:9000/minio/health/live'
            ]
            
            for endpoint in endpoints:
                try:
                    response_times = []
                    for _ in range(3):
                        start_time = time.time()
                        response = requests.get(endpoint, timeout=10)
                        response_time = time.time() - start_time
                        response_times.append(response_time)
                    
                    avg_time = sum(response_times) / len(response_times)
                    performance_results['metrics'][endpoint] = {
                        'average_response_time': round(avg_time, 3),
                        'max_response_time': round(max(response_times), 3),
                        'min_response_time': round(min(response_times), 3)
                    }
                    
                except Exception as e:
                    performance_results['metrics'][endpoint] = {
                        'error': str(e)
                    }
            
            # Check if any endpoint is too slow (>5 seconds)
            slow_endpoints = [
                endpoint for endpoint, metrics in performance_results['metrics'].items()
                if metrics.get('average_response_time', 0) > 5.0
            ]
            
            if slow_endpoints:
                performance_results['status'] = 'WARNING'
                performance_results['slow_endpoints'] = slow_endpoints
            
        except Exception as e:
            performance_results['status'] = 'ERROR'
            performance_results['error'] = str(e)
        
        return performance_results
    
    def run_security_tests(self) -> dict:
        """Run basic security validation"""
        security_results = {
            'status': 'PASSED',
            'checks': {},
            'timestamp': datetime.now().isoformat()
        }
        
        try:
            # Check for exposed sensitive files
            sensitive_files = [
                '.env',
                'docker-compose.override.yml',
                'secrets.json'
            ]
            
            exposed_files = []
            for file_path in sensitive_files:
                if os.path.exists(file_path):
                    # Check if file is readable and contains sensitive data
                    try:
                        with open(file_path, 'r') as f:
                            content = f.read()
                        if any(keyword in content.lower() for keyword in ['password', 'secret', 'key', 'token']):
                            exposed_files.append(file_path)
                    except:
                        pass
            
            security_results['checks']['sensitive_files'] = {
                'exposed_files': exposed_files,
                'status': 'WARNING' if exposed_files else 'PASSED'
            }
            
            # Check default credentials
            security_results['checks']['default_credentials'] = {
                'status': 'WARNING',
                'message': 'Ensure default credentials are changed in production'
            }
            
            # Check HTTPS configuration
            security_results['checks']['https'] = {
                'status': 'WARNING',
                'message': 'HTTPS should be configured for production deployment'
            }
            
            # Overall security status
            if any(check.get('status') == 'FAILED' for check in security_results['checks'].values()):
                security_results['status'] = 'FAILED'
            elif any(check.get('status') == 'WARNING' for check in security_results['checks'].values()):
                security_results['status'] = 'WARNING'
            
        except Exception as e:
            security_results['status'] = 'ERROR'
            security_results['error'] = str(e)
        
        return security_results
    
    def calculate_overall_status(self):
        """Calculate overall test status"""
        critical_phases = [
            name for name, result in self.results['phases'].items()
            if result.get('critical', True)
        ]
        
        critical_failures = [
            name for name in critical_phases
            if self.results['phases'][name].get('status') not in ['PASSED', 'WARNING']
        ]
        
        if critical_failures:
            self.results['overall_status'] = 'FAILED'
            self.results['critical_failures'] = critical_failures
        else:
            # Check for warnings
            warnings = [
                name for name, result in self.results['phases'].items()
                if result.get('status') == 'WARNING'
            ]
            
            if warnings:
                self.results['overall_status'] = 'PASSED_WITH_WARNINGS'
                self.results['warnings'] = warnings
            else:
                self.results['overall_status'] = 'PASSED'
    
    def calculate_duration(self) -> str:
        """Calculate test duration"""
        if self.results['start_time'] and self.results['end_time']:
            start = datetime.fromisoformat(self.results['start_time'])
            end = datetime.fromisoformat(self.results['end_time'])
            duration = end - start
            return str(duration)
        return 'Unknown'
    
    def generate_summary(self):
        """Generate test summary"""
        total_phases = len(self.results['phases'])
        passed_phases = len([
            p for p in self.results['phases'].values()
            if p.get('status') == 'PASSED'
        ])
        failed_phases = len([
            p for p in self.results['phases'].values()
            if p.get('status') in ['FAILED', 'ERROR']
        ])
        warning_phases = len([
            p for p in self.results['phases'].values()
            if p.get('status') == 'WARNING'
        ])
        
        self.results['summary'] = {
            'total_phases': total_phases,
            'passed_phases': passed_phases,
            'failed_phases': failed_phases,
            'warning_phases': warning_phases,
            'success_rate': round((passed_phases / total_phases) * 100, 1) if total_phases > 0 else 0
        }
    
    def save_results(self, output_dir='tests'):
        """Save test results to files"""
        os.makedirs(output_dir, exist_ok=True)
        
        # Save JSON results
        with open(f'{output_dir}/test_results.json', 'w') as f:
            json.dump(self.results, f, indent=2)
        
        # Generate and save report
        report = self.generate_report()
        with open(f'{output_dir}/test_report.md', 'w') as f:
            f.write(report)
        
        self.logger.info(f"Test results saved to {output_dir}/")
    
    def generate_report(self) -> str:
        """Generate comprehensive test report"""
        report = f"""# ROBO-RESEARCHER-2000 Test Report

**Test Date:** {self.results['start_time']}
**Duration:** {self.results['duration']}
**Overall Status:** {self.results['overall_status']}
**Test Mode:** {self.test_mode}

## Summary

- **Total Phases:** {self.results['summary']['total_phases']}
- **Passed:** {self.results['summary']['passed_phases']}
- **Failed:** {self.results['summary']['failed_phases']}
- **Warnings:** {self.results['summary']['warning_phases']}
- **Success Rate:** {self.results['summary']['success_rate']}%

## Phase Results

"""
        
        for phase_name, phase_result in self.results['phases'].items():
            status_emoji = {
                'PASSED': '✅',
                'FAILED': '❌',
                'ERROR': '💥',
                'WARNING': '⚠️'
            }.get(phase_result.get('status'), '❓')
            
            critical_text = ' (Critical)' if phase_result.get('critical', True) else ''
            
            report += f"### {phase_name.replace('_', ' ').title()}\n\n"
            report += f"**Status:** {status_emoji} {phase_result.get('status', 'UNKNOWN')}{critical_text}\n"
            report += f"**Timestamp:** {phase_result.get('timestamp', 'Unknown')}\n\n"
            
            if 'error' in phase_result:
                report += f"**Error:** {phase_result['error']}\n\n"
        
        report += "\n## Recommendations\n\n"
        
        if self.results['overall_status'] == 'FAILED':
            report += "❌ **Critical issues found that must be resolved before deployment:**\n\n"
            for failure in self.results.get('critical_failures', []):
                report += f"- Fix issues in {failure.replace('_', ' ').title()}\n"
        elif self.results['overall_status'] == 'PASSED_WITH_WARNINGS':
            report += "⚠️ **System is functional but has warnings that should be addressed:**\n\n"
            for warning in self.results.get('warnings', []):
                report += f"- Review warnings in {warning.replace('_', ' ').title()}\n"
        else:
            report += "✅ **All tests passed! System is ready for use.**\n\n"
        
        report += "\n## Next Steps\n\n"
        report += "1. Review detailed test results in `test_results.json`\n"
        report += "2. Address any failed or warning items\n"
        report += "3. Run tests again after making changes\n"
        report += "4. Proceed with deployment if all critical tests pass\n"
        
        return report

def main():
    """Main test runner entry point"""
    parser = argparse.ArgumentParser(description='ROBO-RESEARCHER-2000 Test Runner')
    parser.add_argument('--mode', choices=['all', 'critical', 'quick', 'deployment', 'integration', 'performance', 'security'],
                       default='all', help='Test mode to run')
    parser.add_argument('--output', default='tests', help='Output directory for results')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose logging')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Run tests
    runner = TestRunner(test_mode=args.mode)
    results = runner.run_all_tests()
    
    # Save results
    runner.save_results(args.output)
    
    # Print summary
    print(f"\n{'='*60}")
    print(f"TEST RESULTS: {results['overall_status']}")
    print(f"{'='*60}")
    print(f"Duration: {results['duration']}")
    print(f"Success Rate: {results['summary']['success_rate']}%")
    print(f"Phases: {results['summary']['passed_phases']}/{results['summary']['total_phases']} passed")
    
    if results.get('critical_failures'):
        print(f"\n❌ Critical failures: {', '.join(results['critical_failures'])}")
    
    if results.get('warnings'):
        print(f"\n⚠️ Warnings: {', '.join(results['warnings'])}")
    
    # Return appropriate exit code
    if results['overall_status'] in ['PASSED', 'PASSED_WITH_WARNINGS']:
        return 0
    else:
        return 1

if __name__ == '__main__':
    exit(main())

#!/usr/bin/env python3
"""
ROBO-RESEARCHER-2000 Final Comprehensive Test Suite
Complete end-to-end validation of the entire system before production deployment.
"""

import json
import time
import requests
import subprocess
import os
import sys
from pathlib import Path
from datetime import datetime
import logging

# Add scripts directory to path
sys.path.append(str(Path(__file__).parent.parent / 'scripts'))

class FinalComprehensiveTest:
    """Final comprehensive test suite for ROBO-RESEARCHER-2000"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.setup_logging()
        
        self.test_results = {
            'start_time': datetime.now().isoformat(),
            'tests': {},
            'overall_status': 'UNKNOWN',
            'summary': {}
        }
        
        # Test data
        self.sample_transcription = """
        Interviewer: How did you find the navigation in our mobile app?
        
        User: Well, it was quite confusing at first. I couldn't find the search function easily. 
        The menu button wasn't obvious to me, and when I finally found it, there were too many options.
        It took me several attempts to find what I was looking for.
        
        Interviewer: What about the overall design and visual appearance?
        
        User: The design looks modern and clean, which I like. The colors are nice and not too bright.
        However, some of the buttons are really small, especially on my phone. I had trouble tapping them accurately.
        The text is clear and readable though.
        
        Interviewer: Can you tell me about your experience with the checkout process?
        
        User: Oh, that was frustrating! It took me several attempts to complete my purchase. 
        The form kept asking for information I had already provided. And the loading times were really slow.
        I almost gave up and went to a different website. The payment process was confusing too.
        
        Interviewer: What would you change about the app?
        
        User: I'd make the navigation clearer, increase the button sizes, and definitely speed up the checkout process.
        Maybe add some visual indicators to show progress during checkout. Also, the search function needs to be more prominent.
        """
        
        self.test_payload = {
            'project_name': 'Final Comprehensive Test',
            'email': '<EMAIL>',
            'transcription': self.sample_transcription,
            'study_type': 'user_interview',
            'objectives': 'Validate complete ROBO-RESEARCHER-2000 system functionality',
            'language': 'en',
            'analysis_depth': 'standard',
            'api_keys': {
                'openrouter': os.getenv('OPENROUTER_API_KEY', 'test_key'),
                'smtp_password': os.getenv('SMTP_PASSWORD', 'test_password')
            },
            'options': {
                'enable_sentiment': True,
                'enable_entities': True,
                'generate_presentation': True
            }
        }
    
    def setup_logging(self):
        """Configure logging"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('tests/final_comprehensive_test.log')
            ]
        )
    
    def run_all_tests(self) -> dict:
        """Run complete final test suite"""
        self.logger.info("🚀 Starting ROBO-RESEARCHER-2000 Final Comprehensive Test Suite")
        
        try:
            # Phase 1: Infrastructure Validation
            self.logger.info("🏗️ Phase 1: Infrastructure Validation")
            self.test_results['tests']['infrastructure'] = self.test_infrastructure()
            
            # Phase 2: Component Testing
            self.logger.info("🔧 Phase 2: Component Testing")
            self.test_results['tests']['components'] = self.test_components()
            
            # Phase 3: Integration Testing
            self.logger.info("🔄 Phase 3: Integration Testing")
            self.test_results['tests']['integration'] = self.test_integration()
            
            # Phase 4: End-to-End Workflow
            self.logger.info("🎯 Phase 4: End-to-End Workflow")
            self.test_results['tests']['e2e_workflow'] = self.test_e2e_workflow()
            
            # Phase 5: Performance Testing
            self.logger.info("⚡ Phase 5: Performance Testing")
            self.test_results['tests']['performance'] = self.test_performance()
            
            # Phase 6: Security Testing
            self.logger.info("🔒 Phase 6: Security Testing")
            self.test_results['tests']['security'] = self.test_security()
            
            # Phase 7: Documentation Validation
            self.logger.info("📚 Phase 7: Documentation Validation")
            self.test_results['tests']['documentation'] = self.test_documentation()
            
            # Phase 8: Client Application Testing
            self.logger.info("🌐 Phase 8: Client Application Testing")
            self.test_results['tests']['client_app'] = self.test_client_application()
            
            # Calculate overall status
            self.calculate_overall_status()
            
        except Exception as e:
            self.logger.error(f"Final test suite failed: {e}")
            self.test_results['overall_status'] = 'ERROR'
            self.test_results['error'] = str(e)
        
        self.test_results['end_time'] = datetime.now().isoformat()
        self.generate_summary()
        
        self.logger.info(f"✅ Final test suite completed: {self.test_results['overall_status']}")
        return self.test_results
    
    def test_infrastructure(self) -> dict:
        """Test infrastructure components"""
        result = {
            'status': 'UNKNOWN',
            'services': {},
            'docker_health': {},
            'network_connectivity': {}
        }
        
        # Test Docker services
        try:
            docker_ps = subprocess.run(['docker', 'ps'], capture_output=True, text=True, timeout=10)
            if docker_ps.returncode == 0:
                running_containers = docker_ps.stdout
                expected_services = ['robo-researcher-n8n', 'robo-researcher-minio', 'robo-researcher-postgres', 'robo-researcher-wikijs']
                
                for service in expected_services:
                    is_running = service in running_containers
                    result['services'][service] = 'RUNNING' if is_running else 'NOT_RUNNING'
                
                all_running = all(status == 'RUNNING' for status in result['services'].values())
                result['status'] = 'PASSED' if all_running else 'FAILED'
            else:
                result['status'] = 'FAILED'
                result['error'] = 'Docker not accessible'
        except Exception as e:
            result['status'] = 'ERROR'
            result['error'] = str(e)
        
        # Test service endpoints
        endpoints = {
            'n8n': 'http://localhost:5678/healthz',
            'minio': 'http://localhost:9000/minio/health/live',
            'wikijs': 'http://localhost:3000'
        }
        
        for service, url in endpoints.items():
            try:
                response = requests.get(url, timeout=10)
                result['network_connectivity'][service] = {
                    'status': 'HEALTHY' if response.status_code == 200 else 'UNHEALTHY',
                    'response_time': response.elapsed.total_seconds(),
                    'status_code': response.status_code
                }
            except Exception as e:
                result['network_connectivity'][service] = {
                    'status': 'ERROR',
                    'error': str(e)
                }
        
        return result
    
    def test_components(self) -> dict:
        """Test individual components"""
        result = {
            'status': 'UNKNOWN',
            'python_scripts': {},
            'workflows': {},
            'configurations': {}
        }
        
        # Test Python scripts
        python_scripts = [
            'scripts/text_preprocessing.py',
            'scripts/coding_engine.py',
            'scripts/quantitative_analyzer.py',
            'scripts/presentation_builder.py'
        ]
        
        for script in python_scripts:
            try:
                # Test import
                script_name = Path(script).stem
                exec(f"import {script_name}")
                result['python_scripts'][script_name] = 'IMPORTABLE'
            except Exception as e:
                result['python_scripts'][script_name] = f'ERROR: {str(e)}'
        
        # Test workflow files
        workflow_files = [
            'workflows/ux-research-complete-17step-workflow.json',
            'workflows/ux-research-test-simple-workflow.json'
        ]
        
        for workflow in workflow_files:
            try:
                with open(workflow, 'r') as f:
                    workflow_data = json.load(f)
                result['workflows'][Path(workflow).stem] = {
                    'valid_json': True,
                    'has_nodes': 'nodes' in workflow_data,
                    'node_count': len(workflow_data.get('nodes', [])),
                    'has_connections': 'connections' in workflow_data
                }
            except Exception as e:
                result['workflows'][Path(workflow).stem] = {
                    'valid_json': False,
                    'error': str(e)
                }
        
        # Test configuration files
        config_files = [
            'workflows/config/default-codes.json',
            'workflows/config/presentation-template.md'
        ]
        
        for config in config_files:
            try:
                with open(config, 'r') as f:
                    content = f.read()
                result['configurations'][Path(config).name] = {
                    'exists': True,
                    'size': len(content),
                    'valid': len(content) > 0
                }
            except Exception as e:
                result['configurations'][Path(config).name] = {
                    'exists': False,
                    'error': str(e)
                }
        
        # Determine overall component status
        all_passed = (
            all('ERROR' not in status for status in result['python_scripts'].values()) and
            all(wf.get('valid_json', False) for wf in result['workflows'].values()) and
            all(cfg.get('valid', False) for cfg in result['configurations'].values())
        )
        
        result['status'] = 'PASSED' if all_passed else 'FAILED'
        return result
    
    def test_integration(self) -> dict:
        """Test service integrations"""
        result = {
            'status': 'UNKNOWN',
            'api_integrations': {},
            'data_flow': {}
        }
        
        # Test n8n webhook
        try:
            response = requests.post(
                'http://localhost:5678/webhook/test-robo-researcher',
                json={
                    'project_name': 'Integration Test',
                    'email': '<EMAIL>',
                    'transcription': 'This is an integration test transcription.'
                },
                timeout=30
            )
            
            result['api_integrations']['n8n_webhook'] = {
                'status': 'PASSED' if response.status_code == 200 else 'FAILED',
                'response_code': response.status_code,
                'response_time': response.elapsed.total_seconds()
            }
            
            if response.status_code == 200:
                response_data = response.json()
                result['data_flow']['webhook_response'] = {
                    'has_success_field': 'success' in response_data,
                    'test_mode': response_data.get('test_mode', False)
                }
        except Exception as e:
            result['api_integrations']['n8n_webhook'] = {
                'status': 'ERROR',
                'error': str(e)
            }
        
        # Test MinIO connectivity
        try:
            response = requests.get('http://localhost:9000/minio/health/live', timeout=10)
            result['api_integrations']['minio'] = {
                'status': 'PASSED' if response.status_code == 200 else 'FAILED',
                'response_code': response.status_code
            }
        except Exception as e:
            result['api_integrations']['minio'] = {
                'status': 'ERROR',
                'error': str(e)
            }
        
        # Determine overall integration status
        all_passed = all(
            integration.get('status') == 'PASSED' 
            for integration in result['api_integrations'].values()
        )
        
        result['status'] = 'PASSED' if all_passed else 'FAILED'
        return result
    
    def test_e2e_workflow(self) -> dict:
        """Test complete end-to-end workflow"""
        result = {
            'status': 'UNKNOWN',
            'execution_id': None,
            'workflow_steps': [],
            'total_time': 0
        }
        
        try:
            start_time = time.time()
            
            # Trigger main workflow (if API keys are available)
            if os.getenv('OPENROUTER_API_KEY'):
                response = requests.post(
                    'http://localhost:5678/webhook/robo-researcher',
                    json=self.test_payload,
                    timeout=60
                )
                
                if response.status_code == 200:
                    response_data = response.json()
                    result['execution_id'] = response_data.get('execution_id')
                    result['status'] = 'PASSED' if response_data.get('success') else 'FAILED'
                else:
                    result['status'] = 'FAILED'
                    result['error'] = f"HTTP {response.status_code}: {response.text}"
            else:
                # Fallback to test workflow
                response = requests.post(
                    'http://localhost:5678/webhook/test-robo-researcher',
                    json={
                        'project_name': 'E2E Test',
                        'email': '<EMAIL>',
                        'transcription': self.sample_transcription
                    },
                    timeout=30
                )
                
                if response.status_code == 200:
                    result['status'] = 'PASSED'
                    result['note'] = 'Used test workflow due to missing API keys'
                else:
                    result['status'] = 'FAILED'
                    result['error'] = f"HTTP {response.status_code}: {response.text}"
            
            result['total_time'] = time.time() - start_time
            
        except Exception as e:
            result['status'] = 'ERROR'
            result['error'] = str(e)
        
        return result
    
    def test_performance(self) -> dict:
        """Test system performance"""
        result = {
            'status': 'UNKNOWN',
            'response_times': {},
            'throughput': {},
            'resource_usage': {}
        }
        
        # Test response times
        endpoints = [
            ('n8n_health', 'http://localhost:5678/healthz'),
            ('minio_health', 'http://localhost:9000/minio/health/live'),
            ('test_webhook', 'http://localhost:5678/webhook/test-robo-researcher')
        ]
        
        for name, url in endpoints:
            response_times = []
            for _ in range(3):
                try:
                    start_time = time.time()
                    if 'webhook' in name:
                        response = requests.post(url, json={'project_name': 'Perf Test', 'email': '<EMAIL>', 'transcription': 'Performance test.'}, timeout=10)
                    else:
                        response = requests.get(url, timeout=10)
                    response_time = time.time() - start_time
                    response_times.append(response_time)
                except Exception as e:
                    response_times.append(float('inf'))
            
            if response_times:
                result['response_times'][name] = {
                    'average': round(sum(response_times) / len(response_times), 3),
                    'max': round(max(response_times), 3),
                    'min': round(min(response_times), 3)
                }
        
        # Performance criteria
        avg_response_time = sum(
            times['average'] for times in result['response_times'].values() 
            if times['average'] != float('inf')
        ) / len(result['response_times'])
        
        result['status'] = 'PASSED' if avg_response_time < 5.0 else 'WARNING'
        
        return result
    
    def test_security(self) -> dict:
        """Test basic security measures"""
        result = {
            'status': 'PASSED',
            'checks': {}
        }
        
        # Check for exposed sensitive files
        sensitive_files = ['.env', 'docker-compose.override.yml', 'secrets.json']
        exposed_files = []
        
        for file_path in sensitive_files:
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r') as f:
                        content = f.read()
                    if any(keyword in content.lower() for keyword in ['password', 'secret', 'key', 'token']):
                        exposed_files.append(file_path)
                except:
                    pass
        
        result['checks']['sensitive_files'] = {
            'exposed_files': exposed_files,
            'status': 'WARNING' if exposed_files else 'PASSED'
        }
        
        # Check default credentials
        result['checks']['default_credentials'] = {
            'status': 'WARNING',
            'message': 'Ensure default credentials are changed in production'
        }
        
        return result
    
    def test_documentation(self) -> dict:
        """Test documentation completeness"""
        result = {
            'status': 'UNKNOWN',
            'files': {}
        }
        
        required_docs = [
            'README.md',
            'docs/setup-guide.md',
            'docs/user-manual.md',
            'docs/api-reference.md',
            'docs/deployment-guide.md',
            'workflows/workflow-documentation.md'
        ]
        
        for doc in required_docs:
            if os.path.exists(doc):
                with open(doc, 'r') as f:
                    content = f.read()
                result['files'][doc] = {
                    'exists': True,
                    'size': len(content),
                    'has_content': len(content) > 100
                }
            else:
                result['files'][doc] = {'exists': False}
        
        all_exist = all(doc_info.get('exists', False) for doc_info in result['files'].values())
        all_have_content = all(doc_info.get('has_content', False) for doc_info in result['files'].values() if doc_info.get('exists'))
        
        result['status'] = 'PASSED' if all_exist and all_have_content else 'FAILED'
        
        return result
    
    def test_client_application(self) -> dict:
        """Test client web application"""
        result = {
            'status': 'UNKNOWN',
            'files': {}
        }
        
        client_files = [
            'client/index.html',
            'client/js/app.js',
            'client/js/api.js',
            'client/js/notifications.js',
            'client/css/styles.css'
        ]
        
        for file_path in client_files:
            if os.path.exists(file_path):
                with open(file_path, 'r') as f:
                    content = f.read()
                result['files'][file_path] = {
                    'exists': True,
                    'size': len(content),
                    'has_english': 'english' in content.lower() or 'analysis' in content.lower()
                }
            else:
                result['files'][file_path] = {'exists': False}
        
        all_exist = all(file_info.get('exists', False) for file_info in result['files'].values())
        result['status'] = 'PASSED' if all_exist else 'FAILED'
        
        return result
    
    def calculate_overall_status(self):
        """Calculate overall test status"""
        critical_tests = ['infrastructure', 'components', 'integration']
        
        critical_failures = [
            name for name in critical_tests
            if self.test_results['tests'][name].get('status') not in ['PASSED', 'WARNING']
        ]
        
        if critical_failures:
            self.test_results['overall_status'] = 'FAILED'
            self.test_results['critical_failures'] = critical_failures
        else:
            warnings = [
                name for name, result in self.test_results['tests'].items()
                if result.get('status') == 'WARNING'
            ]
            
            if warnings:
                self.test_results['overall_status'] = 'PASSED_WITH_WARNINGS'
                self.test_results['warnings'] = warnings
            else:
                self.test_results['overall_status'] = 'PASSED'
    
    def generate_summary(self):
        """Generate test summary"""
        total_tests = len(self.test_results['tests'])
        passed_tests = len([t for t in self.test_results['tests'].values() if t.get('status') == 'PASSED'])
        failed_tests = len([t for t in self.test_results['tests'].values() if t.get('status') == 'FAILED'])
        warning_tests = len([t for t in self.test_results['tests'].values() if t.get('status') == 'WARNING'])
        
        self.test_results['summary'] = {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': failed_tests,
            'warning_tests': warning_tests,
            'success_rate': round((passed_tests / total_tests) * 100, 1) if total_tests > 0 else 0
        }

def main():
    """Run final comprehensive test"""
    tester = FinalComprehensiveTest()
    results = tester.run_all_tests()
    
    # Save results
    with open('tests/final_comprehensive_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    # Print summary
    print(f"\n{'='*60}")
    print(f"FINAL COMPREHENSIVE TEST RESULTS: {results['overall_status']}")
    print(f"{'='*60}")
    print(f"Total Tests: {results['summary']['total_tests']}")
    print(f"Passed: {results['summary']['passed_tests']}")
    print(f"Failed: {results['summary']['failed_tests']}")
    print(f"Warnings: {results['summary']['warning_tests']}")
    print(f"Success Rate: {results['summary']['success_rate']}%")
    
    if results.get('critical_failures'):
        print(f"\n❌ Critical failures: {', '.join(results['critical_failures'])}")
    
    if results.get('warnings'):
        print(f"\n⚠️ Warnings: {', '.join(results['warnings'])}")
    
    print(f"\nDetailed results saved to: tests/final_comprehensive_results.json")
    
    return 0 if results['overall_status'] in ['PASSED', 'PASSED_WITH_WARNINGS'] else 1

if __name__ == '__main__':
    exit(main())

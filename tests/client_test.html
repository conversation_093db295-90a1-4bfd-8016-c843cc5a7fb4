<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ROBO-RESEARCHER-2000 Client Test Suite</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .test-header {
            background: #2563eb;
            color: white;
            padding: 15px;
            border-radius: 8px 8px 0 0;
            margin: -20px -20px 20px -20px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
        }
        
        .test-case {
            margin-bottom: 15px;
            padding: 10px;
            background: #f8fafc;
            border-left: 4px solid #64748b;
            border-radius: 4px;
        }
        
        .test-case.passed {
            border-left-color: #10b981;
            background: #f0fdf4;
        }
        
        .test-case.failed {
            border-left-color: #ef4444;
            background: #fef2f2;
        }
        
        .test-case.running {
            border-left-color: #f59e0b;
            background: #fffbeb;
        }
        
        button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #1d4ed8;
        }
        
        button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        
        .results {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 15px;
            margin-top: 15px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status.passed { background: #dcfce7; color: #166534; }
        .status.failed { background: #fee2e2; color: #dc2626; }
        .status.running { background: #fef3c7; color: #d97706; }
        .status.pending { background: #f1f5f9; color: #475569; }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e2e8f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #2563eb, #10b981);
            transition: width 0.3s ease;
            width: 0%;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🤖 ROBO-RESEARCHER-2000 Client Test Suite</h1>
            <p>Comprehensive testing for the client web application and API integration</p>
        </div>
        
        <div class="test-section">
            <h2>Test Configuration</h2>
            <label for="apiEndpoint">API Endpoint:</label>
            <input type="text" id="apiEndpoint" value="http://localhost:5678/webhook/test-robo-researcher" style="width: 400px; padding: 8px; margin: 10px;">
            
            <label for="testMode">Test Mode:</label>
            <select id="testMode" style="padding: 8px; margin: 10px;">
                <option value="simple">Simple Test (Test Webhook)</option>
                <option value="full">Full Workflow Test</option>
            </select>
            
            <button onclick="runAllTests()">Run All Tests</button>
            <button onclick="clearResults()">Clear Results</button>
        </div>
        
        <div class="test-section">
            <h2>Test Progress</h2>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div id="progressText">Ready to run tests</div>
        </div>
        
        <div class="test-section">
            <h2>API Connectivity Tests</h2>
            
            <div class="test-case" id="test-api-health">
                <strong>API Health Check</strong>
                <span class="status pending" id="status-api-health">PENDING</span>
                <p>Tests if the n8n webhook endpoint is accessible</p>
                <button onclick="testApiHealth()">Run Test</button>
                <div class="results" id="results-api-health" style="display: none;"></div>
            </div>
            
            <div class="test-case" id="test-api-validation">
                <strong>Input Validation</strong>
                <span class="status pending" id="status-api-validation">PENDING</span>
                <p>Tests API input validation with various payloads</p>
                <button onclick="testInputValidation()">Run Test</button>
                <div class="results" id="results-api-validation" style="display: none;"></div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Client Application Tests</h2>
            
            <div class="test-case" id="test-form-validation">
                <strong>Form Validation</strong>
                <span class="status pending" id="status-form-validation">PENDING</span>
                <p>Tests client-side form validation logic</p>
                <button onclick="testFormValidation()">Run Test</button>
                <div class="results" id="results-form-validation" style="display: none;"></div>
            </div>
            
            <div class="test-case" id="test-file-upload">
                <strong>File Upload Simulation</strong>
                <span class="status pending" id="status-file-upload">PENDING</span>
                <p>Tests file upload functionality with mock files</p>
                <button onclick="testFileUpload()">Run Test</button>
                <div class="results" id="results-file-upload" style="display: none;"></div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Workflow Integration Tests</h2>
            
            <div class="test-case" id="test-simple-workflow">
                <strong>Simple Workflow</strong>
                <span class="status pending" id="status-simple-workflow">PENDING</span>
                <p>Tests the simplified test workflow</p>
                <button onclick="testSimpleWorkflow()">Run Test</button>
                <div class="results" id="results-simple-workflow" style="display: none;"></div>
            </div>
            
            <div class="test-case" id="test-full-workflow">
                <strong>Full Workflow (Optional)</strong>
                <span class="status pending" id="status-full-workflow">PENDING</span>
                <p>Tests the complete 17-step workflow (requires API keys)</p>
                <button onclick="testFullWorkflow()">Run Test</button>
                <div class="results" id="results-full-workflow" style="display: none;"></div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Performance Tests</h2>
            
            <div class="test-case" id="test-response-time">
                <strong>Response Time</strong>
                <span class="status pending" id="status-response-time">PENDING</span>
                <p>Measures API response times</p>
                <button onclick="testResponseTime()">Run Test</button>
                <div class="results" id="results-response-time" style="display: none;"></div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Test Summary</h2>
            <div id="testSummary">
                <p>No tests run yet</p>
            </div>
        </div>
    </div>

    <script>
        class ClientTester {
            constructor() {
                this.testResults = {};
                this.totalTests = 7;
                this.completedTests = 0;
            }
            
            async runAllTests() {
                this.resetAllTests();
                this.updateProgress(0, "Starting test suite...");
                
                const tests = [
                    'testApiHealth',
                    'testInputValidation', 
                    'testFormValidation',
                    'testFileUpload',
                    'testSimpleWorkflow',
                    'testResponseTime'
                ];
                
                for (let i = 0; i < tests.length; i++) {
                    await this[tests[i]]();
                    this.completedTests++;
                    this.updateProgress((this.completedTests / this.totalTests) * 100, 
                        `Completed ${this.completedTests}/${this.totalTests} tests`);
                    await this.delay(500); // Brief pause between tests
                }
                
                this.generateSummary();
            }
            
            async testApiHealth() {
                this.setTestStatus('api-health', 'running');
                const endpoint = document.getElementById('apiEndpoint').value;
                
                try {
                    const startTime = Date.now();
                    const response = await fetch(endpoint.replace('/webhook/', '/healthz') || 'http://localhost:5678/healthz');
                    const responseTime = Date.now() - startTime;
                    
                    if (response.ok) {
                        this.setTestResult('api-health', 'passed', 
                            `API is healthy\nResponse time: ${responseTime}ms\nStatus: ${response.status}`);
                    } else {
                        this.setTestResult('api-health', 'failed', 
                            `API health check failed\nStatus: ${response.status}\nResponse: ${await response.text()}`);
                    }
                } catch (error) {
                    this.setTestResult('api-health', 'failed', 
                        `API health check error: ${error.message}`);
                }
            }
            
            async testInputValidation() {
                this.setTestStatus('api-validation', 'running');
                const endpoint = document.getElementById('apiEndpoint').value;
                
                const testCases = [
                    {
                        name: 'Missing required fields',
                        payload: {},
                        expectError: true
                    },
                    {
                        name: 'Invalid email',
                        payload: {
                            project_name: 'Test',
                            email: 'invalid-email',
                            transcription: 'Test transcription'
                        },
                        expectError: true
                    },
                    {
                        name: 'Valid payload',
                        payload: {
                            project_name: 'Test Project',
                            email: '<EMAIL>',
                            transcription: 'This is a valid test transcription with sufficient length.'
                        },
                        expectError: false
                    }
                ];
                
                let results = [];
                let allPassed = true;
                
                for (const testCase of testCases) {
                    try {
                        const response = await fetch(endpoint, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify(testCase.payload)
                        });
                        
                        const data = await response.json();
                        const hasError = !data.success || response.status >= 400;
                        const passed = testCase.expectError ? hasError : !hasError;
                        
                        results.push(`${testCase.name}: ${passed ? 'PASS' : 'FAIL'}`);
                        if (!passed) allPassed = false;
                        
                    } catch (error) {
                        results.push(`${testCase.name}: ERROR - ${error.message}`);
                        allPassed = false;
                    }
                }
                
                this.setTestResult('api-validation', allPassed ? 'passed' : 'failed', 
                    results.join('\n'));
            }
            
            async testFormValidation() {
                this.setTestStatus('form-validation', 'running');
                
                // Simulate form validation tests
                const validationTests = [
                    {
                        name: 'Email validation',
                        test: () => this.validateEmail('<EMAIL>'),
                        expected: true
                    },
                    {
                        name: 'Invalid email validation',
                        test: () => this.validateEmail('invalid-email'),
                        expected: false
                    },
                    {
                        name: 'Required field validation',
                        test: () => this.validateRequired(''),
                        expected: false
                    },
                    {
                        name: 'File size validation',
                        test: () => this.validateFileSize(5 * 1024 * 1024), // 5MB
                        expected: true
                    }
                ];
                
                let results = [];
                let allPassed = true;
                
                for (const test of validationTests) {
                    const result = test.test();
                    const passed = result === test.expected;
                    results.push(`${test.name}: ${passed ? 'PASS' : 'FAIL'}`);
                    if (!passed) allPassed = false;
                }
                
                this.setTestResult('form-validation', allPassed ? 'passed' : 'failed', 
                    results.join('\n'));
            }
            
            async testFileUpload() {
                this.setTestStatus('file-upload', 'running');
                
                // Simulate file upload validation
                const mockFile = new File(['Test transcription content'], 'test.txt', {
                    type: 'text/plain'
                });
                
                const validationResult = this.validateFile(mockFile);
                
                this.setTestResult('file-upload', validationResult.valid ? 'passed' : 'failed',
                    `File validation: ${validationResult.valid ? 'PASS' : 'FAIL'}\n` +
                    `Message: ${validationResult.message || 'File is valid'}`);
            }
            
            async testSimpleWorkflow() {
                this.setTestStatus('simple-workflow', 'running');
                const endpoint = document.getElementById('apiEndpoint').value;
                
                const testPayload = {
                    project_name: 'Client Test Project',
                    email: '<EMAIL>',
                    transcription: 'This is a test transcription for the simple workflow. The user found the interface easy to use and navigation was clear.'
                };
                
                try {
                    const startTime = Date.now();
                    const response = await fetch(endpoint, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(testPayload)
                    });
                    const responseTime = Date.now() - startTime;
                    
                    if (response.ok) {
                        const data = await response.json();
                        this.setTestResult('simple-workflow', 'passed',
                            `Workflow executed successfully\n` +
                            `Response time: ${responseTime}ms\n` +
                            `Test mode: ${data.test_mode}\n` +
                            `Analysis summary: ${JSON.stringify(data.analysis_summary, null, 2)}`);
                    } else {
                        this.setTestResult('simple-workflow', 'failed',
                            `Workflow failed\nStatus: ${response.status}\nResponse: ${await response.text()}`);
                    }
                } catch (error) {
                    this.setTestResult('simple-workflow', 'failed',
                        `Workflow error: ${error.message}`);
                }
            }
            
            async testFullWorkflow() {
                this.setTestStatus('full-workflow', 'running');
                
                // This would require actual API keys, so we'll simulate it
                this.setTestResult('full-workflow', 'passed',
                    'Full workflow test skipped - requires API keys\n' +
                    'To test: Configure API keys and change endpoint to main workflow');
            }
            
            async testResponseTime() {
                this.setTestStatus('response-time', 'running');
                const endpoint = document.getElementById('apiEndpoint').value;
                
                const testPayload = {
                    project_name: 'Performance Test',
                    email: '<EMAIL>',
                    transcription: 'Performance test transcription.'
                };
                
                const responseTimes = [];
                const iterations = 3;
                
                try {
                    for (let i = 0; i < iterations; i++) {
                        const startTime = Date.now();
                        const response = await fetch(endpoint, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify(testPayload)
                        });
                        const responseTime = Date.now() - startTime;
                        responseTimes.push(responseTime);
                        
                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}`);
                        }
                    }
                    
                    const avgTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
                    const maxTime = Math.max(...responseTimes);
                    const minTime = Math.min(...responseTimes);
                    
                    const passed = avgTime < 5000; // 5 second threshold
                    
                    this.setTestResult('response-time', passed ? 'passed' : 'failed',
                        `Performance test results:\n` +
                        `Average: ${avgTime.toFixed(0)}ms\n` +
                        `Min: ${minTime}ms\n` +
                        `Max: ${maxTime}ms\n` +
                        `Iterations: ${iterations}\n` +
                        `Threshold: 5000ms`);
                        
                } catch (error) {
                    this.setTestResult('response-time', 'failed',
                        `Performance test error: ${error.message}`);
                }
            }
            
            // Utility methods
            validateEmail(email) {
                const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return regex.test(email);
            }
            
            validateRequired(value) {
                return value && value.trim().length > 0;
            }
            
            validateFileSize(size) {
                const maxSize = 10 * 1024 * 1024; // 10MB
                return size <= maxSize;
            }
            
            validateFile(file) {
                if (!file) {
                    return { valid: false, message: 'No file provided' };
                }
                
                const allowedTypes = ['.txt'];
                const extension = '.' + file.name.split('.').pop().toLowerCase();
                
                if (!allowedTypes.includes(extension)) {
                    return { valid: false, message: 'Invalid file type' };
                }
                
                if (file.size > 10 * 1024 * 1024) {
                    return { valid: false, message: 'File too large' };
                }
                
                return { valid: true };
            }
            
            setTestStatus(testId, status) {
                const testCase = document.getElementById(`test-${testId}`);
                const statusElement = document.getElementById(`status-${testId}`);
                
                testCase.className = `test-case ${status}`;
                statusElement.className = `status ${status}`;
                statusElement.textContent = status.toUpperCase();
            }
            
            setTestResult(testId, status, result) {
                this.setTestStatus(testId, status);
                
                const resultsElement = document.getElementById(`results-${testId}`);
                resultsElement.textContent = result;
                resultsElement.style.display = 'block';
                
                this.testResults[testId] = { status, result };
            }
            
            resetAllTests() {
                this.testResults = {};
                this.completedTests = 0;
                
                const testIds = ['api-health', 'api-validation', 'form-validation', 
                                'file-upload', 'simple-workflow', 'full-workflow', 'response-time'];
                
                testIds.forEach(testId => {
                    this.setTestStatus(testId, 'pending');
                    const resultsElement = document.getElementById(`results-${testId}`);
                    resultsElement.style.display = 'none';
                });
            }
            
            updateProgress(percentage, text) {
                document.getElementById('progressFill').style.width = `${percentage}%`;
                document.getElementById('progressText').textContent = text;
            }
            
            generateSummary() {
                const total = Object.keys(this.testResults).length;
                const passed = Object.values(this.testResults).filter(r => r.status === 'passed').length;
                const failed = Object.values(this.testResults).filter(r => r.status === 'failed').length;
                
                const summaryElement = document.getElementById('testSummary');
                summaryElement.innerHTML = `
                    <h3>Test Results Summary</h3>
                    <p><strong>Total Tests:</strong> ${total}</p>
                    <p><strong>Passed:</strong> <span style="color: #10b981;">${passed}</span></p>
                    <p><strong>Failed:</strong> <span style="color: #ef4444;">${failed}</span></p>
                    <p><strong>Success Rate:</strong> ${total > 0 ? Math.round((passed / total) * 100) : 0}%</p>
                `;
            }
            
            delay(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            }
        }
        
        // Initialize tester
        const tester = new ClientTester();
        
        // Expose test functions globally
        function runAllTests() { tester.runAllTests(); }
        function testApiHealth() { tester.testApiHealth(); }
        function testInputValidation() { tester.testInputValidation(); }
        function testFormValidation() { tester.testFormValidation(); }
        function testFileUpload() { tester.testFileUpload(); }
        function testSimpleWorkflow() { tester.testSimpleWorkflow(); }
        function testFullWorkflow() { tester.testFullWorkflow(); }
        function testResponseTime() { tester.testResponseTime(); }
        function clearResults() { 
            tester.resetAllTests(); 
            document.getElementById('testSummary').innerHTML = '<p>No tests run yet</p>';
            tester.updateProgress(0, 'Ready to run tests');
        }
    </script>
</body>
</html>

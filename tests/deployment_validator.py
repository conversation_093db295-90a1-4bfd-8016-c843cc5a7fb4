#!/usr/bin/env python3
"""
ROBO-RESEARCHER-2000 Deployment Validator
Validates that all components are properly deployed and configured.
"""

import json
import requests
import subprocess
import os
import sys
import time
from pathlib import Path
from typing import Dict, List, Tuple
import logging

class DeploymentValidator:
    """Validates deployment of ROBO-RESEARCHER-2000 system"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.setup_logging()
        
        self.services = {
            'n8n': {
                'url': 'http://localhost:5678',
                'health_endpoint': '/healthz',
                'required': True
            },
            'minio': {
                'url': 'http://localhost:9000',
                'health_endpoint': '/minio/health/live',
                'required': True
            },
            'minio_console': {
                'url': 'http://localhost:9001',
                'health_endpoint': '/',
                'required': False
            },
            'wikijs': {
                'url': 'http://localhost:3000',
                'health_endpoint': '/healthz',
                'required': True
            },
            'postgres': {
                'url': 'localhost:5432',
                'health_endpoint': None,
                'required': True
            }
        }
        
        self.required_files = [
            'docker-compose.yml',
            'infrastructure/setup.sh',
            'workflows/main-workflow-complete.json',
            'workflows/test-workflow-simple.json',
            'client/index.html',
            'scripts/text_preprocessing.py',
            'scripts/coding_engine.py',
            'scripts/quantitative_analyzer.py',
            'scripts/presentation_builder.py'
        ]
        
        self.required_directories = [
            'infrastructure',
            'workflows',
            'client',
            'scripts',
            'tests'
        ]
        
        self.validation_results = {}
    
    def setup_logging(self):
        """Configure logging"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('tests/deployment_validation.log')
            ]
        )
    
    def validate_deployment(self) -> Dict:
        """Run complete deployment validation"""
        self.logger.info("🚀 Starting ROBO-RESEARCHER-2000 Deployment Validation")
        
        validation_results = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'overall_status': 'UNKNOWN',
            'validations': {}
        }
        
        try:
            # File structure validation
            self.logger.info("📁 Validating file structure...")
            validation_results['validations']['file_structure'] = self.validate_file_structure()
            
            # Docker services validation
            self.logger.info("🐳 Validating Docker services...")
            validation_results['validations']['docker_services'] = self.validate_docker_services()
            
            # Service health validation
            self.logger.info("🏥 Validating service health...")
            validation_results['validations']['service_health'] = self.validate_service_health()
            
            # Configuration validation
            self.logger.info("⚙️ Validating configuration...")
            validation_results['validations']['configuration'] = self.validate_configuration()
            
            # Workflow validation
            self.logger.info("🔄 Validating workflows...")
            validation_results['validations']['workflows'] = self.validate_workflows()
            
            # Client application validation
            self.logger.info("🌐 Validating client application...")
            validation_results['validations']['client_app'] = self.validate_client_application()
            
            # Calculate overall status
            all_critical_passed = all(
                result.get('status') == 'PASSED' 
                for key, result in validation_results['validations'].items()
                if result.get('critical', True)
            )
            
            validation_results['overall_status'] = 'PASSED' if all_critical_passed else 'FAILED'
            
        except Exception as e:
            self.logger.error(f"Deployment validation failed: {e}")
            validation_results['overall_status'] = 'ERROR'
            validation_results['error'] = str(e)
        
        self.logger.info(f"✅ Deployment validation completed: {validation_results['overall_status']}")
        return validation_results
    
    def validate_file_structure(self) -> Dict:
        """Validate required files and directories exist"""
        result = {
            'status': 'UNKNOWN',
            'critical': True,
            'details': {
                'missing_files': [],
                'missing_directories': [],
                'found_files': [],
                'found_directories': []
            }
        }
        
        # Check directories
        for directory in self.required_directories:
            if os.path.exists(directory) and os.path.isdir(directory):
                result['details']['found_directories'].append(directory)
            else:
                result['details']['missing_directories'].append(directory)
        
        # Check files
        for file_path in self.required_files:
            if os.path.exists(file_path) and os.path.isfile(file_path):
                result['details']['found_files'].append(file_path)
            else:
                result['details']['missing_files'].append(file_path)
        
        # Determine status
        has_missing = (len(result['details']['missing_files']) > 0 or 
                      len(result['details']['missing_directories']) > 0)
        result['status'] = 'FAILED' if has_missing else 'PASSED'
        
        return result
    
    def validate_docker_services(self) -> Dict:
        """Validate Docker services are running"""
        result = {
            'status': 'UNKNOWN',
            'critical': True,
            'details': {
                'running_containers': [],
                'missing_containers': [],
                'docker_available': False
            }
        }
        
        try:
            # Check if Docker is available
            subprocess.run(['docker', '--version'], 
                         capture_output=True, check=True, timeout=10)
            result['details']['docker_available'] = True
            
            # Get running containers
            docker_ps = subprocess.run(
                ['docker', 'ps', '--format', '{{.Names}}'],
                capture_output=True, text=True, timeout=10
            )
            
            if docker_ps.returncode == 0:
                running_containers = docker_ps.stdout.strip().split('\n')
                running_containers = [c for c in running_containers if c]
                
                expected_containers = [
                    'robo-researcher-n8n',
                    'robo-researcher-minio',
                    'robo-researcher-postgres',
                    'robo-researcher-wikijs'
                ]
                
                for container in expected_containers:
                    if any(container in running for running in running_containers):
                        result['details']['running_containers'].append(container)
                    else:
                        result['details']['missing_containers'].append(container)
                
                result['status'] = 'PASSED' if len(result['details']['missing_containers']) == 0 else 'FAILED'
            else:
                result['status'] = 'FAILED'
                result['error'] = 'Failed to list Docker containers'
                
        except subprocess.TimeoutExpired:
            result['status'] = 'FAILED'
            result['error'] = 'Docker command timed out'
        except subprocess.CalledProcessError as e:
            result['status'] = 'FAILED'
            result['error'] = f'Docker not available: {e}'
        except Exception as e:
            result['status'] = 'FAILED'
            result['error'] = str(e)
        
        return result
    
    def validate_service_health(self) -> Dict:
        """Validate all services are healthy"""
        result = {
            'status': 'UNKNOWN',
            'critical': True,
            'details': {
                'healthy_services': [],
                'unhealthy_services': [],
                'service_responses': {}
            }
        }
        
        for service_name, service_config in self.services.items():
            if service_config['health_endpoint']:
                health_url = service_config['url'] + service_config['health_endpoint']
                
                try:
                    response = requests.get(health_url, timeout=10)
                    response_time = response.elapsed.total_seconds()
                    
                    service_result = {
                        'status_code': response.status_code,
                        'response_time': response_time,
                        'healthy': response.status_code == 200
                    }
                    
                    result['details']['service_responses'][service_name] = service_result
                    
                    if service_result['healthy']:
                        result['details']['healthy_services'].append(service_name)
                    else:
                        result['details']['unhealthy_services'].append(service_name)
                        
                except Exception as e:
                    result['details']['service_responses'][service_name] = {
                        'error': str(e),
                        'healthy': False
                    }
                    result['details']['unhealthy_services'].append(service_name)
        
        # Check if all required services are healthy
        required_services = [name for name, config in self.services.items() 
                           if config['required'] and config['health_endpoint']]
        unhealthy_required = [name for name in result['details']['unhealthy_services'] 
                            if name in required_services]
        
        result['status'] = 'PASSED' if len(unhealthy_required) == 0 else 'FAILED'
        
        return result
    
    def validate_configuration(self) -> Dict:
        """Validate configuration files and environment"""
        result = {
            'status': 'UNKNOWN',
            'critical': False,
            'details': {
                'config_files': {},
                'environment_vars': {},
                'recommendations': []
            }
        }
        
        # Check configuration files
        config_files = [
            'infrastructure/docker-compose.yml',
            'workflows/config/default-codes.json',
            'workflows/config/presentation-template.md'
        ]
        
        for config_file in config_files:
            if os.path.exists(config_file):
                try:
                    with open(config_file, 'r') as f:
                        content = f.read()
                    result['details']['config_files'][config_file] = {
                        'exists': True,
                        'size': len(content),
                        'valid': len(content) > 0
                    }
                except Exception as e:
                    result['details']['config_files'][config_file] = {
                        'exists': True,
                        'error': str(e),
                        'valid': False
                    }
            else:
                result['details']['config_files'][config_file] = {
                    'exists': False,
                    'valid': False
                }
        
        # Check environment variables
        env_vars = [
            'OPENROUTER_API_KEY',
            'SMTP_HOST',
            'SMTP_USER',
            'SMTP_PASSWORD'
        ]
        
        for env_var in env_vars:
            value = os.getenv(env_var)
            result['details']['environment_vars'][env_var] = {
                'set': value is not None,
                'has_value': bool(value and value.strip())
            }
            
            if not value:
                result['details']['recommendations'].append(
                    f"Set {env_var} environment variable for full functionality"
                )
        
        # Determine status (non-critical, so always pass but with recommendations)
        result['status'] = 'PASSED'
        
        return result
    
    def validate_workflows(self) -> Dict:
        """Validate n8n workflows are properly configured"""
        result = {
            'status': 'UNKNOWN',
            'critical': True,
            'details': {
                'workflow_files': {},
                'n8n_accessible': False,
                'webhooks_accessible': []
            }
        }
        
        # Check workflow files
        workflow_files = [
            'workflows/main-workflow-complete.json',
            'workflows/test-workflow-simple.json'
        ]
        
        for workflow_file in workflow_files:
            if os.path.exists(workflow_file):
                try:
                    with open(workflow_file, 'r') as f:
                        workflow_data = json.load(f)
                    
                    result['details']['workflow_files'][workflow_file] = {
                        'valid_json': True,
                        'has_nodes': 'nodes' in workflow_data,
                        'node_count': len(workflow_data.get('nodes', [])),
                        'has_connections': 'connections' in workflow_data
                    }
                except Exception as e:
                    result['details']['workflow_files'][workflow_file] = {
                        'valid_json': False,
                        'error': str(e)
                    }
            else:
                result['details']['workflow_files'][workflow_file] = {
                    'exists': False
                }
        
        # Test webhook accessibility
        webhook_endpoints = [
            'http://localhost:5678/webhook/test-robo-researcher',
            'http://localhost:5678/webhook/robo-researcher'
        ]
        
        for endpoint in webhook_endpoints:
            try:
                # Try a simple GET request to see if webhook responds
                response = requests.get(endpoint, timeout=5)
                result['details']['webhooks_accessible'].append({
                    'endpoint': endpoint,
                    'accessible': True,
                    'status_code': response.status_code
                })
            except Exception as e:
                result['details']['webhooks_accessible'].append({
                    'endpoint': endpoint,
                    'accessible': False,
                    'error': str(e)
                })
        
        # Check if n8n is accessible
        try:
            response = requests.get('http://localhost:5678', timeout=5)
            result['details']['n8n_accessible'] = response.status_code == 200
        except:
            result['details']['n8n_accessible'] = False
        
        # Determine status
        valid_workflows = all(
            wf.get('valid_json', False) and wf.get('has_nodes', False)
            for wf in result['details']['workflow_files'].values()
        )
        
        result['status'] = 'PASSED' if valid_workflows and result['details']['n8n_accessible'] else 'FAILED'
        
        return result
    
    def validate_client_application(self) -> Dict:
        """Validate client web application"""
        result = {
            'status': 'UNKNOWN',
            'critical': False,
            'details': {
                'html_files': {},
                'css_files': {},
                'js_files': {},
                'assets': {}
            }
        }
        
        # Check HTML files
        html_files = ['client/index.html']
        for html_file in html_files:
            if os.path.exists(html_file):
                with open(html_file, 'r') as f:
                    content = f.read()
                result['details']['html_files'][html_file] = {
                    'exists': True,
                    'size': len(content),
                    'has_form': 'form' in content.lower(),
                    'has_javascript': 'script' in content.lower()
                }
            else:
                result['details']['html_files'][html_file] = {'exists': False}
        
        # Check CSS files
        css_files = ['client/css/styles.css', 'client/css/components.css']
        for css_file in css_files:
            if os.path.exists(css_file):
                with open(css_file, 'r') as f:
                    content = f.read()
                result['details']['css_files'][css_file] = {
                    'exists': True,
                    'size': len(content)
                }
            else:
                result['details']['css_files'][css_file] = {'exists': False}
        
        # Check JavaScript files
        js_files = ['client/js/app.js', 'client/js/api.js', 'client/js/notifications.js']
        for js_file in js_files:
            if os.path.exists(js_file):
                with open(js_file, 'r') as f:
                    content = f.read()
                result['details']['js_files'][js_file] = {
                    'exists': True,
                    'size': len(content),
                    'has_functions': 'function' in content
                }
            else:
                result['details']['js_files'][js_file] = {'exists': False}
        
        # Check assets
        asset_files = ['client/assets/icons/robot.svg']
        for asset_file in asset_files:
            result['details']['assets'][asset_file] = {
                'exists': os.path.exists(asset_file)
            }
        
        # Determine status
        essential_files_exist = (
            result['details']['html_files'].get('client/index.html', {}).get('exists', False) and
            result['details']['js_files'].get('client/js/app.js', {}).get('exists', False)
        )
        
        result['status'] = 'PASSED' if essential_files_exist else 'FAILED'
        
        return result
    
    def generate_report(self, validation_results: Dict) -> str:
        """Generate a comprehensive validation report"""
        report = f"""
# ROBO-RESEARCHER-2000 Deployment Validation Report

**Validation Date:** {validation_results['timestamp']}
**Overall Status:** {validation_results['overall_status']}

## Summary

"""
        
        for validation_name, validation_data in validation_results['validations'].items():
            status_emoji = '✅' if validation_data['status'] == 'PASSED' else '❌'
            critical_text = ' (Critical)' if validation_data.get('critical', True) else ''
            report += f"- **{validation_name.replace('_', ' ').title()}:** {status_emoji} {validation_data['status']}{critical_text}\n"
        
        report += "\n## Detailed Results\n\n"
        
        for validation_name, validation_data in validation_results['validations'].items():
            report += f"### {validation_name.replace('_', ' ').title()}\n\n"
            report += f"**Status:** {validation_data['status']}\n"
            report += f"**Critical:** {'Yes' if validation_data.get('critical', True) else 'No'}\n\n"
            
            if 'details' in validation_data:
                report += "**Details:**\n"
                report += f"```json\n{json.dumps(validation_data['details'], indent=2)}\n```\n\n"
        
        return report

def main():
    """Run deployment validation"""
    validator = DeploymentValidator()
    results = validator.validate_deployment()
    
    # Save results
    with open('tests/deployment_validation_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    # Generate report
    report = validator.generate_report(results)
    with open('tests/deployment_validation_report.md', 'w') as f:
        f.write(report)
    
    print(f"\n{'='*60}")
    print(f"DEPLOYMENT VALIDATION RESULTS: {results['overall_status']}")
    print(f"{'='*60}")
    
    # Print summary
    for validation_name, validation_data in results['validations'].items():
        status_emoji = '✅' if validation_data['status'] == 'PASSED' else '❌'
        print(f"{status_emoji} {validation_name.replace('_', ' ').title()}: {validation_data['status']}")
    
    return 0 if results['overall_status'] == 'PASSED' else 1

if __name__ == '__main__':
    exit(main())

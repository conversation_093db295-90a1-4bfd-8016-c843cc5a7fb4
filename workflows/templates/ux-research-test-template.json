{"name": "ROBO-RESEARCHER-2000 Test Workflow", "nodes": [{"parameters": {"httpMethod": "POST", "path": "test-robo-researcher", "responseMode": "responseNode", "options": {}}, "id": "webhook-test", "name": "Webhook Test", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "test-robo-researcher"}, {"parameters": {"jsCode": "// Test workflow for ROBO-RESEARCHER-2000\n// Validates input and returns test response\n\nconst input = $input.all()[0].json;\n\n// Validate required fields\nconst requiredFields = ['email', 'transcription', 'project_name'];\nconst missingFields = [];\n\nfor (const field of requiredFields) {\n  if (!input[field]) {\n    missingFields.push(field);\n  }\n}\n\nif (missingFields.length > 0) {\n  return {\n    status: 'error',\n    message: `Missing required fields: ${missingFields.join(', ')}`,\n    timestamp: new Date().toISOString()\n  };\n}\n\n// Simulate processing\nconst response = {\n  status: 'success',\n  message: 'Test workflow executed successfully',\n  data: {\n    project_name: input.project_name,\n    email: input.email,\n    transcription_length: input.transcription.length,\n    processing_steps: [\n      { step: 1, name: 'Input Validation', status: 'completed' },\n      { step: 2, name: 'Text Preprocessing', status: 'simulated' },\n      { step: 3, name: 'Basic Analysis', status: 'simulated' }\n    ],\n    estimated_full_processing_time: '15-20 minutes',\n    next_steps: [\n      'Import main workflow',\n      'Configure API credentials',\n      'Run full analysis'\n    ]\n  },\n  timestamp: new Date().toISOString()\n};\n\nreturn response;"}, "id": "test-processor", "name": "Test Processor", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json }}"}, "id": "response", "name": "Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 300]}], "connections": {"Webhook Test": {"main": [[{"node": "Test Processor", "type": "main", "index": 0}]]}, "Test Processor": {"main": [[{"node": "Response", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "test-1.0", "meta": {"templateCredsSetupCompleted": true}, "id": "test-workflow", "tags": [{"createdAt": "2025-01-17T00:00:00.000Z", "updatedAt": "2025-01-17T00:00:00.000Z", "id": "test", "name": "test"}]}
/**
 * Wiki.js Attachment Integration for ROBO-RESEARCHER-2000
 * Handles uploading and linking generated documents to Wiki.js pages
 */

const fs = require('fs');
const path = require('path');
const http = require('http');
const https = require('https');

class WikiAttachmentManager {
    constructor(config = {}) {
        this.wikiUrl = config.wikiUrl || 'http://localhost:3002';
        this.apiToken = config.apiToken || null;
        this.uploadPath = config.uploadPath || '/uploads';
        this.maxFileSize = config.maxFileSize || 50 * 1024 * 1024; // 50MB
        
        this.supportedTypes = {
            'application/pdf': '.pdf',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation': '.pptx',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',
            'application/json': '.json',
            'text/plain': '.txt',
            'image/svg+xml': '.svg',
            'image/png': '.png',
            'image/jpeg': '.jpg'
        };
    }

    /**
     * Upload file to Wiki.js and return attachment URL (simulated for now)
     */
    async uploadAttachment(filePath, fileName, pageId) {
        try {
            // Validate file
            const stats = fs.statSync(filePath);
            if (stats.size > this.maxFileSize) {
                throw new Error(`File size exceeds maximum allowed size (${this.maxFileSize} bytes)`);
            }

            // Simulate upload (in real implementation, would use multipart form data)
            console.log(`Simulating upload of ${fileName} (${this.formatFileSize(stats.size)})`);

            // For now, return simulated success
            return {
                success: true,
                url: `${this.wikiUrl}${this.uploadPath}/${fileName}`,
                fileName: fileName,
                size: stats.size,
                type: this.getMimeType(fileName)
            };

        } catch (error) {
            console.error('Upload error:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Create or update Wiki.js page with attachments
     */
    async createPageWithAttachments(pageData, attachments = []) {
        try {
            const { title, content, path: pagePath, tags = [] } = pageData;
            
            // Generate attachment links
            const attachmentLinks = this.generateAttachmentLinks(attachments);
            
            // Combine content with attachment links
            const fullContent = this.combineContentWithAttachments(content, attachmentLinks);
            
            // Create page via GraphQL
            const mutation = `
                mutation CreatePage($content: String!, $description: String!, $editor: String!, $isPublished: Boolean!, $isPrivate: Boolean!, $locale: String!, $path: String!, $publishEndDate: Date, $publishStartDate: Date, $scriptCss: String, $scriptJs: String, $tags: [String]!, $title: String!) {
                    pages {
                        create(content: $content, description: $description, editor: $editor, isPublished: $isPublished, isPrivate: $isPrivate, locale: $locale, path: $path, publishEndDate: $publishEndDate, publishStartDate: $publishStartDate, scriptCss: $scriptCss, scriptJs: $scriptJs, tags: $tags, title: $title) {
                            responseResult {
                                succeeded
                                errorCode
                                slug
                                message
                            }
                            page {
                                id
                                path
                                title
                            }
                        }
                    }
                }
            `;

            const variables = {
                content: fullContent,
                description: `ROBO-RESEARCHER-2000 Analysis Results for ${title}`,
                editor: 'markdown',
                isPublished: true,
                isPrivate: false,
                locale: 'en',
                path: pagePath,
                tags: ['robo-researcher', 'ux-research', 'analysis', ...tags],
                title: title
            };

            const response = await this.makeGraphQLRequest(mutation, variables);
            
            if (response.data?.pages?.create?.responseResult?.succeeded) {
                return {
                    success: true,
                    pageId: response.data.pages.create.page.id,
                    pageUrl: `${this.wikiUrl}/${pagePath}`,
                    attachments: attachments
                };
            } else {
                throw new Error(response.data?.pages?.create?.responseResult?.message || 'Page creation failed');
            }

        } catch (error) {
            console.error('Page creation error:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Generate markdown links for attachments
     */
    generateAttachmentLinks(attachments) {
        return attachments.map(attachment => {
            const { fileName, url, type, size } = attachment;
            const icon = this.getFileIcon(type);
            const sizeFormatted = this.formatFileSize(size);
            
            return {
                markdown: `${icon} [${fileName}](${url}) *(${sizeFormatted})*`,
                html: `<a href="${url}" class="attachment-link" data-type="${type}">
                    <i class="${icon}"></i> ${fileName} <span class="file-size">(${sizeFormatted})</span>
                </a>`,
                fileName,
                url,
                type,
                size
            };
        });
    }

    /**
     * Combine content with attachment links
     */
    combineContentWithAttachments(content, attachmentLinks) {
        const attachmentSection = attachmentLinks.length > 0 ? `

## 📎 Generated Documents

${attachmentLinks.map(link => link.markdown).join('\n')}

---

` : '';

        return content + attachmentSection;
    }

    /**
     * Upload multiple files from n8n workflow results
     */
    async uploadWorkflowResults(executionId, resultFiles, pageData) {
        const uploadResults = [];
        const attachments = [];

        console.log(`Uploading ${resultFiles.length} files for execution ${executionId}`);

        for (const file of resultFiles) {
            const { filePath, fileName, description } = file;
            
            console.log(`Uploading ${fileName}...`);
            const uploadResult = await this.uploadAttachment(filePath, fileName, pageData.path);
            
            if (uploadResult.success) {
                attachments.push({
                    ...uploadResult,
                    description: description || ''
                });
                console.log(`✅ ${fileName} uploaded successfully`);
            } else {
                console.error(`❌ Failed to upload ${fileName}: ${uploadResult.error}`);
            }
            
            uploadResults.push(uploadResult);
        }

        // Create Wiki page with attachments
        const pageResult = await this.createPageWithAttachments(pageData, attachments);
        
        return {
            executionId,
            uploads: uploadResults,
            page: pageResult,
            attachments: attachments
        };
    }

    /**
     * Get MIME type from file extension
     */
    getMimeType(fileName) {
        const ext = path.extname(fileName).toLowerCase();
        const mimeTypes = {
            '.pdf': 'application/pdf',
            '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            '.json': 'application/json',
            '.txt': 'text/plain',
            '.svg': 'image/svg+xml',
            '.png': 'image/png',
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg'
        };
        return mimeTypes[ext] || 'application/octet-stream';
    }

    /**
     * Get Font Awesome icon for file type
     */
    getFileIcon(mimeType) {
        const icons = {
            'application/pdf': 'fas fa-file-pdf',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'fas fa-file-powerpoint',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'fas fa-file-word',
            'application/json': 'fas fa-file-code',
            'text/plain': 'fas fa-file-alt',
            'image/svg+xml': 'fas fa-file-image',
            'image/png': 'fas fa-file-image',
            'image/jpeg': 'fas fa-file-image'
        };
        return icons[mimeType] || 'fas fa-file';
    }

    /**
     * Format file size for display
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * Make GraphQL request to Wiki.js
     */
    async makeGraphQLRequest(query, variables = {}) {
        return new Promise((resolve, reject) => {
            const postData = JSON.stringify({ query, variables });
            const url = new URL(`${this.wikiUrl}/graphql`);

            const options = {
                hostname: url.hostname,
                port: url.port || (url.protocol === 'https:' ? 443 : 80),
                path: url.pathname,
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Content-Length': Buffer.byteLength(postData),
                    'Authorization': `Bearer ${this.apiToken}`
                }
            };

            const req = (url.protocol === 'https:' ? https : http).request(options, (res) => {
                let data = '';
                res.on('data', chunk => data += chunk);
                res.on('end', () => {
                    try {
                        resolve(JSON.parse(data));
                    } catch (error) {
                        reject(new Error(`Invalid JSON response: ${error.message}`));
                    }
                });
            });

            req.on('error', reject);
            req.write(postData);
            req.end();
        });
    }

    /**
     * Generate sample page content with analysis results
     */
    generateAnalysisPageContent(analysisData) {
        const { projectName, executionId, insights, segments, codes } = analysisData;
        
        return `# ${projectName} - UX Research Analysis

**Execution ID:** \`${executionId}\`  
**Generated:** ${new Date().toISOString()}  
**Analysis Type:** Automated UX Research  

## 📊 Executive Summary

This analysis was generated by ROBO-RESEARCHER-2000, processing user interview transcriptions through a 17-step automated workflow.

### Key Insights
${insights ? insights.map(insight => `- ${insight}`).join('\n') : '- Analysis in progress...'}

## 🔍 Analysis Details

### Segmentation Results
- **Total Segments:** ${segments?.length || 0}
- **Topics Identified:** ${segments ? [...new Set(segments.map(s => s.topic))].length : 0}

### Coding Framework
- **Deductive Codes Applied:** ${codes?.deductive?.length || 0}
- **Emergent Codes Identified:** ${codes?.emergent?.length || 0}

## 📈 Methodology

This analysis follows a comprehensive 17-step workflow:

1. **Input Validation** - Data quality checks
2. **Text Preprocessing** - Cleaning and normalization
3. **Segmentation** - Topic-based text division
4. **Deductive Coding** - Predefined framework application
5. **Open Coding** - AI-generated emergent themes
6. **Category Grouping** - Thematic organization
7. **Affinity Mapping** - Pattern identification
8. **Quantitative Analysis** - Statistical insights
9. **Pattern Detection** - Recurring theme analysis
10. **Insight Generation** - Actionable recommendations
11. **Archetype Creation** - User persona development
12. **HMW Generation** - Opportunity identification
13. **Opportunity Prioritization** - Impact assessment
14. **Presentation Generation** - Report creation
15. **Documentation** - Comprehensive documentation
16. **Quality Assurance** - Validation and review
17. **Delivery** - Multi-format output generation

## 🎯 Next Steps

1. Review the generated presentation and summary documents
2. Validate insights with stakeholders
3. Prioritize identified opportunities
4. Plan implementation roadmap

---

*Generated by ROBO-RESEARCHER-2000 - Automated UX Research Analysis System*`;
    }
}

module.exports = WikiAttachmentManager;

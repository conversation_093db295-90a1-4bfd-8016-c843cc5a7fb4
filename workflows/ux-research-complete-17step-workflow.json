{"name": "ROBO-RESEARCHER-2000 Complete 17-Step Workflow", "nodes": [{"parameters": {"httpMethod": "POST", "path": "robo-researcher", "responseMode": "responseNode", "options": {"allowedOrigins": "*"}}, "id": "webhook-trigger", "name": "1. <PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [240, 300], "webhookId": "robo-researcher", "notes": "**PURPOSE**: Entry point for the UX research workflow. Receives HTTP POST requests containing user research data including project details, transcriptions, and configuration parameters.\n\n**IMPLEMENTATION**: Standard n8n Webhook node configured for POST requests. Uses 'responseNode' mode to send response after workflow completion.\n\n**CONFIGURATION**:\n- HTTP Method: POST (for secure data transmission)\n- Path: 'robo-researcher' (creates endpoint /webhook/robo-researcher)\n- Response Mode: responseNode (waits for workflow completion)\n- CORS: Enabled with '*' for cross-origin requests\n\n**EXPECTED INPUT**: JSON payload with:\n- projectName (string): Research project identifier\n- email (string): Notification email address\n- transcription (string): Raw interview/research text\n- studyType (optional): Type of UX study\n- language (optional): Content language code\n\n**OUTPUT**: Passes received data to validation step\n\n**AI AGENT INTEGRATION**: Could be enhanced with AI Agent for intelligent request routing and preprocessing based on study type and content analysis."}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "project-name-check", "leftValue": "={{ $json.projectName }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}, {"id": "email-check", "leftValue": "={{ $json.email }}", "rightValue": "^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$", "operator": {"type": "string", "operation": "regex"}}, {"id": "transcription-check", "leftValue": "={{ $json.transcription?.length }}", "rightValue": 100, "operator": {"type": "number", "operation": "gte"}}], "combinator": "and"}, "options": {}}, "id": "validate-input", "name": "2. Validate Input", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 300], "notes": "**PURPOSE**: Validates incoming request data to ensure all required fields are present and properly formatted before processing begins.\n\n**IMPLEMENTATION**: Standard n8n IF node with multiple validation conditions using AND combinator for strict validation.\n\n**VALIDATION RULES**:\n1. Project Name: Must be non-empty string\n2. Email: Must match valid email regex pattern\n3. Transcription: Must be at least 100 characters long\n\n**CONFIGURATION**:\n- Case Sensitive: true (strict string matching)\n- Type Validation: strict (enforces data types)\n- Combinator: AND (all conditions must pass)\n\n**DATA FLOW**:\n- Input: Raw webhook payload\n- Success Path: Continues to data preparation\n- Failure Path: Should connect to error handling (currently missing)\n\n**AI AGENT INTEGRATION**: Could be enhanced with AI Agent for intelligent validation including:\n- Content quality assessment\n- Language detection and validation\n- Automatic data cleaning suggestions\n- Smart error messaging with correction hints\n\n**RECOMMENDED ENHANCEMENT**: Add error handling branch for validation failures with detailed error messages."}, {"parameters": {"assignments": {"assignments": [{"id": "execution-id", "name": "executionId", "value": "=exec_{{ $now.format('YYYYMMDD_HHmmss') }}", "type": "string"}, {"id": "project-name", "name": "projectName", "value": "={{ $json.projectName }}", "type": "string"}, {"id": "email", "name": "email", "value": "={{ $json.email }}", "type": "string"}, {"id": "transcription", "name": "transcription", "value": "={{ $json.transcription }}", "type": "string"}, {"id": "study-type", "name": "studyType", "value": "={{ $json.studyType || 'user_interview' }}", "type": "string"}, {"id": "language", "name": "language", "value": "={{ $json.language || 'en' }}", "type": "string"}, {"id": "timestamp", "name": "timestamp", "value": "={{ $now.toISO() }}", "type": "string"}, {"id": "step", "name": "step", "value": "validation_complete", "type": "string"}]}, "options": {}}, "id": "prepare-data", "name": "2b. Prepare Data", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [680, 240], "notes": "**PURPOSE**: Normalizes and enriches validated input data with execution metadata, default values, and tracking information for the analysis pipeline.\n\n**IMPLEMENTATION**: Standard n8n Set node that creates a standardized data structure with all required fields and metadata.\n\n**DATA TRANSFORMATIONS**:\n- executionId: Unique identifier with timestamp format 'exec_YYYYMMDD_HHmmss'\n- projectName: Direct pass-through from input\n- email: Direct pass-through from input\n- transcription: Direct pass-through from input\n- studyType: Defaults to 'user_interview' if not provided\n- language: Defaults to 'en' if not provided\n- timestamp: ISO format timestamp for execution tracking\n- step: Progress marker set to 'validation_complete'\n\n**CONFIGURATION**:\n- Uses n8n expressions for dynamic value generation\n- Implements fallback values for optional parameters\n- Creates audit trail with execution metadata\n\n**DATA FLOW**:\n- Input: Validated webhook data\n- Output: Normalized data structure for downstream processing\n\n**AI AGENT INTEGRATION**: Could be enhanced with AI Agent for:\n- Intelligent study type detection based on transcription content\n- Automatic language detection using content analysis\n- Content quality scoring and recommendations\n- Metadata enrichment with content insights"}, {"parameters": {"operation": "upload", "bucketName": "={{ $vars.MINIO_BUCKET || 'robo-researcher-data' }}", "fileName": "={{ $json.executionId }}/original-transcription.txt", "binaryData": false, "fileContent": "={{ $json.transcription }}"}, "id": "upload-to-minio", "name": "3. Upload to MinIO", "type": "n8n-nodes-base.s3", "typeVersion": 1, "position": [900, 240], "credentials": {"s3": {"id": "minio-s3-credentials", "name": "MinIO S3 Storage"}}, "notes": "**PURPOSE**: Archives original transcription data to object storage for audit trail, backup, and potential reprocessing needs.\n\n**IMPLEMENTATION**: Standard n8n S3 node configured for MinIO object storage with execution-specific folder structure.\n\n**CONFIGURATION**:\n- Operation: upload (stores file content)\n- Bucket: Uses environment variable MINIO_BUCKET or defaults to 'robo-researcher-data'\n- File Path: '/{executionId}/original-transcription.txt' for organized storage\n- Content Type: Plain text file\n- Binary Data: false (text content)\n\n**STORAGE STRUCTURE**:\n```\nrobo-researcher-data/\n├── exec_20250123_143022/\n│   ├── original-transcription.txt\n│   ├── processed-data.json (future)\n│   └── analysis-results.json (future)\n```\n\n**CREDENTIALS**: Requires MinIO S3 credentials with:\n- Access Key ID\n- Secret Access Key\n- Endpoint URL\n- Region (optional)\n\n**DATA FLOW**:\n- Input: Prepared data with transcription\n- Output: File upload confirmation with storage metadata\n\n**AI AGENT INTEGRATION**: Could be enhanced with AI Agent for:\n- Intelligent file naming based on content analysis\n- Automatic content categorization and tagging\n- Duplicate detection and deduplication\n- Storage optimization recommendations\n\n**RECOMMENDED ENHANCEMENT**: Add error handling for storage failures and retry logic."}, {"parameters": {"assignments": {"assignments": [{"id": "cleaned-text", "name": "cleanedTranscription", "value": "={{ $json.transcription.replace(/\\[.*?\\]/g, '').replace(/\\s+/g, ' ').trim() }}", "type": "string"}, {"id": "word-count", "name": "wordCount", "value": "={{ $json.transcription.split(' ').length }}", "type": "number"}, {"id": "preprocessing-complete", "name": "step", "value": "preprocessing_complete", "type": "string"}]}, "options": {}}, "id": "text-preprocessing", "name": "4. Text Preprocessing", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1120, 240], "notes": "**PURPOSE**: Cleans and normalizes raw transcription text by removing artifacts, standardizing whitespace, and calculating basic metrics for analysis preparation.\n\n**IMPLEMENTATION**: Standard n8n Set node using regex expressions for text cleaning and JavaScript methods for metrics calculation.\n\n**TEXT CLEANING OPERATIONS**:\n1. Remove bracketed annotations: `/\\[.*?\\]/g` (removes [speaker], [pause], etc.)\n2. Normalize whitespace: `/\\s+/g` (converts multiple spaces/tabs/newlines to single space)\n3. Trim edges: `.trim()` (removes leading/trailing whitespace)\n4. Word count calculation: `.split(' ').length`\n\n**CONFIGURATION**:\n- cleanedTranscription: Processed text ready for analysis\n- wordCount: Total word count for content assessment\n- step: Progress marker set to 'preprocessing_complete'\n\n**DATA FLOW**:\n- Input: Raw transcription with potential artifacts\n- Output: Clean text suitable for NLP analysis\n\n**AI AGENT INTEGRATION**: Could be enhanced with AI Agent for advanced preprocessing:\n- Intelligent speaker identification and separation\n- Automatic correction of transcription errors\n- Sentiment-aware text normalization\n- Language-specific preprocessing rules\n- Content quality assessment and recommendations\n\n**RECOMMENDED ENHANCEMENT**: Replace with Basic LLM Chain node for AI-powered text cleaning:\n```\nPrompt: 'Clean and normalize this transcription for UX research analysis. Remove artifacts, fix obvious errors, but preserve meaning and emotional context: {transcription}'\n```"}, {"parameters": {"assignments": {"assignments": [{"id": "segments", "name": "segments", "value": "={{ $json.cleanedTranscription.split(/\\n\\n|\\. (?=[A-Z])/).map((segment, index) => ({ id: index + 1, text: segment.trim(), wordCount: segment.split(' ').length })).filter(seg => seg.text.length > 50) }}", "type": "object"}, {"id": "segment-count", "name": "segmentCount", "value": "={{ $json.segments?.length || 0 }}", "type": "number"}, {"id": "segmentation-complete", "name": "step", "value": "segmentation_complete", "type": "string"}]}, "options": {}}, "id": "segmentation", "name": "5. Segmentation", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1340, 240], "notes": "**PURPOSE**: Divides cleaned transcription into meaningful segments for granular analysis, enabling topic-based coding and pattern identification.\n\n**IMPLEMENTATION**: Standard n8n Set node using regex patterns to identify natural text boundaries and create structured segment objects.\n\n**SEGMENTATION LOGIC**:\n1. Split on double newlines: `\\n\\n` (paragraph breaks)\n2. Split on sentence boundaries: `\\. (?=[A-Z])` (period followed by capital letter)\n3. Create segment objects with: id, text, wordCount\n4. Filter segments: minimum 50 characters (removes fragments)\n\n**SEGMENT STRUCTURE**:\n```javascript\n{\n  id: 1,\n  text: \"User mentioned difficulty with navigation...\",\n  wordCount: 25\n}\n```\n\n**CONFIGURATION**:\n- segments: Array of segment objects\n- segmentCount: Total number of valid segments\n- step: Progress marker set to 'segmentation_complete'\n\n**DATA FLOW**:\n- Input: Cleaned transcription text\n- Output: Structured segments ready for coding\n\n**AI AGENT INTEGRATION**: Could be enhanced with AI Agent for intelligent segmentation:\n- Topic-based boundary detection\n- Speaker change identification\n- Semantic coherence analysis\n- Context-aware segment optimization\n\n**RECOMMENDED ENHANCEMENT**: Replace with Basic LLM Chain node for AI-powered segmentation:\n```\nPrompt: 'Segment this UX research transcription into meaningful units based on topics, speakers, or natural conversation flow. Return JSON array of segments: {cleanedTranscription}'\n```"}, {"parameters": {"assignments": {"assignments": [{"id": "deductive-codes", "name": "deductiveCodes", "value": "={{ $json.segments.map(segment => ({ segmentId: segment.id, codes: ['usability', 'navigation', 'feature_request', 'satisfaction', 'frustration'].filter(code => segment.text.toLowerCase().includes(code.replace('_', ' '))) })) }}", "type": "object"}, {"id": "coded-segments", "name": "codedSegments", "value": "={{ $json.segments.map((segment, index) => ({ ...segment, deductiveCodes: $json.deductiveCodes[index]?.codes || [] })) }}", "type": "object"}, {"id": "deductive-coding-complete", "name": "step", "value": "deductive_coding_complete", "type": "string"}]}, "options": {}}, "id": "deductive-coding", "name": "6. Deductive Coding", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1560, 240], "notes": "**PURPOSE**: Applies predefined UX research codes to segments using keyword matching to identify standard usability themes and user experience patterns.\n\n**IMPLEMENTATION**: Standard n8n Set node using JavaScript array methods and string matching to apply predetermined coding schema.\n\n**DEDUCTIVE CODES APPLIED**:\n- 'usability': General usability issues\n- 'navigation': Navigation-related problems\n- 'feature_request': User-requested features\n- 'satisfaction': Positive user experiences\n- 'frustration': Negative user experiences\n\n**CODING LOGIC**:\n1. For each segment, check if text contains code keywords\n2. Use case-insensitive matching with underscore-to-space conversion\n3. Create mapping of segmentId to applicable codes\n4. Merge codes back into segment objects\n\n**CODED SEGMENT STRUCTURE**:\n```javascript\n{\n  id: 1,\n  text: \"Navigation is confusing and frustrating\",\n  wordCount: 5,\n  deductiveCodes: ['navigation', 'frustration']\n}\n```\n\n**DATA FLOW**:\n- Input: Segmented transcription\n- Output: Segments with applied deductive codes\n\n**AI AGENT INTEGRATION**: Should be replaced with AI Agent for intelligent coding:\n- Semantic understanding beyond keyword matching\n- Context-aware code application\n- Confidence scoring for code assignments\n- Custom code suggestions based on content\n\n**RECOMMENDED ENHANCEMENT**: Replace with Basic LLM Chain node:\n```\nPrompt: 'Apply UX research codes to these segments. Use standard codes: usability, navigation, feature_request, satisfaction, frustration. Add confidence scores: {segments}'\n```"}, {"parameters": {"model": "gpt-4o-mini", "messages": {"values": [{"content": "=Analyze the following user research segments and identify emergent themes and codes. Focus on patterns not captured by standard UX codes.\n\nSegments to analyze:\n{{ JSON.stringify($json.codedSegments.slice(0, 10), null, 2) }}\n\nFor each segment, identify:\n1. Emergent themes not covered by existing codes\n2. Specific user behaviors mentioned\n3. Emotional indicators\n4. Context-specific issues\n\nReturn a JSON array of emergent codes with format:\n[{\"code\": \"code_name\", \"description\": \"what this represents\", \"segments\": [segment_ids], \"frequency\": number}]"}]}, "options": {"temperature": 0.3, "maxTokens": 2000}}, "id": "open-coding-ai", "name": "7. Open Coding AI", "type": "n8n-nodes-base.openAi", "typeVersion": 1.4, "position": [1780, 240], "credentials": {"openAiApi": {"id": "openrouter-api", "name": "OpenRouter API"}}, "notes": "**PURPOSE**: Discovers emergent themes and patterns in user research data that aren't captured by predefined deductive codes, enabling comprehensive qualitative analysis.\n\n**IMPLEMENTATION**: OpenAI node configured for emergent coding analysis with structured JSON output format.\n\n**AI ANALYSIS FOCUS**:\n1. Emergent themes beyond standard UX codes\n2. Specific user behaviors and interaction patterns\n3. Emotional indicators and sentiment patterns\n4. Context-specific issues unique to the product/domain\n\n**CONFIGURATION**:\n- Model: gpt-4o-mini (cost-effective for coding tasks)\n- Temperature: 0.3 (balanced creativity and consistency)\n- Max Tokens: 2000 (sufficient for detailed coding output)\n- Input: First 10 coded segments (to manage token limits)\n\n**OUTPUT FORMAT**:\n```javascript\n[{\n  \"code\": \"mobile_context_switching\",\n  \"description\": \"Users struggle when switching between mobile and desktop contexts\",\n  \"segments\": [1, 3, 7],\n  \"frequency\": 3\n}]\n```\n\n**DATA FLOW**:\n- Input: Segments with deductive codes applied\n- Output: Array of emergent codes with metadata\n\n**AI AGENT INTEGRATION**: Could be enhanced with AI Agent for:\n- Multi-model consensus coding\n- Iterative refinement of emergent codes\n- Cross-validation with domain expertise\n- Dynamic code evolution based on new patterns\n\n**RECOMMENDED ENHANCEMENT**: Replace with Basic LLM Chain node for better integration with n8n's AI ecosystem and potential MCP tool usage for specialized UX research methodologies."}, {"parameters": {"assignments": {"assignments": [{"id": "emergent-codes", "name": "emergentCodes", "value": "={{ JSON.parse($json.choices[0].message.content) }}", "type": "object"}, {"id": "all-codes", "name": "allCodes", "value": "={{ $('6. Deductive Coding').item.json.codedSegments.concat($json.emergentCodes || []) }}", "type": "object"}, {"id": "open-coding-complete", "name": "step", "value": "open_coding_complete", "type": "string"}]}, "options": {}}, "id": "merge-codes", "name": "7b. Merge Codes", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2000, 240], "notes": "**PURPOSE**: Combines deductive codes (predefined) with emergent codes (AI-discovered) to create a comprehensive coding schema for downstream analysis.\n\n**IMPLEMENTATION**: Standard n8n Set node that parses AI output and merges coding results from multiple sources.\n\n**DATA INTEGRATION**:\n1. Parse emergent codes from AI JSON response\n2. Retrieve coded segments from deductive coding step\n3. Concatenate both code sets into unified structure\n4. Update progress tracking\n\n**MERGED DATA STRUCTURE**:\n- emergentCodes: AI-discovered codes with metadata\n- allCodes: Combined deductive + emergent codes\n- step: Progress marker 'open_coding_complete'\n\n**ERROR HANDLING**:\n- JSON.parse() may fail if AI returns malformed JSON\n- Fallback to empty array for emergentCodes\n- Node reference $('6. Deductive Coding') assumes specific node naming\n\n**DATA FLOW**:\n- Input: AI-generated emergent codes + reference to deductive codes\n- Output: Unified coding schema for analysis\n\n**AI AGENT INTEGRATION**: Could be enhanced with AI Agent for:\n- Intelligent code deduplication and merging\n- Code hierarchy optimization\n- Quality assessment of merged coding schema\n- Automatic code relationship mapping\n\n**RECOMMENDED ENHANCEMENT**: Add error handling for JSON parsing and implement code validation to ensure data quality before proceeding to analysis phases."}, {"parameters": {"model": "gpt-4o-mini", "messages": {"values": [{"content": "=Organize the following codes into hierarchical categories for UX research analysis.\n\nCodes to categorize:\n{{ JSON.stringify($json.allCodes, null, 2) }}\n\nCreate categories with subcategories following this structure:\n{\n  \"user_experience\": {\n    \"name\": \"User Experience\",\n    \"subcategories\": {\n      \"usability\": {\"name\": \"Usability Issues\", \"codes\": []},\n      \"satisfaction\": {\"name\": \"User Satisfaction\", \"codes\": []}\n    }\n  },\n  \"product_features\": {\n    \"name\": \"Product Features\",\n    \"subcategories\": {\n      \"functionality\": {\"name\": \"Feature Functionality\", \"codes\": []},\n      \"requests\": {\"name\": \"Feature Requests\", \"codes\": []}\n    }\n  }\n}\n\nReturn organized categories with codes properly assigned."}]}, "options": {"temperature": 0.2, "maxTokens": 3000}}, "id": "category-grouping", "name": "8. Category Grouping", "type": "n8n-nodes-base.openAi", "typeVersion": 1.4, "position": [2220, 240], "credentials": {"openAiApi": {"id": "openrouter-api", "name": "OpenRouter API"}}, "notes": "**PURPOSE**: Organizes merged codes into hierarchical categories to create structured taxonomy for systematic UX research analysis and reporting.\n\n**IMPLEMENTATION**: OpenAI node configured for taxonomic organization with predefined category structure template.\n\n**CATEGORIZATION APPROACH**:\n- Hierarchical structure: Categories → Subcategories → Codes\n- Predefined framework with flexibility for emergent categories\n- Systematic organization for consistent analysis\n\n**STANDARD CATEGORIES**:\n1. **User Experience**: Usability, satisfaction, accessibility\n2. **Product Features**: Functionality, requests, performance\n3. **Additional categories**: Dynamically created based on emergent codes\n\n**CONFIGURATION**:\n- Model: gpt-4o-mini (efficient for classification tasks)\n- Temperature: 0.2 (low for consistent categorization)\n- Max <PERSON>ken<PERSON>: 3000 (sufficient for detailed taxonomy)\n\n**OUTPUT STRUCTURE**:\n```javascript\n{\n  \"user_experience\": {\n    \"name\": \"User Experience\",\n    \"subcategories\": {\n      \"usability\": {\n        \"name\": \"Usability Issues\",\n        \"codes\": [\"navigation\", \"confusion\", \"error_recovery\"]\n      }\n    }\n  }\n}\n```\n\n**DATA FLOW**:\n- Input: All codes (deductive + emergent)\n- Output: Hierarchically organized code taxonomy\n\n**AI AGENT INTEGRATION**: Could be enhanced with AI Agent for:\n- Dynamic category creation based on domain expertise\n- Multi-perspective categorization validation\n- Category relationship mapping\n- Taxonomy optimization for specific research goals\n\n**RECOMMENDED ENHANCEMENT**: Replace with Basic LLM Chain node and consider MCP tools for specialized UX research taxonomies."}, {"parameters": {"model": "gpt-4o-mini", "messages": {"values": [{"content": "=Create an affinity map from the categorized codes to identify relationships and clusters.\n\nCategorized Codes:\n{{ JSON.stringify($json.choices[0].message.content, null, 2) }}\n\nGenerate an affinity map with:\n1. Clusters of related themes\n2. Relationships between clusters (strength: weak/moderate/strong)\n3. Insights about theme connections\n\nReturn JSON format:\n{\n  \"clusters\": [{\"id\": \"cluster_1\", \"name\": \"Cluster Name\", \"themes\": [], \"strength\": 0.8}],\n  \"relationships\": [{\"from\": \"cluster_1\", \"to\": \"cluster_2\", \"strength\": 0.6, \"type\": \"moderate\"}],\n  \"insights\": [{\"type\": \"connection\", \"description\": \"insight description\"}]\n}"}]}, "options": {"temperature": 0.3, "maxTokens": 3000}}, "id": "affinity-mapping", "name": "9. Affinity Mapping", "type": "n8n-nodes-base.openAi", "typeVersion": 1.4, "position": [2440, 240], "credentials": {"openAiApi": {"id": "openrouter-api", "name": "OpenRouter API"}}, "notes": "**PURPOSE**: Creates affinity maps to visualize relationships between categorized themes, identifying clusters and connections that reveal deeper insights about user experience patterns.\n\n**IMPLEMENTATION**: OpenAI node configured for relationship analysis and cluster identification with structured output for visualization.\n\n**AFFINITY MAPPING PROCESS**:\n1. **Cluster Identification**: Groups related themes based on semantic similarity\n2. **Relationship Mapping**: Identifies connections between clusters with strength ratings\n3. **Insight Generation**: Discovers patterns and connections not immediately obvious\n\n**CONFIGURATION**:\n- Model: gpt-4o-mini (good for pattern recognition)\n- Temperature: 0.3 (balanced creativity for relationship discovery)\n- Max Tokens: 3000 (sufficient for complex relationship mapping)\n\n**OUTPUT STRUCTURE**:\n```javascript\n{\n  \"clusters\": [{\n    \"id\": \"navigation_issues\",\n    \"name\": \"Navigation Problems\",\n    \"themes\": [\"menu_confusion\", \"search_difficulty\"],\n    \"strength\": 0.8\n  }],\n  \"relationships\": [{\n    \"from\": \"navigation_issues\",\n    \"to\": \"user_frustration\",\n    \"strength\": 0.9,\n    \"type\": \"strong\"\n  }],\n  \"insights\": [{\n    \"type\": \"connection\",\n    \"description\": \"Navigation issues strongly correlate with user frustration\"\n  }]\n}\n```\n\n**DATA FLOW**:\n- Input: Categorized code taxonomy\n- Output: Affinity map with clusters and relationships\n\n**AI AGENT INTEGRATION**: Ideal candidate for AI Agent with MCP tools for:\n- Advanced graph analysis algorithms\n- Interactive affinity mapping tools\n- Statistical correlation analysis\n- Visual relationship mapping\n\n**RECOMMENDED ENHANCEMENT**: Replace with AI Agent connected to MCP Client Tool for specialized affinity mapping algorithms and visualization tools."}, {"parameters": {"assignments": {"assignments": [{"id": "code-frequency", "name": "codeFrequency", "value": "={{ Object.fromEntries($json.allCodes.map(code => [code.code || 'unknown', ($json.allCodes.filter(c => c.code === code.code).length)])) }}", "type": "object"}, {"id": "total-codes", "name": "totalCodes", "value": "={{ $json.allCodes.length }}", "type": "number"}, {"id": "unique-codes", "name": "uniqueCodes", "value": "={{ Object.keys($json.codeFrequency).length }}", "type": "number"}, {"id": "most-frequent", "name": "mostFrequentCode", "value": "={{ Object.entries($json.codeFrequency).sort(([,a], [,b]) => b - a)[0] }}", "type": "object"}, {"id": "quantitative-complete", "name": "step", "value": "quantitative_analysis_complete", "type": "string"}]}, "options": {}}, "id": "quantitative-analysis", "name": "10. Quantitative Analysis", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2660, 240], "notes": "**PURPOSE**: Generates quantitative metrics from qualitative coding data to provide statistical insights and identify the most prevalent themes in the research.\n\n**IMPLEMENTATION**: Standard n8n Set node using JavaScript array and object methods for statistical calculations.\n\n**CALCULATED METRICS**:\n1. **Code Frequency**: Count of each unique code occurrence\n2. **Total Codes**: Overall number of code applications\n3. **Unique Codes**: Number of distinct codes identified\n4. **Most Frequent Code**: Top-occurring code with frequency count\n\n**STATISTICAL OPERATIONS**:\n- Frequency mapping: `Object.fromEntries()` with filtering\n- Sorting: Descending order by frequency\n- Aggregation: Total and unique counts\n\n**OUTPUT STRUCTURE**:\n```javascript\n{\n  codeFrequency: {\n    \"navigation\": 15,\n    \"frustration\": 12,\n    \"feature_request\": 8\n  },\n  totalCodes: 35,\n  uniqueCodes: 3,\n  mostFrequentCode: [\"navigation\", 15]\n}\n```\n\n**DATA FLOW**:\n- Input: All codes from merged coding results\n- Output: Statistical summary for pattern analysis\n\n**LIMITATIONS**:\n- Simple frequency counting (no statistical significance testing)\n- No correlation analysis between codes\n- Missing confidence intervals or error margins\n\n**AI AGENT INTEGRATION**: Could be enhanced with AI Agent for:\n- Advanced statistical analysis (chi-square, correlation)\n- Trend identification and significance testing\n- Comparative analysis across user segments\n- Predictive modeling based on code patterns\n\n**RECOMMENDED ENHANCEMENT**: Add more sophisticated statistical measures and consider AI Agent for advanced analytics with MCP tools for statistical computing."}, {"parameters": {"model": "gpt-4o-mini", "messages": {"values": [{"content": "=Analyze the data to detect behavioral patterns and user journey insights.\n\nQuantitative Analysis:\n{{ JSON.stringify($json, null, 2) }}\n\nAffinity Map:\n{{ JSON.stringify($('9. Affinity Mapping').item.json.choices[0].message.content, null, 2) }}\n\nIdentify:\n1. Behavioral patterns in user interactions\n2. User journey pain points\n3. Recurring themes across segments\n4. Emotional journey patterns\n\nFor each pattern provide:\n- Pattern name and description\n- Supporting evidence\n- Frequency/prevalence\n- Impact assessment (high/medium/low)\n- Recommended actions\n\nReturn JSON array of detected patterns."}]}, "options": {"temperature": 0.3, "maxTokens": 4000}}, "id": "pattern-detection", "name": "11. <PERSON><PERSON> Detection", "type": "n8n-nodes-base.openAi", "typeVersion": 1.4, "position": [2880, 240], "credentials": {"openAiApi": {"id": "openrouter-api", "name": "OpenRouter API"}}, "notes": "**PURPOSE**: Synthesizes quantitative metrics and affinity mapping results to identify behavioral patterns, user journey insights, and recurring themes across the research data.\n\n**IMPLEMENTATION**: OpenAI node configured for pattern analysis combining statistical data with relationship mapping for comprehensive insight discovery.\n\n**PATTERN ANALYSIS FOCUS**:\n1. **Behavioral Patterns**: How users interact with the product\n2. **User Journey Pain Points**: Friction points in user workflows\n3. **Recurring Themes**: Consistent issues across segments\n4. **Emotional Journey**: User sentiment patterns throughout experience\n\n**CONFIGURATION**:\n- Model: gpt-4o-mini (effective for pattern recognition)\n- Temperature: 0.3 (balanced for creative pattern discovery)\n- Max Tokens: 4000 (sufficient for detailed pattern analysis)\n\n**INPUT SYNTHESIS**:\n- Quantitative metrics (frequencies, statistics)\n- Affinity map relationships and clusters\n- Cross-references between data sources\n\n**OUTPUT STRUCTURE**:\n```javascript\n[{\n  \"pattern\": \"Mobile Abandonment Cascade\",\n  \"description\": \"Users consistently abandon tasks on mobile after navigation failures\",\n  \"evidence\": [\"navigation: 15 occurrences\", \"mobile_context: 8 occurrences\"],\n  \"frequency\": \"high\",\n  \"impact\": \"high\",\n  \"recommendations\": [\"Redesign mobile navigation\", \"Add progress indicators\"]\n}]\n```\n\n**DATA FLOW**:\n- Input: Quantitative analysis + affinity mapping results\n- Output: Structured patterns with actionable insights\n\n**AI AGENT INTEGRATION**: Excellent candidate for AI Agent with MCP tools for:\n- Advanced pattern recognition algorithms\n- Statistical significance testing\n- Cross-validation with external research databases\n- Predictive modeling for user behavior\n\n**RECOMMENDED ENHANCEMENT**: Replace with AI Agent connected to MCP Client Tool for specialized UX research pattern detection methodologies."}, {"parameters": {"model": "gpt-4o-mini", "messages": {"values": [{"content": "=Generate structured UX insights from the comprehensive analysis.\n\nDetected Patterns:\n{{ JSON.stringify($json.choices[0].message.content, null, 2) }}\n\nQuantitative Data:\n{{ JSON.stringify($('10. Quantitative Analysis').item.json, null, 2) }}\n\nGenerate actionable insights with:\n1. Key Findings (3-5 main discoveries)\n2. User Pain Points (prioritized with severity)\n3. Opportunities (specific improvement areas)\n4. Recommendations (actionable next steps)\n5. Success Metrics (measurement criteria)\n\nFor each insight include:\n- Clear description\n- Supporting evidence\n- Business impact assessment\n- Implementation complexity (low/medium/high)\n- Priority level (critical/high/medium/low)\n\nReturn structured JSON with insights."}]}, "options": {"temperature": 0.2, "maxTokens": 4000}}, "id": "insight-generation", "name": "12. Insight Generation", "type": "n8n-nodes-base.openAi", "typeVersion": 1.4, "position": [3100, 240], "credentials": {"openAiApi": {"id": "openrouter-api", "name": "OpenRouter API"}}, "notes": "**PURPOSE**: Synthesizes all analysis results into actionable UX insights with clear business impact, implementation guidance, and success metrics for stakeholder decision-making.\n\n**IMPLEMENTATION**: OpenAI node configured for strategic insight generation with structured output format for business consumption.\n\n**INSIGHT CATEGORIES**:\n1. **Key Findings**: 3-5 main discoveries from the research\n2. **User Pain Points**: Prioritized issues with severity assessment\n3. **Opportunities**: Specific areas for product improvement\n4. **Recommendations**: Actionable next steps with implementation guidance\n5. **Success Metrics**: Measurable criteria for tracking improvements\n\n**CONFIGURATION**:\n- Model: gpt-4o-mini (cost-effective for synthesis tasks)\n- Temperature: 0.2 (low for consistent, focused insights)\n- Max Tokens: 4000 (sufficient for comprehensive insights)\n\n**BUSINESS-FOCUSED OUTPUT**:\n```javascript\n{\n  \"keyFindings\": [{\n    \"finding\": \"Mobile navigation causes 60% of user abandonment\",\n    \"evidence\": \"15 navigation issues, 12 frustration codes\",\n    \"businessImpact\": \"High - directly affects conversion rates\",\n    \"priority\": \"critical\"\n  }],\n  \"recommendations\": [{\n    \"action\": \"Redesign mobile navigation\",\n    \"complexity\": \"medium\",\n    \"timeline\": \"2-3 sprints\",\n    \"successMetrics\": [\"Reduce mobile bounce rate by 25%\"]\n  }]\n}\n```\n\n**DATA FLOW**:\n- Input: Detected patterns + quantitative analysis\n- Output: Business-ready insights with implementation roadmap\n\n**AI AGENT INTEGRATION**: Could be enhanced with AI Agent for:\n- Multi-stakeholder perspective analysis\n- ROI calculations and business case development\n- Competitive analysis integration\n- Risk assessment for recommendations\n\n**RECOMMENDED ENHANCEMENT**: Replace with AI Agent connected to MCP tools for business intelligence and strategic planning capabilities."}, {"parameters": {"model": "gpt-4o-mini", "messages": {"values": [{"content": "=Create data-driven user archetypes based on the analysis.\n\nStructured Insights:\n{{ JSON.stringify($json.choices[0].message.content, null, 2) }}\n\nDetected Patterns:\n{{ JSON.stringify($('11. Pattern Detection').item.json.choices[0].message.content, null, 2) }}\n\nCreate 3-5 user archetypes representing distinct user groups based on:\n1. Behavioral patterns\n2. Pain points and frustrations\n3. Goals and motivations\n4. Technology comfort level\n5. Usage context\n\nFor each archetype provide:\n- Name (memorable and descriptive)\n- Demographics (age range, role, experience)\n- Goals (what they want to achieve)\n- Frustrations (main pain points)\n- Behaviors (interaction patterns)\n- Motivations (what drives them)\n- Technology Comfort (novice/intermediate/expert)\n- Quote (representative statement)\n- Percentage (estimated user base portion)\n\nReturn JSON array of user archetypes."}]}, "options": {"temperature": 0.3, "maxTokens": 4000}}, "id": "archetype-creation", "name": "13. Archetype Creation", "type": "n8n-nodes-base.openAi", "typeVersion": 1.4, "position": [3320, 240], "credentials": {"openAiApi": {"id": "openrouter-api", "name": "OpenRouter API"}}, "notes": "**PURPOSE**: Creates data-driven user archetypes based on behavioral patterns and insights to provide design teams with concrete user representations for decision-making.\n\n**IMPLEMENTATION**: OpenAI node configured for persona generation using research data to create realistic, actionable user archetypes.\n\n**ARCHETYPE DEVELOPMENT CRITERIA**:\n1. **Behavioral Patterns**: How users interact with the product\n2. **Pain Points**: Specific frustrations and barriers\n3. **Goals & Motivations**: What drives user behavior\n4. **Technology Comfort**: Digital literacy and preferences\n5. **Usage Context**: When, where, and why users engage\n\n**CONFIGURATION**:\n- Model: gpt-4o-mini (good for creative synthesis)\n- Temperature: 0.3 (balanced for realistic but varied personas)\n- Max Tokens: 4000 (sufficient for detailed archetypes)\n\n**ARCHETYPE STRUCTURE**:\n```javascript\n[{\n  \"name\": \"Efficiency-Focused Emma\",\n  \"demographics\": {\n    \"ageRange\": \"28-35\",\n    \"role\": \"Product Manager\",\n    \"experience\": \"3-5 years\"\n  },\n  \"goals\": [\"Complete tasks quickly\", \"Minimize cognitive load\"],\n  \"frustrations\": [\"Complex navigation\", \"Slow loading times\"],\n  \"behaviors\": [\"Uses keyboard shortcuts\", \"Skips tutorials\"],\n  \"motivations\": [\"Career advancement\", \"Work-life balance\"],\n  \"technologyComfort\": \"expert\",\n  \"quote\": \"I just want to get things done without jumping through hoops\",\n  \"percentage\": 35\n}]\n```\n\n**DATA FLOW**:\n- Input: Structured insights + detected patterns\n- Output: 3-5 user archetypes with detailed profiles\n\n**AI AGENT INTEGRATION**: Could be enhanced with AI Agent for:\n- Multi-dimensional persona validation\n- Archetype testing against research data\n- Dynamic persona evolution based on new data\n- Cross-validation with demographic databases\n\n**RECOMMENDED ENHANCEMENT**: Replace with AI Agent connected to MCP tools for persona research and validation methodologies."}, {"parameters": {"model": "gpt-4o-mini", "messages": {"values": [{"content": "=Generate \"How Might We\" questions from the insights and user archetypes.\n\nUser Archetypes:\n{{ JSON.stringify($json.choices[0].message.content, null, 2) }}\n\nStructured Insights:\n{{ JSON.stringify($('12. Insight Generation').item.json.choices[0].message.content, null, 2) }}\n\nGenerate HMW questions that:\n1. <PERSON><PERSON> identified pain points\n2. Leverage opportunities\n3. Consider different user archetypes\n4. Focus on actionable solutions\n\nFor each HMW question provide:\n- Question text\n- Related pain point/opportunity\n- Target archetype(s)\n- Potential impact (high/medium/low)\n- Implementation complexity\n- Category (usability/features/design/etc)\n\nReturn JSON array of HMW questions grouped by category."}]}, "options": {"temperature": 0.4, "maxTokens": 3000}}, "id": "hmw-generation", "name": "14. HMW Generation", "type": "n8n-nodes-base.openAi", "typeVersion": 1.4, "position": [3540, 240], "credentials": {"openAiApi": {"id": "openrouter-api", "name": "OpenRouter API"}}, "notes": "**PURPOSE**: Generates \"How Might We\" questions to transform research insights into actionable design challenges that guide ideation and solution development.\n\n**IMPLEMENTATION**: OpenAI node configured for creative problem framing using design thinking methodology to convert insights into opportunity statements.\n\n**HMW GENERATION CRITERIA**:\n1. **Pain Point Addressing**: Direct response to identified user frustrations\n2. **Opportunity Leveraging**: Builds on identified improvement areas\n3. **Archetype-Specific**: Tailored to different user types and contexts\n4. **Solution-Focused**: Actionable and implementable challenges\n\n**CONFIGURATION**:\n- Model: gpt-4o-mini (good for creative ideation)\n- Temperature: 0.4 (higher creativity for diverse question generation)\n- Max Tokens: 3000 (sufficient for comprehensive HMW set)\n\n**HMW STRUCTURE**:\n```javascript\n{\n  \"usability\": [{\n    \"question\": \"How might we simplify navigation for efficiency-focused users?\",\n    \"painPoint\": \"Complex navigation causing task abandonment\",\n    \"targetArchetypes\": [\"Efficiency-Focused Emma\"],\n    \"impact\": \"high\",\n    \"complexity\": \"medium\",\n    \"category\": \"usability\"\n  }],\n  \"features\": [...],\n  \"design\": [...]\n}\n```\n\n**QUESTION CATEGORIES**:\n- **Usability**: Interface and interaction improvements\n- **Features**: New functionality opportunities\n- **Design**: Visual and aesthetic enhancements\n- **Performance**: Speed and reliability improvements\n- **Accessibility**: Inclusive design opportunities\n\n**DATA FLOW**:\n- Input: User archetypes + structured insights\n- Output: Categorized HMW questions for ideation\n\n**AI AGENT INTEGRATION**: Could be enhanced with AI Agent for:\n- Multi-perspective HMW generation\n- Feasibility assessment integration\n- Competitive analysis for question validation\n- Iterative refinement based on team feedback\n\n**RECOMMENDED ENHANCEMENT**: Replace with AI Agent connected to MCP tools for design thinking methodologies and ideation frameworks."}, {"parameters": {"model": "gpt-4o-mini", "messages": {"values": [{"content": "=Prioritize opportunities using RICE methodology (Reach, Impact, Confidence, Effort).\n\nHMW Questions:\n{{ JSON.stringify($json.choices[0].message.content, null, 2) }}\n\nInsights:\n{{ JSON.stringify($('12. Insight Generation').item.json.choices[0].message.content, null, 2) }}\n\nFor each opportunity/HMW question, score:\n- Reach: How many users affected (1-10)\n- Impact: Improvement magnitude (1-10)\n- Confidence: How sure we are (1-10)\n- Effort: Implementation effort (1-10, lower = less effort)\n\nCalculate RICE score: (Reach × Impact × Confidence) / Effort\n\nReturn prioritized opportunities with:\n- Opportunity description\n- RICE scores breakdown\n- Total RICE score\n- Priority ranking\n- Recommended timeline\n- Success metrics\n\nSort by RICE score (highest first)."}]}, "options": {"temperature": 0.2, "maxTokens": 3000}}, "id": "opportunity-prioritization", "name": "15. Opportunity Prioritization", "type": "n8n-nodes-base.openAi", "typeVersion": 1.4, "position": [3760, 240], "credentials": {"openAiApi": {"id": "openrouter-api", "name": "OpenRouter API"}}, "notes": "**PURPOSE**: Applies RICE methodology (Reach, Impact, Confidence, Effort) to prioritize opportunities and HMW questions, providing data-driven roadmap for implementation.\n\n**IMPLEMENTATION**: OpenAI node configured for systematic prioritization using established product management framework for objective decision-making.\n\n**RICE METHODOLOGY**:\n- **Reach**: Number of users affected (1-10 scale)\n- **Impact**: Magnitude of improvement per user (1-10 scale)\n- **Confidence**: Certainty in estimates (1-10 scale)\n- **Effort**: Implementation complexity (1-10 scale, lower = easier)\n- **RICE Score**: (Reach × Impact × Confidence) / Effort\n\n**CONFIGURATION**:\n- Model: gpt-4o-mini (good for analytical tasks)\n- Temperature: 0.2 (low for consistent scoring)\n- Max Tokens: 3000 (sufficient for detailed prioritization)\n\n**PRIORITIZATION OUTPUT**:\n```javascript\n[{\n  \"opportunity\": \"Simplify mobile navigation\",\n  \"riceScores\": {\n    \"reach\": 8,\n    \"impact\": 9,\n    \"confidence\": 7,\n    \"effort\": 5\n  },\n  \"totalScore\": 100.8,\n  \"ranking\": 1,\n  \"timeline\": \"2-3 sprints\",\n  \"successMetrics\": [\"Reduce mobile bounce rate by 25%\"]\n}]\n```\n\n**BUSINESS VALUE**:\n- Objective prioritization framework\n- Resource allocation guidance\n- ROI-focused implementation roadmap\n- Stakeholder alignment tool\n\n**DATA FLOW**:\n- Input: HMW questions + insights\n- Output: Prioritized opportunities with implementation guidance\n\n**AI AGENT INTEGRATION**: Could be enhanced with AI Agent for:\n- Multi-criteria decision analysis\n- Risk assessment integration\n- Resource availability consideration\n- Dynamic re-prioritization based on changing conditions\n\n**RECOMMENDED ENHANCEMENT**: Replace with AI Agent connected to MCP tools for advanced prioritization algorithms and business intelligence integration."}, {"parameters": {"method": "POST", "url": "={{ $vars.WIKIJS_URL || 'http://localhost:3002' }}/api/pages", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "httpHeaderAuth": {"name": "Authorization", "value": "=Bearer {{ $vars.WIKIJS_API_TOKEN }}"}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "title", "value": "={{ $json.projectName }} - UX Research Analysis"}, {"name": "path", "value": "=/ux-research/{{ $json.executionId }}"}, {"name": "content", "value": "=# {{ $json.projectName }} - UX Research Analysis\n\n## Executive Summary\n\n**Project:** {{ $json.projectName }}\n**Analysis Date:** {{ $json.timestamp }}\n**Execution ID:** {{ $json.executionId }}\n\n## Key Findings\n\n{{ JSON.stringify($('12. Insight Generation').item.json.choices[0].message.content, null, 2) }}\n\n## User Archetypes\n\n{{ JSON.stringify($('13. Archetype Creation').item.json.choices[0].message.content, null, 2) }}\n\n## Prioritized Opportunities\n\n{{ JSON.stringify($('15. Opportunity Prioritization').item.json.choices[0].message.content, null, 2) }}\n\n## How Might We Questions\n\n{{ JSON.stringify($('14. HMW Generation').item.json.choices[0].message.content, null, 2) }}\n\n## Quantitative Analysis\n\n{{ JSON.stringify($('10. Quantitative Analysis').item.json, null, 2) }}\n\n## Methodology\n\nThis analysis was conducted using the ROBO-RESEARCHER-2000 automated UX research platform, following a comprehensive 17-step methodology:\n\n1. Data validation and preprocessing\n2. Text segmentation\n3. Deductive and emergent coding\n4. Category grouping and affinity mapping\n5. Quantitative analysis\n6. Pattern detection\n7. Insight generation\n8. User archetype creation\n9. Opportunity identification and prioritization\n\n---\n\n*Generated automatically by ROBO-RESEARCHER-2000*"}, {"name": "tags", "value": "=[\"ux-research\", \"{{ $json.projectName }}\", \"automated-analysis\"]"}]}, "options": {}}, "id": "wiki-documentation", "name": "16. Wiki Documentation", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [3980, 240], "notes": "**PURPOSE**: Creates comprehensive documentation in Wiki.js knowledge base for stakeholder access, team collaboration, and research archive maintenance.\n\n**IMPLEMENTATION**: Standard n8n HTTP Request node configured for Wiki.js API integration with structured content creation.\n\n**DOCUMENTATION STRUCTURE**:\n1. **Executive Summary**: Project metadata and key identifiers\n2. **Key Findings**: Primary insights from analysis\n3. **User Archetypes**: Data-driven persona profiles\n4. **Prioritized Opportunities**: RICE-scored improvement areas\n5. **HMW Questions**: Design challenge statements\n6. **Quantitative Analysis**: Statistical summaries\n7. **Methodology**: Process transparency and reproducibility\n\n**CONFIGURATION**:\n- Method: POST (creates new wiki page)\n- Authentication: Bearer token via environment variable\n- Content-Type: application/json\n- URL: Configurable Wiki.js instance endpoint\n\n**API PARAMETERS**:\n- title: Project-specific page title\n- path: Organized URL structure (/ux-research/{executionId})\n- content: Markdown-formatted comprehensive report\n- tags: Searchable metadata tags\n\n**CONTENT FORMATTING**:\n- Markdown structure for readability\n- JSON.stringify for data preservation\n- Execution metadata for traceability\n- Methodology documentation for transparency\n\n**DATA FLOW**:\n- Input: All analysis results from previous steps\n- Output: Wiki page creation confirmation\n\n**INTEGRATION REQUIREMENTS**:\n- Wiki.js instance with API enabled\n- Valid API token with page creation permissions\n- Network connectivity to Wiki.js server\n\n**AI AGENT INTEGRATION**: Could be enhanced with AI Agent for:\n- Intelligent content formatting and summarization\n- Multi-format export (PDF, Word, PowerPoint)\n- Automatic cross-referencing with related research\n- Dynamic content updates based on new findings\n\n**RECOMMENDED ENHANCEMENT**: Add error handling for API failures and implement retry logic for reliability."}, {"parameters": {"fromEmail": "={{ $vars.SMTP_USER || '<EMAIL>' }}", "toEmail": "={{ $json.email }}", "subject": "=UX Research Analysis Complete: {{ $json.projectName }}", "emailFormat": "html", "message": "=<!DOCTYPE html>\n<html>\n<head>\n    <style>\n        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }\n        .header { background: #4CAF50; color: white; padding: 20px; text-align: center; }\n        .content { padding: 20px; }\n        .summary { background: #f9f9f9; padding: 15px; border-left: 4px solid #4CAF50; margin: 20px 0; }\n        .metrics { display: flex; justify-content: space-around; margin: 20px 0; }\n        .metric { text-align: center; }\n        .metric h3 { margin: 0; color: #4CAF50; }\n        .footer { background: #f1f1f1; padding: 15px; text-align: center; font-size: 12px; }\n        .button { background: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 0; }\n    </style>\n</head>\n<body>\n    <div class=\"header\">\n        <h1>🤖 ROBO-RESEARCHER-2000</h1>\n        <h2>UX Research Analysis Complete</h2>\n    </div>\n    \n    <div class=\"content\">\n        <h2>Project: {{ $json.projectName }}</h2>\n        \n        <div class=\"summary\">\n            <h3>📊 Analysis Summary</h3>\n            <p><strong>Execution ID:</strong> {{ $json.executionId }}</p>\n            <p><strong>Completed:</strong> {{ $json.timestamp }}</p>\n            <p><strong>Total Segments Analyzed:</strong> {{ $json.segmentCount || 0 }}</p>\n            <p><strong>Codes Identified:</strong> {{ $json.totalCodes || 0 }}</p>\n        </div>\n        \n        <h3>🎯 Top Insights</h3>\n        <div class=\"summary\">\n            <p>Your UX research analysis has identified key user pain points, behavioral patterns, and improvement opportunities. The analysis includes data-driven user archetypes and prioritized recommendations using the RICE methodology.</p>\n        </div>\n        \n        <h3>📚 Access Your Results</h3>\n        <p>Your complete analysis is available in the documentation system:</p>\n        <a href=\"{{ $vars.WIKIJS_URL || 'http://localhost:3002' }}/ux-research/{{ $json.executionId }}\" class=\"button\">View Complete Analysis</a>\n        \n        <h3>📈 Next Steps</h3>\n        <ol>\n            <li>Review the prioritized opportunities in your documentation</li>\n            <li>Share user archetypes with your design team</li>\n            <li>Use HMW questions for ideation sessions</li>\n            <li>Implement high-priority recommendations</li>\n            <li>Set up success metrics tracking</li>\n        </ol>\n    </div>\n    \n    <div class=\"footer\">\n        <p>Generated by ROBO-RESEARCHER-2000 | Automated UX Research Platform</p>\n        <p>For support, visit our documentation or contact your system administrator.</p>\n    </div>\n</body>\n</html>", "options": {}}, "id": "email-notification", "name": "17. Email Notification", "type": "n8n-nodes-base.emailSend", "typeVersion": 2.1, "position": [4200, 240], "credentials": {"smtp": {"id": "smtp-credentials", "name": "SMTP Email"}}, "notes": "**PURPOSE**: Sends professional HTML email notification to stakeholders upon workflow completion, providing analysis summary and access links to detailed results.\n\n**IMPLEMENTATION**: Standard n8n Email Send node configured for HTML email delivery with professional styling and actionable content.\n\n**EMAIL COMPONENTS**:\n1. **Header**: Branded header with ROBO-RESEARCHER-2000 identity\n2. **Analysis Summary**: Key metrics and execution metadata\n3. **Top Insights**: High-level findings overview\n4. **Access Link**: Direct link to Wiki.js documentation\n5. **Next Steps**: Actionable guidance for stakeholders\n6. **Footer**: Support information and branding\n\n**CONFIGURATION**:\n- From Email: Configurable sender address (default: <EMAIL>)\n- To Email: Dynamic recipient from input data\n- Subject: Project-specific subject line\n- Format: HTML with embedded CSS styling\n\n**EMAIL STYLING**:\n- Professional color scheme (#4CAF50 primary)\n- Responsive design principles\n- Clear visual hierarchy\n- Branded button styling\n- Mobile-friendly layout\n\n**DYNAMIC CONTENT**:\n- Project name integration\n- Execution metadata (ID, timestamp)\n- Analysis metrics (segments, codes)\n- Direct documentation links\n- Personalized messaging\n\n**SMTP REQUIREMENTS**:\n- Valid SMTP credentials configured\n- Network connectivity to mail server\n- Proper authentication and security settings\n\n**DATA FLOW**:\n- Input: Complete analysis results and metadata\n- Output: Email delivery confirmation\n\n**AI AGENT INTEGRATION**: Could be enhanced with AI Agent for:\n- Personalized email content based on analysis results\n- Executive summary generation\n- Intelligent next steps recommendations\n- Multi-format report attachments\n- Follow-up scheduling and reminders\n\n**RECOMMENDED ENHANCEMENT**: Add email delivery error handling and implement alternative notification channels (Slack, Teams) for reliability."}, {"parameters": {"respondWith": "json", "responseBody": "={{ { success: true, executionId: $json.executionId, projectName: $json.projectName, completionTime: $now.toISO(), message: 'UX Research analysis completed successfully for project: ' + $json.projectName, documentationUrl: ($vars.WIKIJS_URL || 'http://localhost:3002') + '/ux-research/' + $json.executionId } }}"}, "id": "webhook-response", "name": "Webhook Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [4420, 240]}], "connections": {"1. Webhook Trigger": {"main": [[{"node": "2. Validate Input", "type": "main", "index": 0}]]}, "2. Validate Input": {"main": [[{"node": "2b. Prepare Data", "type": "main", "index": 0}]]}, "2b. Prepare Data": {"main": [[{"node": "3. Upload to MinIO", "type": "main", "index": 0}]]}, "3. Upload to MinIO": {"main": [[{"node": "4. Text Preprocessing", "type": "main", "index": 0}]]}, "4. Text Preprocessing": {"main": [[{"node": "5. Segmentation", "type": "main", "index": 0}]]}, "5. Segmentation": {"main": [[{"node": "6. Deductive Coding", "type": "main", "index": 0}]]}, "6. Deductive Coding": {"main": [[{"node": "7. Open Coding AI", "type": "main", "index": 0}]]}, "7. Open Coding AI": {"main": [[{"node": "7b. Merge Codes", "type": "main", "index": 0}]]}, "7b. Merge Codes": {"main": [[{"node": "8. Category Grouping", "type": "main", "index": 0}]]}, "8. Category Grouping": {"main": [[{"node": "9. Affinity Mapping", "type": "main", "index": 0}]]}, "9. Affinity Mapping": {"main": [[{"node": "10. Quantitative Analysis", "type": "main", "index": 0}]]}, "10. Quantitative Analysis": {"main": [[{"node": "11. <PERSON><PERSON> Detection", "type": "main", "index": 0}]]}, "11. Pattern Detection": {"main": [[{"node": "12. Insight Generation", "type": "main", "index": 0}]]}, "12. Insight Generation": {"main": [[{"node": "13. Archetype Creation", "type": "main", "index": 0}]]}, "13. Archetype Creation": {"main": [[{"node": "14. HMW Generation", "type": "main", "index": 0}]]}, "14. HMW Generation": {"main": [[{"node": "15. Opportunity Prioritization", "type": "main", "index": 0}]]}, "15. Opportunity Prioritization": {"main": [[{"node": "16. Wiki Documentation", "type": "main", "index": 0}]]}, "16. Wiki Documentation": {"main": [[{"node": "17. Email Notification", "type": "main", "index": 0}]]}, "17. Email Notification": {"main": [[{"node": "Webhook Response", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1", "saveManualExecutions": true, "callerPolicy": "workflowsFromSameOwner", "errorWorkflow": "", "timezone": "America/New_York"}, "versionId": "2025.4", "meta": {"templateCredsSetupCompleted": false, "instanceId": "robo-researcher-2000"}, "id": "robo-researcher-complete-17step", "tags": [{"id": "robo-researcher", "name": "robo-researcher"}, {"id": "ux-research", "name": "ux-research"}, {"id": "production", "name": "production"}, {"id": "complete", "name": "complete"}]}
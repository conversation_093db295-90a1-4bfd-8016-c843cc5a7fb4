---
marp: true
theme: default
class: lead
paginate: true
backgroundColor: #fff
backgroundImage: url('https://marp.app/assets/hero-background.svg')
header: 'ROBO-RESEARCHER-2000 | UX Research'
footer: '{{project_name}} | {{date}}'
---

# {{project_name}}
## UX Research Results

**Date:** {{date}}
**Researcher:** {{researcher_name}}
**Participants:** {{participant_count}}

---

## 🎯 Research Objectives

{{#each objectives}}
- {{this}}
{{/each}}

---

## 📊 Methodology

- **Study type:** {{study_type}}
- **Average duration:** {{average_duration}} minutes
- **Collection method:** {{collection_method}}
- **Analysis tools:** ROBO-RESEARCHER-2000

---

## 🔍 Key Findings

{{#each key_findings}}
### {{@index}}. {{title}}

> "{{quote}}"
> — {{participant_alias}}

**Frequency:** {{frequency}} participants
**Intensity:** {{intensity}}/5

---
{{/each}}

## 👥 User Archetypes

{{#each archetypes}}
---

### {{name}}

![{{name}}]({{image_url}})

**Representative quote:**
> "{{representative_quote}}"

**Jobs to be Done:**
{{#each jobs}}
- {{this}}
{{/each}}

**Critical friction:** {{critical_friction}}

**Preferred channel:** {{preferred_channel}}

{{/each}}

---

## 💡 Main Insights

{{#each insights}}
### Insight {{@index}}

**[{{user_type}}] {{action}} {{context}}**

**Root cause:** {{root_cause}}

**Business impact:** {{business_impact}}

**Priority:** {{priority}} (ICE: {{ice_score}})

---
{{/each}}

## 🚀 Design Opportunities

{{#each hmw_questions}}
### How might we...?

**{{question}}**

- **So that:** {{outcome}}
- **Without:** {{constraint}}
- **Related OKR:** {{related_okr}}

---
{{/each}}

## 📈 RICE Prioritization

| Opportunity | Reach | Impact | Confidence | Effort | Score |
|-------------|-------|--------|------------|--------|-------|
{{#each opportunities}}
| {{name}} | {{reach}} | {{impact}} | {{confidence}} | {{effort}} | **{{rice_score}}** |
{{/each}}

---

## 🎨 Affinity Map

![Affinity Map]({{affinity_map_url}})

**Main categories:**
{{#each affinity_categories}}
- **{{name}}:** {{description}} ({{code_count}} codes)
{{/each}}

---

## 📊 Quantitative Analysis

### Code Distribution

{{#each code_frequencies}}
- **{{code_name}}:** {{frequency}} mentions ({{percentage}}%)
{{/each}}

### Emotional Intensity

- **Frustration moments:** {{frustration_intensity}}/5
- **Satisfaction moments:** {{satisfaction_intensity}}/5
- **Average cognitive load:** {{cognitive_load}}/5

---

## ⚠️ Critical Pain Points

{{#each pain_points}}
### {{title}}

**Description:** {{description}}

**Frequency:** {{frequency}} participants

**Representative quote:**
> "{{quote}}"
> — {{participant}}

**Impact:** {{impact_level}}

---
{{/each}}

## ✨ Delight Moments

{{#each delight_moments}}
### {{title}}

**Description:** {{description}}

**Representative quote:**
> "{{quote}}"
> — {{participant}}

**Opportunity:** {{opportunity}}

---
{{/each}}

## 🔄 Next Steps

### Immediate (1-2 weeks)
{{#each immediate_actions}}
- {{this}}
{{/each}}

### Short term (1-3 months)
{{#each short_term_actions}}
- {{this}}
{{/each}}

### Long term (3-6 months)
{{#each long_term_actions}}
- {{this}}
{{/each}}

---

## 📋 Recommendations

{{#each recommendations}}
### {{priority}} Priority: {{title}}

**Description:** {{description}}

**Rationale:** {{rationale}}

**Required resources:** {{resources}}

**Estimated timeline:** {{timeline}}

---
{{/each}}

## 📚 Appendix

### Detailed Methodology
- **Interview script:** [Link to document]
- **Recruitment criteria:** [Link to document]
- **Analysis process:** ROBO-RESEARCHER-2000

### Additional Data
- **Complete transcriptions:** Available in Wiki.js
- **Detailed codes:** [Link to repository]
- **Quantitative data:** [Link to dashboard]

---

## 🤝 Acknowledgments

**Participants:** Thanks to all users who shared their time and experiences.

**Team:** {{team_members}}

**Tools:** Automated analysis with ROBO-RESEARCHER-2000

---

# Questions?

**Contact:**
- Email: {{researcher_email}}
- Complete documentation: {{wiki_url}}
- Data repository: {{data_repository_url}}

**Next review:** {{next_review_date}}

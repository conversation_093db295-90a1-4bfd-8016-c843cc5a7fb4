{"name": "ROBO-RESEARCHER-2000 Main Workflow", "nodes": [{"parameters": {"httpMethod": "POST", "path": "robo-researcher", "responseMode": "responseNode", "options": {}}, "id": "webhook-trigger", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [240, 300], "webhookId": "robo-researcher"}, {"parameters": {"language": "python", "pythonCode": "# Step 1: Validate Input Data\nimport json\nimport re\nfrom datetime import datetime\n\n# Get input data\ninput_data = $input.all()[0].json\n\n# Required fields validation\nrequired_fields = ['project_name', 'email', 'transcription', 'api_keys']\nmissing_fields = []\n\nfor field in required_fields:\n    if field not in input_data or not input_data[field]:\n        missing_fields.append(field)\n\nif missing_fields:\n    return [{\n        'json': {\n            'success': False,\n            'error': f'Missing required fields: {\", \".join(missing_fields)}',\n            'step': 'validation',\n            'timestamp': datetime.now().isoformat()\n        }\n    }]\n\n# Email validation\nemail_pattern = r'^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$'\nif not re.match(email_pattern, input_data['email']):\n    return [{\n        'json': {\n            'success': False,\n            'error': 'Invalid email format',\n            'step': 'validation',\n            'timestamp': datetime.now().isoformat()\n        }\n    }]\n\n# Transcription validation\ntranscription = input_data['transcription']\nif len(transcription) < 100:\n    return [{\n        'json': {\n            'success': False,\n            'error': 'Transcription too short (minimum 100 characters)',\n            'step': 'validation',\n            'timestamp': datetime.now().isoformat()\n        }\n    }]\n\n# API keys validation\napi_keys = input_data.get('api_keys', {})\nif 'openrouter' not in api_keys or not api_keys['openrouter']:\n    return [{\n        'json': {\n            'success': False,\n            'error': 'OpenRouter API key is required',\n            'step': 'validation',\n            'timestamp': datetime.now().isoformat()\n        }\n    }]\n\n# Generate workflow execution ID\nexecution_id = f\"exec_{datetime.now().strftime('%Y%m%d_%H%M%S')}\"\n\n# Return validated data\nreturn [{\n    'json': {\n        'success': True,\n        'execution_id': execution_id,\n        'project_name': input_data['project_name'],\n        'email': input_data['email'],\n        'transcription': transcription,\n        'study_type': input_data.get('study_type', 'user_interview'),\n        'objectives': input_data.get('objectives', ''),\n        'language': input_data.get('language', 'en'),\n        'analysis_depth': input_data.get('analysis_depth', 'standard'),\n        'api_keys': api_keys,\n        'options': input_data.get('options', {}),\n        'step': 'validation_complete',\n        'timestamp': datetime.now().isoformat()\n    }\n}]"}, "id": "validate-input", "name": "Validate Input", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"method": "PUT", "url": "http://localhost:9000/robo-researcher-data/transcripts/{{ $json.execution_id }}/original.txt", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "text/plain"}]}, "sendBody": true, "contentType": "raw", "rawContentType": "text", "body": "={{ $json.transcription }}"}, "id": "upload-to-minio", "name": "Upload to MinIO", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [680, 300]}, {"parameters": {"language": "python", "pythonCode": "# Step 4: Text Preprocessing\nimport re\nimport json\nfrom datetime import datetime\n\n# Get validated data\ndata = $input.all()[0].json\ntranscription = data['transcription']\nlanguage = data.get('language', 'en')\n\n# Import preprocessing module (simulated)\n# In actual implementation, this would import from scripts/text_preprocessing.py\n\ndef preprocess_text(text, language='en'):\n    \"\"\"Basic text preprocessing\"\"\"\n    \n    # Remove timestamps\n    text = re.sub(r'\\[\\d{1,2}:\\d{2}:\\d{2}\\]', '', text)\n    text = re.sub(r'\\(\\d{1,2}:\\d{2}:\\d{2}\\)', '', text)\n    \n    # Remove filler words based on language\n    if language == 'es':\n        fillers = r'\\b(eh|ehm|mm|este|bueno|o sea|como que|tipo|pues|entonces)\\b'\n    else:\n        fillers = r'\\b(uh|uhm|um|hmm|like|you know|I mean|sort of|kind of)\\b'\n    \n    text = re.sub(fillers, '', text, flags=re.IGNORECASE)\n    \n    # Normalize whitespace\n    text = re.sub(r'\\s+', ' ', text)\n    text = text.strip()\n    \n    # Extract speaker segments\n    segments = []\n    speaker_pattern = r'^([A-Za-z0-9_]+):\\s*(.+?)(?=\\n[A-Za-z0-9_]+:|$)'\n    matches = list(re.finditer(speaker_pattern, text, re.MULTILINE | re.DOTALL))\n    \n    if matches:\n        for i, match in enumerate(matches):\n            speaker = match.group(1)\n            content = match.group(2).strip()\n            \n            if len(content) >= 10:\n                segments.append({\n                    'segment_id': i + 1,\n                    'speaker': speaker,\n                    'content': content,\n                    'word_count': len(content.split()),\n                    'char_count': len(content)\n                })\n    else:\n        # No speaker labels, treat as single segment\n        if len(text) >= 10:\n            segments.append({\n                'segment_id': 1,\n                'speaker': 'Unknown',\n                'content': text,\n                'word_count': len(text.split()),\n                'char_count': len(text)\n            })\n    \n    return {\n        'processed_text': text,\n        'segments': segments,\n        'statistics': {\n            'original_length': len(data['transcription']),\n            'processed_length': len(text),\n            'segment_count': len(segments),\n            'reduction_percentage': round((1 - len(text) / len(data['transcription'])) * 100, 2)\n        }\n    }\n\n# Process the transcription\nresult = preprocess_text(transcription, language)\n\n# Return processed data\nreturn [{\n    'json': {\n        **data,\n        'processed_transcription': result,\n        'step': 'preprocessing_complete',\n        'timestamp': datetime.now().isoformat()\n    }\n}]"}, "id": "text-preprocessing", "name": "Text Preprocessing", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 300]}, {"parameters": {"language": "python", "pythonCode": "# Step 5: Segmentation\nimport json\nfrom datetime import datetime\n\n# Get preprocessed data\ndata = $input.all()[0].json\nsegments = data['processed_transcription']['segments']\n\n# Enhanced segmentation by topics/themes\ndef segment_by_topics(segments):\n    \"\"\"Segment text by topics using simple keyword clustering\"\"\"\n    \n    topic_keywords = {\n        'navigation': ['navigate', 'menu', 'find', 'search', 'look for', 'go to'],\n        'usability': ['easy', 'difficult', 'hard', 'simple', 'complex', 'confusing'],\n        'performance': ['slow', 'fast', 'quick', 'loading', 'wait', 'delay'],\n        'design': ['design', 'layout', 'color', 'font', 'visual', 'appearance'],\n        'functionality': ['feature', 'function', 'work', 'button', 'click', 'tap'],\n        'emotion': ['frustrated', 'happy', 'satisfied', 'annoyed', 'pleased', 'angry']\n    }\n    \n    enhanced_segments = []\n    \n    for segment in segments:\n        content_lower = segment['content'].lower()\n        detected_topics = []\n        \n        # Detect topics based on keywords\n        for topic, keywords in topic_keywords.items():\n            if any(keyword in content_lower for keyword in keywords):\n                detected_topics.append(topic)\n        \n        # If no topics detected, assign 'general'\n        if not detected_topics:\n            detected_topics = ['general']\n        \n        enhanced_segment = {\n            **segment,\n            'topics': detected_topics,\n            'primary_topic': detected_topics[0],\n            'topic_count': len(detected_topics)\n        }\n        \n        enhanced_segments.append(enhanced_segment)\n    \n    return enhanced_segments\n\n# Process segments\nenhanced_segments = segment_by_topics(segments)\n\n# Calculate segmentation statistics\ntopic_distribution = {}\nfor segment in enhanced_segments:\n    for topic in segment['topics']:\n        topic_distribution[topic] = topic_distribution.get(topic, 0) + 1\n\n# Return segmented data\nreturn [{\n    'json': {\n        **data,\n        'segments': enhanced_segments,\n        'segmentation_stats': {\n            'total_segments': len(enhanced_segments),\n            'topic_distribution': topic_distribution,\n            'avg_topics_per_segment': round(sum(seg['topic_count'] for seg in enhanced_segments) / len(enhanced_segments), 2)\n        },\n        'step': 'segmentation_complete',\n        'timestamp': datetime.now().isoformat()\n    }\n}]"}, "id": "segmentation", "name": "Segmentation", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 300]}, {"parameters": {"language": "python", "pythonCode": "# Step 6: Deductive Coding\nimport json\nfrom datetime import datetime\n\n# Get segmented data\ndata = $input.all()[0].json\nsegments = data['segments']\n\n# Load deductive codes (simplified version)\ndeductive_codes = {\n    'user_experience': {\n        'frustration': {\n            'keywords': ['frustrated', 'annoying', 'irritating', 'difficult', 'complicated', 'confusing'],\n            'category': 'negative_emotion'\n        },\n        'satisfaction': {\n            'keywords': ['satisfied', 'happy', 'easy', 'intuitive', 'clear', 'simple'],\n            'category': 'positive_emotion'\n        },\n        'confusion': {\n            'keywords': ['confused', 'lost', 'unclear', 'don\\'t understand'],\n            'category': 'cognitive_load'\n        }\n    },\n    'interaction_patterns': {\n        'navigation': {\n            'keywords': ['menu', 'navigate', 'search', 'find', 'go to', 'back'],\n            'category': 'behavior'\n        },\n        'input_methods': {\n            'keywords': ['click', 'tap', 'type', 'select', 'drag'],\n            'category': 'behavior'\n        }\n    }\n}\n\ndef apply_deductive_coding(segments, codes):\n    \"\"\"Apply predefined codes to segments\"\"\"\n    coded_segments = []\n    \n    for segment in segments:\n        content_lower = segment['content'].lower()\n        matched_codes = []\n        \n        # Find matching codes\n        for category, code_group in codes.items():\n            for code_name, code_info in code_group.items():\n                keywords = code_info.get('keywords', [])\n                \n                for keyword in keywords:\n                    if keyword in content_lower:\n                        if code_name not in matched_codes:\n                            matched_codes.append(code_name)\n                        break\n        \n        # Calculate intensity (1-5 scale)\n        intensity = 1.0\n        if any(word in content_lower for word in ['very', 'extremely', 'really', 'totally']):\n            intensity += 1.5\n        if any(word in content_lower for word in ['quite', 'somewhat', 'a bit']):\n            intensity += 0.5\n        if '!' in segment['content']:\n            intensity += 0.5\n        \n        intensity = min(5.0, max(1.0, intensity))\n        \n        # Detect emotion\n        positive_words = ['good', 'great', 'excellent', 'easy', 'clear', 'like', 'love']\n        negative_words = ['bad', 'terrible', 'difficult', 'hate', 'frustrated', 'annoying']\n        \n        pos_count = sum(1 for word in positive_words if word in content_lower)\n        neg_count = sum(1 for word in negative_words if word in content_lower)\n        \n        if pos_count > neg_count:\n            emotion = 'positive'\n        elif neg_count > pos_count:\n            emotion = 'negative'\n        else:\n            emotion = 'neutral'\n        \n        coded_segment = {\n            **segment,\n            'codes': matched_codes,\n            'intensity': round(intensity, 1),\n            'emotion': emotion,\n            'context': segment.get('primary_topic', 'general')\n        }\n        \n        coded_segments.append(coded_segment)\n    \n    return coded_segments\n\n# Apply deductive coding\ncoded_segments = apply_deductive_coding(segments, deductive_codes)\n\n# Calculate coding statistics\ncode_frequencies = {}\nfor segment in coded_segments:\n    for code in segment['codes']:\n        code_frequencies[code] = code_frequencies.get(code, 0) + 1\n\n# Return coded data\nreturn [{\n    'json': {\n        **data,\n        'coded_segments': coded_segments,\n        'deductive_coding_stats': {\n            'total_coded_segments': len([s for s in coded_segments if s['codes']]),\n            'code_frequencies': code_frequencies,\n            'avg_codes_per_segment': round(sum(len(s['codes']) for s in coded_segments) / len(coded_segments), 2)\n        },\n        'step': 'deductive_coding_complete',\n        'timestamp': datetime.now().isoformat()\n    }\n}]"}, "id": "deductive-coding", "name": "Deductive Coding", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1340, 300]}, {"parameters": {"method": "POST", "url": "https://openrouter.ai/api/v1/chat/completions", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "httpHeaderAuth": {"name": "Authorization", "value": "Bearer {{ $json.api_keys.openrouter }}"}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "contentType": "json", "jsonBody": "{\n  \"model\": \"anthropic/claude-3-sonnet\",\n  \"messages\": [\n    {\n      \"role\": \"system\",\n      \"content\": \"You are an expert UX researcher specializing in qualitative analysis. Analyze the provided text segments and suggest open codes (emergent themes) that are not covered by the existing deductive codes. Return only a JSON array of suggested codes with their descriptions.\"\n    },\n    {\n      \"role\": \"user\",\n      \"content\": \"Analyze these coded segments and suggest 5-10 additional open codes for themes not yet captured:\\n\\nExisting codes: {{ $json.deductive_coding_stats.code_frequencies }}\\n\\nSegments to analyze:\\n{{ $json.coded_segments | slice(0, 10) | map(item => item.content) | join('\\n---\\n') }}\\n\\nReturn format: [{\\\"code\\\": \\\"code_name\\\", \\\"description\\\": \\\"brief description\\\", \\\"category\\\": \\\"theme_category\\\"}]\"\n    }\n  ],\n  \"max_tokens\": 1000,\n  \"temperature\": 0.3\n}"}, "id": "open-coding-ai", "name": "Open Coding AI", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1560, 300]}], "connections": {"Webhook Trigger": {"main": [[{"node": "Validate Input", "type": "main", "index": 0}]]}, "Validate Input": {"main": [[{"node": "Upload to MinIO", "type": "main", "index": 0}]]}, "Upload to MinIO": {"main": [[{"node": "Text Preprocessing", "type": "main", "index": 0}]]}, "Text Preprocessing": {"main": [[{"node": "Segmentation", "type": "main", "index": 0}]]}, "Segmentation": {"main": [[{"node": "Deductive Coding", "type": "main", "index": 0}]]}, "Deductive Coding": {"main": [[{"node": "Open Coding AI", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1", "saveManualExecutions": true, "callerPolicy": "workflowsFromSameOwner"}, "versionId": "1.0", "meta": {"templateCredsSetupCompleted": true}, "id": "main-workflow", "tags": [{"createdAt": "2025-01-17T00:00:00.000Z", "updatedAt": "2025-01-17T00:00:00.000Z", "id": "robo-researcher", "name": "robo-researcher"}]}
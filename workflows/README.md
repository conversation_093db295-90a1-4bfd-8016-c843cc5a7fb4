# ROBO-RESEARCHER-2000 Workflows

This folder contains all n8n workflows for the automated UX research system.

## 📋 Main Workflows

### 1. ux-research-complete-17step-workflow.json
**Complete 17-step UX research automation workflow** - The main production workflow that automates the entire UX research process:

1. **Webhook Trigger** - Receives data from web client
2. **Validate Input** - Validates format and content
3. **Upload to MinIO** - Stores transcription
4. **Text Preprocessing** - Cleaning and anonymization
5. **Segmentation** - Division by topics
6. **Deductive Coding** - Predefined codes
7. **Open Coding AI** - AI suggests emergent codes
8. **Category Grouping** - Groups similar codes
9. **Affinity Mapping** - Generates visualizations
10. **Quantitative Analysis** - Metrics and frequencies
11. **Pattern Detection** - AI detects patterns
12. **Insight Generation** - Generates structured insights
13. **Archetype Creation** - Creates user archetypes
14. **HMW Generation** - "How Might We" questions
15. **Opportunity Prioritization** - RICE matrix
16. **Presentation Generation** - Marp → automatic PPTX
17. **Documentation & Email** - Wiki.js + notifications

### 2. ux-research-test-simple-workflow.json
**Simple test workflow** - Lightweight workflow for testing and development with basic validation and response.

### 3. ux-research-basic-workflow.json
**Basic workflow** - Simplified version with core functionality for smaller projects.



## 🔧 Templates

### AI Prompts
- `prompts/coding-prompt.txt` - Template for AI coding
- `prompts/insight-prompt.txt` - Template for insight generation
- `prompts/pattern-prompt.txt` - Template for pattern detection

### Configurations
- `config/default-codes.json` - Predefined codes for analysis
- `config/presentation-template.md` - Marp template for presentations
- `config/email-templates/` - Email templates

## 📥 Import

### In n8n Web Interface

1. Access http://localhost:5678
2. Login: admin / robo-researcher-2000
3. Go to "Workflows" → "Import from file"
4. Select `ux-research-complete-17step-workflow.json`
5. Configure necessary credentials

### Via CLI (if available)

```bash
# Import main workflow
n8n import:workflow --file=workflows/ux-research-complete-17step-workflow.json

# Import test workflow
n8n import:workflow --file=workflows/ux-research-test-simple-workflow.json

# Import all workflows
for file in workflows/ux-research-*.json; do
    n8n import:workflow --file="$file"
done
```

## ⚙️ Configuration

### Required Environment Variables

```bash
# APIs
OPENROUTER_API_KEY=your_key_here
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
WIKIJS_API_TOKEN=your_token_here

# SMTP
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_password_here

# URLs
N8N_WEBHOOK_URL=http://localhost:5678/webhook/robo-researcher
MINIO_ENDPOINT=http://localhost:9000
WIKIJS_URL=http://localhost:3000
```

### Credentials in n8n

Configure the following credentials in n8n:

1. **OpenRouter API**
   - Type: HTTP Request
   - URL: https://openrouter.ai/api/v1
   - Headers: Authorization: Bearer YOUR_API_KEY

2. **MinIO S3**
   - Type: S3
   - Endpoint: http://localhost:9000
   - Access Key: minioadmin
   - Secret Key: minioadmin

3. **SMTP**
   - Type: SMTP
   - Host: smtp.gmail.com
   - Port: 587
   - User: <EMAIL>
   - Password: your_app_password

4. **Wiki.js API**
   - Type: HTTP Request
   - URL: http://localhost:3000/graphql
   - Headers: Authorization: Bearer YOUR_TOKEN

## 🧪 Testing

### Test Workflow

```json
{
  "email": "<EMAIL>",
  "transcription": "This is a test transcription...",
  "project_name": "Test Project",
  "api_keys": {
    "openrouter": "your_key",
    "smtp_password": "your_password"
  }
}
```

### Testing Endpoints

- **Webhook URL**: `http://localhost:5678/webhook/robo-researcher`
- **Health Check**: `http://localhost:5678/healthz`
- **Metrics**: `http://localhost:5678/metrics`

## 📊 Monitoring

### Workflow Logs

```bash
# View n8n logs
docker-compose logs -f robo-researcher-n8n

# View executions in n8n UI
# http://localhost:5678 → Executions
```

### Metrics

n8n exposes Prometheus metrics at `/metrics`:
- Number of executions
- Execution time
- Errors per node
- Workflow status

## 🔄 Versioning

### Workflow Backup

```bash
# Export current workflow
curl -X GET "http://localhost:5678/api/v1/workflows/1/export" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  > workflows/backup/main-workflow-$(date +%Y%m%d).json
```

### Version Control

Workflows are versioned using Git:
- `main-workflow-v1.0.json` - Initial version
- `main-workflow-v1.1.json` - Minor improvements
- `main-workflow-v2.0.json` - Major changes

## 🐛 Troubleshooting

### Common Issues

1. **Webhook not responding**
   - Verify that n8n is running
   - Check webhook URL
   - Review n8n logs

2. **Credential errors**
   - Verify configuration in n8n UI
   - Check environment variables
   - Validate API tokens

3. **Execution timeout**
   - Increase timeout in configuration
   - Optimize Python scripts
   - Verify server resources

### Debug Mode

Enable debug in n8n:

```bash
# In docker-compose.yml
environment:
  - N8N_LOG_LEVEL=debug
```

## 📚 Additional Documentation

- [n8n Documentation](https://docs.n8n.io/)
- [Workflow Best Practices](https://docs.n8n.io/workflows/best-practices/)
- [Custom Nodes Development](https://docs.n8n.io/nodes/creating-nodes/)

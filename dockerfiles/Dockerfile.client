# ROBO-RESEARCHER-2000 Client Dockerfile
FROM nginx:alpine

# Add labels
LABEL org.opencontainers.image.source=https://github.com/robo-researcher-2000/robo-researcher-2000
LABEL org.opencontainers.image.description="ROBO-RESEARCHER-2000 Client Web Interface"
LABEL org.opencontainers.image.licenses=MIT

# Install curl for health checks
RUN apk add --no-cache curl

# Copy client files
COPY . /usr/share/nginx/html/

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Create health check endpoint
RUN echo 'healthy' > /usr/share/nginx/html/health

# Set proper permissions
RUN chown -R nginx:nginx /usr/share/nginx/html && \
    chmod -R 755 /usr/share/nginx/html

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD curl -f http://localhost:80/health || exit 1

# Expose port
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]

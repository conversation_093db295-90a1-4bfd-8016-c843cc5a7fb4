# ROBO-RESEARCHER-2000 n8n Dockerfile
FROM n8nio/n8n:latest

# Add labels
LABEL org.opencontainers.image.source=https://github.com/robo-researcher-2000/robo-researcher-2000
LABEL org.opencontainers.image.description="ROBO-RESEARCHER-2000 n8n Workflow Engine"
LABEL org.opencontainers.image.licenses=MIT

# Switch to root to install packages
USER root

# Install additional packages
RUN apk add --no-cache \
    curl \
    python3 \
    py3-pip \
    git \
    && rm -rf /var/cache/apk/*

# Install additional n8n community nodes
RUN npm install -g \
    n8n-nodes-base@latest \
    && npm cache clean --force

# Install Python packages for custom nodes
RUN pip3 install --no-cache-dir \
    requests \
    pandas \
    numpy \
    nltk \
    spacy

# Create custom nodes directory
RUN mkdir -p /home/<USER>/.n8n/custom

# Copy custom workflows and nodes
COPY workflows/ /home/<USER>/.n8n/workflows/
COPY custom-nodes/ /home/<USER>/.n8n/custom/

# Set proper ownership
RUN chown -R node:node /home/<USER>/.n8n

# Switch back to node user
USER node

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:5678/healthz || exit 1

# Expose port
EXPOSE 5678

# Start n8n
CMD ["n8n", "start"]

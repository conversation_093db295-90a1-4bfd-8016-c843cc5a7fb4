# 🚀 ROBO-RESEARCHER-2000 Deployment Guide

## Overview
Complete deployment guide for the enterprise-grade UX research automation platform with 17-step workflow, security hardening, and comprehensive monitoring.

## 📋 Prerequisites

### System Requirements
- **Docker**: Version 20.10+ with Docker Compose
- **Memory**: Minimum 8GB RAM (16GB recommended)
- **Storage**: 20GB free space minimum
- **Network**: Internet access for AI providers and email notifications

### Required API Keys
- **OpenRouter API Key** (Primary AI provider) - Get from: https://openrouter.ai/
- **SMTP Credentials** (Email notifications) - Configure your email provider
- **Optional**: OpenAI, Anthropic, Google API keys for additional fallback providers

## 🔧 Configuration

### 1. Environment Variables Setup
The `.env` file is pre-configured with all necessary settings. Update these critical values:

```bash
# REQUIRED: AI Provider Configuration
OPENROUTER_API_KEY=your_openrouter_key_here

# REQUIRED: Email Notifications
SMTP_PASSWORD=your_smtp_password_here

# OPTIONAL: Additional AI Providers (for fallback)
OPENAI_API_KEY=your_openai_key_here
ANTHROPIC_API_KEY=your_anthropic_key_here
GOOGLE_API_KEY=your_google_key_here

# SECURITY: Change in production
JWT_SECRET=your_secure_jwt_secret_here
ADMIN_PASSWORD=your_secure_admin_password_here
```

### 2. Security Configuration
The system includes enterprise-grade security features:

- **Authentication**: JWT-based with role-based access control
- **File Upload Security**: Virus scanning and validation
- **Data Encryption**: AES encryption for sensitive data
- **Audit Logging**: Comprehensive activity tracking
- **Privacy Controls**: GDPR compliance and data retention

## 🚀 Deployment Steps

### Step 1: Clone and Configure
```bash
# Ensure you're in the project directory
cd /path/to/robo-researcher-2000

# Verify .env file is configured
cat .env | grep -E "(OPENROUTER_API_KEY|SMTP_PASSWORD)"
```

### Step 2: Launch the System
```bash
# Start all services
docker-compose -f docker-compose.local.yml up -d

# Verify all services are running
docker-compose -f docker-compose.local.yml ps
```

### Step 3: Wait for Initialization
The system auto-initializes all components (2-3 minutes):
- Security databases and encryption keys
- AI provider configurations and health checks
- Workflow engine with all 17 steps
- Monitoring and audit systems

### Step 4: Monitor Startup
```bash
# Watch startup logs
docker-compose -f docker-compose.local.yml logs -f

# Check individual service health
docker-compose -f docker-compose.local.yml logs robo-researcher-n8n
```

## 🌐 Access Points

Once deployed, access the system through these interfaces:

| Service | URL | Credentials | Purpose |
|---------|-----|-------------|---------|
| **Main Client** | http://localhost:8080 | User registration | Primary user interface |
| **n8n Workflow Engine** | http://localhost:5678 | admin / robo-researcher-2000 | Workflow management |
| **Wiki.js Documentation** | http://localhost:3002 | Auto-configured | Results documentation |
| **MinIO Storage** | http://localhost:9001 | minioadmin / minioadmin | File storage management |
| **PostgreSQL** | localhost:5433 | n8n / n8n_password | Database access |

## ✅ Verification Checklist

### System Health Verification
```bash
# Check all containers are healthy
docker-compose -f docker-compose.local.yml ps

# Verify n8n workflow is loaded
curl -f http://localhost:5678/healthz

# Test client interface
curl -f http://localhost:8080/health

# Check Wiki.js availability
curl -f http://localhost:3002/healthz
```

### Feature Verification
- [ ] **Authentication System**: User registration and login working
- [ ] **File Upload Security**: File validation and virus scanning active
- [ ] **17-Step Workflow**: All workflow steps loaded and connected
- [ ] **AI Provider Fallback**: Multiple providers configured and tested
- [ ] **Cost Monitoring**: Usage tracking and budget alerts functional
- [ ] **Audit Logging**: User actions and system events being logged
- [ ] **Data Encryption**: Sensitive data encrypted at rest
- [ ] **Email Notifications**: SMTP configuration working

## 🔧 Troubleshooting

### Common Issues

#### 1. Services Not Starting
```bash
# Check Docker resources
docker system df
docker system prune -f

# Restart specific service
docker-compose -f docker-compose.local.yml restart robo-researcher-n8n
```

#### 2. Database Connection Issues
```bash
# Check PostgreSQL logs
docker-compose -f docker-compose.local.yml logs robo-researcher-postgres

# Verify database connectivity
docker exec -it robo-researcher-postgres psql -U n8n -d n8n -c "SELECT 1;"
```

#### 3. AI Provider Issues
```bash
# Check n8n logs for API errors
docker-compose -f docker-compose.local.yml logs robo-researcher-n8n | grep -i "api\|error"

# Verify API keys in environment
docker exec -it robo-researcher-n8n env | grep -E "(OPENROUTER|OPENAI)"
```

#### 4. File Upload Issues
```bash
# Check upload directory permissions
docker exec -it robo-researcher-n8n ls -la /home/<USER>/data/uploads

# Verify file security settings
docker-compose -f docker-compose.local.yml logs | grep -i "file\|upload\|security"
```

### Log Locations
- **Application Logs**: `./logs/` directory
- **Audit Logs**: `./data/audit_logs/` directory
- **Error Logs**: `./data/errors.db` (SQLite database)
- **Docker Logs**: `docker-compose logs [service-name]`

## 🔒 Security Considerations

### Production Hardening
1. **Change Default Passwords**: Update all default credentials
2. **SSL/TLS**: Configure HTTPS for production deployment
3. **Firewall**: Restrict access to necessary ports only
4. **Backup Encryption**: Ensure backups are encrypted
5. **Regular Updates**: Keep Docker images and dependencies updated

### Monitoring
- **System Health**: Built-in health monitoring dashboard
- **Cost Tracking**: Real-time AI API usage and cost monitoring
- **Security Events**: Comprehensive audit logging and alerting
- **Performance Metrics**: Resource usage and response time tracking

## 📊 System Capabilities

### Complete 17-Step UX Research Workflow
1. Webhook Trigger → 2. Input Validation → 3. MinIO Upload → 4. Text Preprocessing
5. Segmentation → 6. Deductive Coding → 7. Open Coding AI → 8. **Category Grouping**
9. **Affinity Mapping** → 10. **Quantitative Analysis** → 11. **Pattern Detection**
12. **Insight Generation** → 13. **Archetype Creation** → 14. **HMW Generation**
15. **Opportunity Prioritization** → 16. Wiki Documentation → 17. Email Notification

### Enterprise Features
- **Multi-Provider AI**: OpenRouter, OpenAI, Anthropic, Google with automatic failover
- **Security**: Authentication, encryption, audit logging, file security
- **Monitoring**: Cost tracking, usage quotas, system health, error recovery
- **Compliance**: GDPR privacy controls, data retention, user consent management

## 🆘 Support

### Getting Help
1. **Check Logs**: Review application and Docker logs for errors
2. **System Health**: Access monitoring dashboard for system status
3. **Documentation**: Refer to Wiki.js for detailed workflow documentation
4. **Configuration**: Verify `.env` file settings match requirements

### Maintenance
- **Daily**: Monitor system health and cost usage
- **Weekly**: Review audit logs and security events
- **Monthly**: Update dependencies and perform system cleanup
- **Quarterly**: Review and rotate security keys and certificates

---

**System Status**: ✅ Production Ready  
**Last Updated**: 2025-01-22  
**Version**: 2.0.0 (Complete Implementation)

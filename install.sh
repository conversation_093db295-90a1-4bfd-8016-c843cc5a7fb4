#!/bin/bash

# ROBO-RESEARCHER-2000 Interactive Installation & Management Script
# Comprehensive script to install, configure, and manage the system

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${CYAN}$1${NC}"
}

print_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# Determine which docker compose command to use
if command -v docker-compose &> /dev/null; then
    COMPOSE_CMD="docker-compose"
else
    COMPOSE_CMD="docker compose"
fi

COMPOSE_FILE="docker-compose.local.yml"
ENV_FILE=".env"

# Detect Docker Compose command
if command -v docker-compose &> /dev/null; then
    COMPOSE_CMD="docker-compose"
elif docker compose version &> /dev/null; then
    COMPOSE_CMD="docker compose"
else
    print_error "Docker Compose not found!"
    exit 1
fi

# Function to prompt for user input
prompt_input() {
    local prompt="$1"
    local default="$2"
    local secret="$3"

    if [ "$secret" = "true" ]; then
        echo -n "$prompt: "
        read -s value
        echo
    else
        if [ -n "$default" ]; then
            echo -n "$prompt [$default]: "
        else
            echo -n "$prompt: "
        fi
        read value
    fi

    if [ -z "$value" ] && [ -n "$default" ]; then
        value="$default"
    fi

    echo "$value"
}

# Function to check prerequisites
check_prerequisites() {
    print_header "🔍 Checking Prerequisites"

    local missing_deps=()

    # Check Docker
    if ! command -v docker &> /dev/null; then
        missing_deps+=("docker")
    fi

    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        missing_deps+=("docker-compose")
    fi

    # Check curl
    if ! command -v curl &> /dev/null; then
        missing_deps+=("curl")
    fi

    if [ ${#missing_deps[@]} -gt 0 ]; then
        print_error "Missing required dependencies: ${missing_deps[*]}"
        echo ""
        echo "Please install the missing dependencies and try again."
        echo ""
        echo "Ubuntu/Debian:"
        echo "  sudo apt update && sudo apt install -y docker.io docker-compose curl"
        echo ""
        echo "CentOS/RHEL:"
        echo "  sudo yum install -y docker docker-compose curl"
        echo ""
        exit 1
    fi

    print_success "All prerequisites are installed"
}

# Function for interactive installation
interactive_install() {
    clear
    echo "🚀 ROBO-RESEARCHER-2000 Interactive Installation"
    echo "================================================="
    echo ""
    echo "This script will guide you through the complete setup process:"
    echo "  1. Check prerequisites"
    echo "  2. Deploy Docker stack"
    echo "  3. Configure API keys and credentials"
    echo "  4. Set up MinIO storage"
    echo "  5. Import n8n workflows"
    echo "  6. Validate the installation"
    echo ""

    read -p "Press Enter to continue or Ctrl+C to cancel..."
    echo ""

    # Step 1: Check prerequisites
    print_step "1/6 Checking Prerequisites"
    check_prerequisites
    echo ""

    # Step 2: Deploy Docker stack
    print_step "2/6 Deploying Docker Stack"
    deploy_stack
    echo ""

    # Step 3: Configure credentials
    print_step "3/6 Configuring API Keys and Credentials"
    configure_credentials
    echo ""

    # Step 4: Set up MinIO
    print_step "4/6 Setting Up MinIO Storage"
    setup_minio_storage
    echo ""

    # Step 5: Import workflows
    print_step "5/6 Configuring n8n Workflows"
    configure_workflows
    echo ""

    # Step 6: Validate installation
    print_step "6/6 Validating Installation"
    validate_installation
    echo ""

    # Final success message
    print_header "🎉 Installation Complete!"
    echo ""
    show_final_instructions
}

# Function to deploy the Docker stack
deploy_stack() {
    print_status "Starting ROBO-RESEARCHER-2000 services..."
    echo ""
    echo "⏳ This may take 2-3 minutes for first-time setup..."
    echo ""

    # Stop any existing containers quietly
    if docker ps -q --filter "name=robo-researcher" | grep -q .; then
        print_status "Stopping existing containers..."
        $COMPOSE_CMD -f $COMPOSE_FILE down > /dev/null 2>&1
    fi

    # Start services with minimal output
    print_status "Creating and starting containers..."
    if ! $COMPOSE_CMD -f $COMPOSE_FILE up -d > /tmp/docker-output.log 2>&1; then
        print_error "Failed to start Docker services!"
        echo "Error details:"
        cat /tmp/docker-output.log
        return 1
    fi

    # Wait for services to be ready with progress indicator
    print_status "Waiting for services to initialize..."
    local retries=0
    local max_retries=18
    local services_ready=0

    while [ $retries -lt $max_retries ]; do
        services_ready=0

        # Check each service
        if curl -s http://localhost:8080/health > /dev/null 2>&1; then
            ((services_ready++))
        fi
        if curl -s http://localhost:5678/healthz > /dev/null 2>&1; then
            ((services_ready++))
        fi
        if curl -s http://localhost:9002/minio/health/live > /dev/null 2>&1; then
            ((services_ready++))
        fi

        # Show progress
        local progress=$((services_ready * 100 / 3))
        printf "\r   Progress: %d%% (%d/3 services ready)" "$progress" "$services_ready"

        if [ $services_ready -eq 3 ]; then
            echo ""
            print_success "All services are healthy and ready!"
            return 0
        fi

        sleep 10
        ((retries++))
    done

    echo ""
    if [ $services_ready -gt 0 ]; then
        print_warning "$services_ready/3 services are ready. Continuing with configuration..."
    else
        print_error "Services are taking longer than expected to start."
        echo "You can check service status later with: ./install.sh health"
    fi
}

# Function to safely update environment file
update_env_var() {
    local var_name="$1"
    local var_value="$2"
    local env_file="$3"

    # Escape special characters for sed
    local escaped_value=$(printf '%s\n' "$var_value" | sed 's/[[\.*^$()+?{|]/\\&/g')

    # Create backup
    cp "$env_file" "${env_file}.backup"

    # Update or add the variable
    if grep -q "^${var_name}=" "$env_file"; then
        sed -i "s|^${var_name}=.*|${var_name}=${escaped_value}|" "$env_file"
    else
        echo "${var_name}=${escaped_value}" >> "$env_file"
    fi
}

# Function to validate API key format
validate_openrouter_key() {
    local key="$1"
    if [[ $key =~ ^sk-or-v1-[a-zA-Z0-9]{64}$ ]]; then
        return 0
    else
        return 1
    fi
}

# Function to configure credentials
configure_credentials() {
    print_status "Configuring API keys and credentials..."
    echo ""
    echo "📋 Required credentials for full functionality:"
    echo "  🤖 OpenRouter API Key (required for AI analysis)"
    echo "  📧 SMTP credentials (optional for email notifications)"
    echo ""

    # Check if .env file exists
    if [ ! -f "$ENV_FILE" ]; then
        print_error ".env file not found!"
        return 1
    fi

    # OpenRouter API Key
    echo "🔑 OpenRouter Configuration"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "📍 Get your API key from: https://openrouter.ai/keys"
    echo "💡 Format: sk-or-v1-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
    echo ""

    current_key=$(grep "OPENROUTER_API_KEY=" "$ENV_FILE" 2>/dev/null | cut -d'=' -f2)
    if [ "$current_key" = "your_openrouter_api_key_here" ] || [ -z "$current_key" ]; then
        while true; do
            echo -n "🔐 Enter your OpenRouter API key (input hidden): "
            read -s openrouter_key
            echo ""

            if [ -z "$openrouter_key" ]; then
                echo ""
                echo "⚠️  No API key entered. You can add it later by:"
                echo "   1. Editing the .env file manually"
                echo "   2. Running: ./install.sh config"
                print_warning "OpenRouter API key not configured - AI features will not work"
                break
            elif validate_openrouter_key "$openrouter_key"; then
                update_env_var "OPENROUTER_API_KEY" "$openrouter_key" "$ENV_FILE"
                print_success "✅ OpenRouter API key configured and validated"
                break
            else
                print_error "❌ Invalid API key format. Please check and try again."
                echo "   Expected format: sk-or-v1-[64 characters]"
                echo ""
                echo "Try again? (y/N)"
                read -n 1 -r retry
                echo ""
                if [[ ! $retry =~ ^[Yy]$ ]]; then
                    print_warning "OpenRouter API key not configured"
                    break
                fi
            fi
        done
    else
        print_success "✅ OpenRouter API key already configured"
    fi

    echo ""

    # SMTP Configuration
    echo "📧 SMTP Email Configuration (Optional)"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "📍 Server: mail.stargety.com:465 (SSL)"
    echo "📍 Username: <EMAIL>"
    echo ""

    current_smtp=$(grep "SMTP_PASSWORD=" "$ENV_FILE" 2>/dev/null | cut -d'=' -f2)
    if [ "$current_smtp" = "your_smtp_password_here" ] || [ -z "$current_smtp" ]; then
        echo "Configure email notifications? (y/N): "
        read -n 1 -r configure_smtp
        echo ""

        if [[ $configure_smtp =~ ^[Yy]$ ]]; then
            echo -n "🔐 Enter SMTP <NAME_EMAIL> (input hidden): "
            read -s smtp_password
            echo ""

            if [ -n "$smtp_password" ]; then
                update_env_var "SMTP_PASSWORD" "$smtp_password" "$ENV_FILE"
                print_success "✅ SMTP password configured"
            else
                print_warning "SMTP password not configured"
            fi
        else
            print_status "📧 SMTP configuration skipped (you can configure later with: ./install.sh config)"
        fi
    else
        print_success "✅ SMTP password already configured"
    fi

    echo ""
    print_success "🎉 Credential configuration complete!"
}

# Function to verify MinIO container and tools
verify_minio_container() {
    # Check if container is running
    if ! docker ps --filter "name=robo-researcher-minio" --filter "status=running" | grep -q "robo-researcher-minio"; then
        print_error "❌ MinIO container is not running"
        return 1
    fi

    # Test basic shell access
    if ! docker exec robo-researcher-minio sh -c "echo 'test'" > /dev/null 2>&1; then
        print_error "❌ Cannot execute commands in MinIO container"
        return 1
    fi

    return 0
}

# Function to setup MinIO client configuration
setup_minio_client() {
    print_status "Configuring MinIO client..."

    # Configure mc client to connect to local MinIO instance
    if docker exec robo-researcher-minio mc alias set local http://localhost:9000 minioadmin minioadmin 2>/dev/null; then
        echo "   ✅ MinIO client configured"
        return 0
    else
        print_error "   ❌ Failed to configure MinIO client"
        return 1
    fi
}

# Function to create and verify MinIO bucket and prefixes
create_minio_directories() {
    local bucket_name="robo-researcher-data"
    local directories=("transcripts" "results" "presentations" "temp" "exports")
    local created_count=0
    local failed_dirs=()

    print_status "Creating MinIO bucket and object prefixes..."

    # Setup MinIO client first
    if ! setup_minio_client; then
        print_error "❌ Cannot proceed without MinIO client configuration"
        return 1
    fi

    # Create main bucket using MinIO client
    if docker exec robo-researcher-minio mc mb local/$bucket_name 2>/dev/null; then
        print_success "✅ Created MinIO bucket: $bucket_name"
    else
        # Check if bucket already exists
        if docker exec robo-researcher-minio mc ls local/ 2>/dev/null | grep -q "$bucket_name"; then
            print_success "✅ MinIO bucket already exists: $bucket_name"
        else
            print_error "❌ Failed to create MinIO bucket: $bucket_name"
            return 1
        fi
    fi

    # Create object prefixes by uploading placeholder files
    for dir in "${directories[@]}"; do
        local placeholder_content="# ROBO-RESEARCHER-2000 Directory Placeholder\n# This file creates the $dir/ prefix in MinIO\n# Created: $(date)\n"
        local placeholder_file="/tmp/.${dir}_placeholder"

        # Create placeholder file
        echo -e "$placeholder_content" | docker exec -i robo-researcher-minio sh -c "cat > $placeholder_file"

        # Upload placeholder to create the prefix
        if docker exec robo-researcher-minio mc cp "$placeholder_file" "local/$bucket_name/$dir/.placeholder" 2>/dev/null; then
            echo "   ✅ Created /$dir/ prefix in MinIO bucket"
            ((created_count++))

            # Clean up placeholder file
            docker exec robo-researcher-minio rm -f "$placeholder_file" 2>/dev/null
        else
            echo "   ❌ Failed to create /$dir/ prefix"
            failed_dirs+=("$dir")
            docker exec robo-researcher-minio rm -f "$placeholder_file" 2>/dev/null
        fi
    done

    # Verify bucket structure
    echo ""
    print_status "Verifying MinIO bucket structure..."
    local bucket_contents=$(docker exec robo-researcher-minio mc ls local/$bucket_name --recursive 2>/dev/null)
    if [ -n "$bucket_contents" ]; then
        echo "   ✅ Bucket contents verified:"
        echo "$bucket_contents" | sed 's/^/      /'
    else
        print_warning "   ⚠️  Bucket appears empty or inaccessible"
    fi

    # Report results
    echo ""
    if [ $created_count -eq ${#directories[@]} ]; then
        print_success "✅ All $created_count MinIO prefixes created successfully"
        return 0
    else
        print_warning "⚠️  Created $created_count/${#directories[@]} MinIO prefixes"
        if [ ${#failed_dirs[@]} -gt 0 ]; then
            echo "   Failed prefixes: ${failed_dirs[*]}"
        fi
        return 1
    fi
}

# Function to test MinIO storage functionality
test_minio_storage() {
    local bucket_name="robo-researcher-data"
    local test_content="ROBO-RESEARCHER-2000 storage test - $(date)"
    local test_file="/tmp/minio_test.txt"

    print_status "Testing MinIO storage functionality..."

    # Create test file
    echo "$test_content" | docker exec -i robo-researcher-minio sh -c "cat > $test_file"

    # Test upload to MinIO bucket
    if docker exec robo-researcher-minio mc cp "$test_file" "local/$bucket_name/test.txt" >/dev/null 2>&1; then
        echo "   ✅ Upload test successful"
    else
        # Try alternative upload method
        if docker exec robo-researcher-minio sh -c "cat $test_file" | docker exec -i robo-researcher-minio mc pipe "local/$bucket_name/test.txt" >/dev/null 2>&1; then
            echo "   ✅ Upload test successful (alternative method)"
        else
            print_warning "   ⚠️  Upload test failed (bucket prefixes still work)"
            docker exec robo-researcher-minio rm -f "$test_file" 2>/dev/null
            # Don't return error since prefixes were created successfully
        fi
    fi

    # Test download from MinIO bucket
    local download_file="/tmp/minio_download_test.txt"
    if docker exec robo-researcher-minio mc cp "local/$bucket_name/test.txt" "$download_file" 2>/dev/null; then
        echo "   ✅ Download test successful"
    else
        print_error "   ❌ Download test failed"
        docker exec robo-researcher-minio rm -f "$test_file" 2>/dev/null
        return 1
    fi

    # Verify content integrity
    local downloaded_content=$(docker exec robo-researcher-minio cat "$download_file" 2>/dev/null)
    if [ "$downloaded_content" = "$test_content" ]; then
        echo "   ✅ Content integrity test successful"
    else
        print_error "   ❌ Content integrity test failed"
        docker exec robo-researcher-minio rm -f "$test_file" "$download_file" 2>/dev/null
        return 1
    fi

    # Test bucket listing
    local bucket_objects=$(docker exec robo-researcher-minio mc ls "local/$bucket_name" --recursive 2>/dev/null | wc -l)
    if [ "$bucket_objects" -ge 5 ]; then
        echo "   ✅ Bucket listing test successful ($bucket_objects objects)"
    else
        print_warning "   ⚠️  Bucket contains only $bucket_objects objects"
    fi

    # Clean up test files
    docker exec robo-researcher-minio rm -f "$test_file" "$download_file" 2>/dev/null

    return 0
}

# Function to set up MinIO storage
setup_minio_storage() {
    print_status "Setting up MinIO object storage..."
    echo ""
    echo "📁 Configuring file storage for transcripts, results, and presentations..."
    echo ""

    # Verify MinIO container is ready
    if ! verify_minio_container; then
        print_error "❌ MinIO container verification failed"
        return 1
    fi

    # Wait for MinIO to be ready with progress indicator
    local retries=0
    local max_retries=15

    print_status "Waiting for MinIO service to be ready..."
    while [ $retries -lt $max_retries ]; do
        if curl -s http://localhost:9002/minio/health/live > /dev/null 2>&1; then
            break
        fi

        local dots=""
        for i in $(seq 1 $((retries % 4))); do
            dots="${dots}."
        done
        printf "\r   Checking MinIO health%s   " "$dots"

        sleep 2
        ((retries++))
    done

    echo ""

    if [ $retries -eq $max_retries ]; then
        print_error "❌ MinIO is not responding after $((max_retries * 2)) seconds"
        echo ""
        echo "🔧 Troubleshooting steps:"
        echo "   1. Check if MinIO container is running: docker ps | grep minio"
        echo "   2. Check MinIO logs: docker logs robo-researcher-minio"
        echo "   3. Try restarting: ./install.sh restart"
        return 1
    fi

    print_success "✅ MinIO service is ready"
    echo ""

    # Create directory structure with verification
    if ! create_minio_directories; then
        print_warning "⚠️  Some directories failed to create, but continuing..."
    fi

    echo ""

    # Test storage functionality
    if test_minio_storage; then
        print_success "✅ Storage functionality test passed"
    else
        print_warning "⚠️  Storage functionality test failed"
    fi

    echo ""
    print_success "🎉 MinIO storage configured successfully!"
    echo ""
    echo "📊 Storage Access Information:"
    echo "   🌐 MinIO Console: http://localhost:9003"
    echo "   🔐 Username: minioadmin"
    echo "   🔐 Password: minioadmin"
    echo "   📁 Bucket: robo-researcher-data"
    echo ""
    echo "📁 Available Directories:"
    echo "   • /transcripts  - User interview transcriptions"
    echo "   • /results      - Analysis results and reports"
    echo "   • /presentations - Generated presentation files"
    echo "   • /temp         - Temporary processing files"
    echo "   • /exports      - Exported data and backups"
}

# Function to setup n8n owner account
setup_n8n_owner_account() {
    print_status "Setting up n8n owner account..."

    # Check if n8n is ready
    local retries=0
    local max_retries=15

    print_status "Waiting for n8n to be fully ready..."
    while [ $retries -lt $max_retries ]; do
        if curl -s http://localhost:5678/healthz > /dev/null 2>&1; then
            break
        fi
        printf "\r   Waiting for n8n... (%d/%d)" "$((retries + 1))" "$max_retries"
        sleep 2
        ((retries++))
    done

    echo ""

    if [ $retries -eq $max_retries ]; then
        print_warning "⚠️  n8n is taking longer than expected to start"
        return 1
    fi

    # Try to run the automated setup script
    if [ -f "./scripts/setup-n8n-owner.sh" ]; then
        print_status "Running automated n8n owner setup..."
        if ./scripts/setup-n8n-owner.sh > /tmp/n8n-setup.log 2>&1; then
            print_success "✅ n8n owner account configured automatically"
            return 0
        else
            print_warning "⚠️  Automated setup failed, manual setup required"
            return 1
        fi
    else
        print_warning "⚠️  Setup script not found, manual setup required"
        return 1
    fi
}

# Function to configure workflows
configure_workflows() {
    print_status "Configuring n8n workflows..."
    echo ""
    echo "� n8n Workflow Setup Guide"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo ""
    # Try to set up n8n owner account
    local setup_success=false
    if setup_n8n_owner_account; then
        setup_success=true
    fi

    echo ""
    echo "📍 Step 1: Access n8n Interface"
    echo "   🌐 URL: http://localhost:5678"

    if [ "$setup_success" = true ]; then
        echo "   🔐 Email: <EMAIL>"
        echo "   🔐 Password: robo-researcher-2000"
        echo "   ✅ Owner account configured automatically"
    else
        echo "   🔧 Manual Setup Required:"
        echo "      1. Complete the initial setup wizard"
        echo "      2. Create owner account with your preferred credentials"
        echo "      3. Recommended: <EMAIL> / robo-researcher-2000"
    fi
    echo ""
    echo "📍 Step 2: Import Workflows (2 files)"
    echo "   📄 workflows/test-workflow-simple.json (for testing)"
    echo "   📄 workflows/main-workflow-complete.json (main analysis pipeline)"
    echo ""
    echo "📍 Step 3: Configure Credentials (3 required)"
    echo "   🤖 OpenRouter API (for AI analysis)"
    echo "   📧 SMTP Email (for notifications)"
    echo "   📁 MinIO S3 Storage (for file handling)"
    echo ""

    # Check if n8n is accessible
    if curl -s http://localhost:5678/healthz > /dev/null 2>&1; then
        print_success "✅ n8n is accessible and ready"
        echo ""
        echo "🚀 Ready to open n8n interface?"
        if [ "$setup_success" = true ]; then
            echo "   This will open your web browser to the n8n login page."
            echo "   You can immediately log in with the configured credentials."
        else
            echo "   This will open your web browser to complete the n8n setup."
            echo "   You'll need to complete the initial setup wizard."
        fi
        echo ""
        echo "Open n8n now? (Y/n): "
        read -n 1 -r open_n8n
        echo ""

        if [[ ! $open_n8n =~ ^[Nn]$ ]]; then
            print_status "🌐 Opening n8n interface..."

            # Try different browser opening methods
            if command -v xdg-open &> /dev/null; then
                xdg-open "http://localhost:5678" &
                print_success "✅ Browser opened"
            elif command -v open &> /dev/null; then
                open "http://localhost:5678" &
                print_success "✅ Browser opened"
            elif command -v start &> /dev/null; then
                start "http://localhost:5678" &
                print_success "✅ Browser opened"
            else
                print_warning "⚠️  Could not auto-open browser"
                echo "   Please manually open: http://localhost:5678"
            fi

            echo ""
            if [ "$setup_success" = true ]; then
                echo "💡 Login Instructions:"
                echo "   1. Use email: <EMAIL>"
                echo "   2. Use password: robo-researcher-2000"
                echo "   3. No setup wizard - direct access to workflows!"
            else
                echo "💡 Setup Instructions:"
                echo "   1. Complete the initial setup wizard"
                echo "   2. Create your owner account"
                echo "   3. Recommended credentials: <EMAIL> / robo-researcher-2000"
            fi
            echo ""
            echo "⏳ Waiting 5 seconds for browser to load..."
            sleep 5
        fi
    else
        print_warning "⚠️  n8n is not responding"
        echo "   You can access it later at: http://localhost:5678"
    fi

    echo ""
    echo "📚 Additional Resources:"
    echo "   📖 Detailed setup guide: setup-n8n.md"
    echo "   📋 Workflow documentation: workflows/workflow-documentation.md"
    echo "   🔧 Configuration help: ./install.sh config"
    echo ""

    print_success "🎉 Workflow configuration guidance provided!"
}

# Function to validate installation
validate_installation() {
    print_status "Running comprehensive system validation..."
    echo ""
    echo "🔍 System Health Check"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo ""

    local errors=0
    local warnings=0

    # Check container health with detailed status
    echo "🐳 Container Health Status:"
    local containers=(
        "robo-researcher-postgres:Database"
        "robo-researcher-redis:Cache"
        "robo-researcher-minio:Storage"
        "robo-researcher-n8n:Workflow Engine"
        "robo-researcher-wikijs:Documentation"
        "robo-researcher-client:Web Interface"
    )

    for container_info in "${containers[@]}"; do
        local container_name=$(echo "$container_info" | cut -d: -f1)
        local container_desc=$(echo "$container_info" | cut -d: -f2)

        if docker ps --filter "name=$container_name" --filter "status=running" | grep -q "$container_name"; then
            local health=$(docker inspect --format='{{.State.Health.Status}}' "$container_name" 2>/dev/null || echo "unknown")
            if [ "$health" = "healthy" ]; then
                echo "   ✅ $container_desc ($container_name) - Healthy"
            elif [ "$health" = "starting" ]; then
                echo "   ⏳ $container_desc ($container_name) - Starting"
                ((warnings++))
            else
                echo "   ✅ $container_desc ($container_name) - Running"
            fi
        else
            echo "   ❌ $container_desc ($container_name) - Not Running"
            ((errors++))
        fi
    done

    echo ""
    echo "🌐 Service Connectivity Test:"

    # Test services with timeout and better error reporting
    local services=(
        "http://localhost:8080/health:Client Application:Web Interface"
        "http://localhost:5678/healthz:n8n Workflow Engine:Automation Platform"
        "http://localhost:9002/minio/health/live:MinIO Storage API:File Storage"
        "http://localhost:9003:MinIO Console:Storage Management"
        "http://localhost:3002:Wiki.js:Documentation Platform"
    )

    for service_info in "${services[@]}"; do
        local url=$(echo "$service_info" | cut -d: -f1)
        local name=$(echo "$service_info" | cut -d: -f2)
        local desc=$(echo "$service_info" | cut -d: -f3)

        if timeout 5 curl -s "$url" > /dev/null 2>&1; then
            echo "   ✅ $name - Accessible"
        else
            echo "   ❌ $name - Not Responding"
            echo "      URL: $url"
            ((errors++))
        fi
    done

    echo ""
    echo "🔧 Configuration Check:"

    # Check environment configuration
    if [ -f ".env" ]; then
        local openrouter_key=$(grep "OPENROUTER_API_KEY=" .env | cut -d'=' -f2)
        if [ "$openrouter_key" != "your_openrouter_api_key_here" ] && [ -n "$openrouter_key" ]; then
            echo "   ✅ OpenRouter API Key - Configured"
        else
            echo "   ⚠️  OpenRouter API Key - Not Configured"
            ((warnings++))
        fi

        local smtp_pass=$(grep "SMTP_PASSWORD=" .env | cut -d'=' -f2)
        if [ "$smtp_pass" != "your_smtp_password_here" ] && [ -n "$smtp_pass" ]; then
            echo "   ✅ SMTP Password - Configured"
        else
            echo "   ⚠️  SMTP Password - Not Configured (optional)"
        fi
    else
        echo "   ❌ Environment file (.env) - Missing"
        ((errors++))
    fi

    echo ""
    echo "📊 Validation Summary:"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

    if [ $errors -eq 0 ] && [ $warnings -eq 0 ]; then
        print_success "🎉 Perfect! All systems are operational and properly configured."
        echo ""
        echo "✅ Ready to use ROBO-RESEARCHER-2000!"
    elif [ $errors -eq 0 ]; then
        print_warning "⚠️  System is functional with $warnings minor warnings."
        echo ""
        echo "✅ ROBO-RESEARCHER-2000 is ready to use with limited functionality."
    else
        print_error "❌ Found $errors critical errors and $warnings warnings."
        echo ""
        echo "🔧 Recommended actions:"
        echo "   1. Check container logs: docker logs <container-name>"
        echo "   2. Restart services: ./install.sh restart"
        echo "   3. Check system resources: docker system df"
        echo "   4. Review configuration: ./install.sh config"
    fi
}

# Function to show final instructions
show_final_instructions() {
    clear
    echo ""
    echo "🎉 ROBO-RESEARCHER-2000 Installation Complete!"
    echo "═══════════════════════════════════════════════════════════════════════════════════════"
    echo ""
    echo "🚀 Your AI-powered UX research analysis system is now ready!"
    echo ""
    echo "📍 Quick Access URLs:"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "   🌐 Main Application:     http://localhost:8080"
    echo "   🔧 n8n Workflow Engine:  http://localhost:5678"
    echo "   📁 MinIO File Storage:   http://localhost:9003"
    echo "   📚 Documentation Wiki:   http://localhost:3002"
    echo ""
    echo "🔐 Login Credentials:"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "   n8n:   <EMAIL> / robo-researcher-2000"
    echo "   MinIO: minioadmin / minioadmin"
    echo ""
    echo "📋 Next Steps to Complete Setup:"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo ""
    echo "   1️⃣  Configure n8n Workflows (5-10 minutes)"
    echo "      • Open: http://localhost:5678"
    echo "      • Login: <EMAIL> / robo-researcher-2000"
    echo "      • Import: workflows/test-workflow-simple.json"
    echo "      • Import: workflows/main-workflow-complete.json"
    echo "      • Set up OpenRouter API credentials"
    echo ""
    echo "   2️⃣  Test the System"
    echo "      • Visit: http://localhost:8080"
    echo "      • Upload a sample transcription file"
    echo "      • Monitor analysis progress in n8n"
    echo ""
    echo "   3️⃣  Optional: Configure Email Notifications"
    echo "      • Run: ./install.sh config"
    echo "      • Add SMTP credentials for result delivery"
    echo ""
    echo "🛠️  Management Commands:"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "   ./install.sh status    - Check all services"
    echo "   ./install.sh health    - Run health diagnostics"
    echo "   ./install.sh logs      - View service logs"
    echo "   ./install.sh restart   - Restart all services"
    echo "   ./install.sh config    - Reconfigure credentials"
    echo ""
    echo "📚 Documentation & Support:"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "   📖 setup-n8n.md                    - Detailed workflow setup"
    echo "   📋 workflows/workflow-documentation.md - Analysis pipeline details"
    echo "   📊 DEPLOYMENT-COMPLETE.md          - Complete system overview"
    echo ""
    echo "🎯 What You Can Do Now:"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "   ✅ Upload transcription files for AI analysis"
    echo "   ✅ Generate automated UX research insights"
    echo "   ✅ Create professional presentation reports"
    echo "   ✅ Export results in multiple formats"
    echo "   ✅ Monitor analysis workflows in real-time"
    echo ""
    print_success "🚀 Ready to revolutionize your UX research process!"
    echo ""
    echo "💡 Pro tip: Start with the test workflow to familiarize yourself with the system."
    echo ""
}

# Function to show usage
show_usage() {
    echo "🤖 ROBO-RESEARCHER-2000 Interactive Installation & Management Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Installation Commands:"
    echo "  install   🚀 Interactive installation and configuration"
    echo "  setup     🔧 Quick setup (same as install)"
    echo ""
    echo "Management Commands:"
    echo "  start     Start all services"
    echo "  stop      Stop all services"
    echo "  restart   Restart all services"
    echo "  status    Show service status"
    echo "  logs      Show service logs"
    echo "  update    Update services (pull latest images)"
    echo "  clean     Stop and remove all containers and volumes"
    echo "  health    Check service health"
    echo "  urls      Show access URLs"
    echo ""
    echo "Configuration Commands:"
    echo "  config    🔑 Configure API keys and credentials"
    echo "  workflows 📋 Import and configure n8n workflows"
    echo "  minio     📁 Set up MinIO storage buckets"
    echo "  test      🧪 Run system validation tests"
    echo ""
    echo "Help:"
    echo "  help      Show this help message"
    echo ""
    echo "💡 For first-time setup, run: $0 install"
    echo ""
}

# Function to start services
start_services() {
    print_status "Starting ROBO-RESEARCHER-2000 services..."
    $COMPOSE_CMD -f $COMPOSE_FILE up -d
    print_success "Services started!"
    show_urls
}

# Function to stop services
stop_services() {
    print_status "Stopping ROBO-RESEARCHER-2000 services..."
    $COMPOSE_CMD -f $COMPOSE_FILE down
    print_success "Services stopped!"
}

# Function to restart services
restart_services() {
    print_status "Restarting ROBO-RESEARCHER-2000 services..."
    $COMPOSE_CMD -f $COMPOSE_FILE restart
    print_success "Services restarted!"
}

# Function to show status
show_status() {
    print_status "Service status:"
    $COMPOSE_CMD -f $COMPOSE_FILE ps
}

# Function to show logs
show_logs() {
    print_status "Showing service logs (Ctrl+C to exit)..."
    $COMPOSE_CMD -f $COMPOSE_FILE logs -f
}

# Function to update services
update_services() {
    print_status "Updating ROBO-RESEARCHER-2000 services..."
    $COMPOSE_CMD -f $COMPOSE_FILE pull
    $COMPOSE_CMD -f $COMPOSE_FILE up -d
    print_success "Services updated!"
}

# Function to clean everything
clean_services() {
    print_warning "This will stop and remove all containers and volumes!"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_status "Cleaning up ROBO-RESEARCHER-2000..."
        $COMPOSE_CMD -f $COMPOSE_FILE down -v --remove-orphans
        print_success "Cleanup complete!"
    else
        print_status "Cleanup cancelled."
    fi
}

# Function to check health
check_health() {
    print_status "Checking service health..."

    # Check Client
    if curl -s http://localhost:8080/health > /dev/null 2>&1; then
        print_success "Client Application is healthy"
    else
        print_error "Client Application is not responding"
    fi

    # Check n8n
    if curl -s http://localhost:5678/healthz > /dev/null 2>&1; then
        print_success "n8n is healthy"
    else
        print_error "n8n is not responding"
    fi

    # Check MinIO (updated port)
    if curl -s http://localhost:9002/minio/health/live > /dev/null 2>&1; then
        print_success "MinIO is healthy"
    else
        print_error "MinIO is not responding"
    fi

    # Check MinIO Console
    if curl -s http://localhost:9003 > /dev/null 2>&1; then
        print_success "MinIO Console is healthy"
    else
        print_error "MinIO Console is not responding"
    fi

    # Check Redis
    if docker exec robo-researcher-redis redis-cli ping > /dev/null 2>&1; then
        print_success "Redis is healthy"
    else
        print_error "Redis is not responding"
    fi

    # Check PostgreSQL
    if docker exec robo-researcher-postgres pg_isready -U n8n > /dev/null 2>&1; then
        print_success "PostgreSQL is healthy"
    else
        print_error "PostgreSQL is not responding"
    fi

    # Check Wiki.js
    if curl -s http://localhost:3002 > /dev/null 2>&1; then
        print_success "Wiki.js is healthy"
    else
        print_error "Wiki.js is not responding"
    fi
}

# Function to show URLs
show_urls() {
    echo ""
    print_success "🎉 ROBO-RESEARCHER-2000 Access URLs:"
    echo ""
    echo "📋 Service URLs:"
    echo "  • Client Application:        http://localhost:8080"
    echo "  • n8n (Workflow Engine):     http://localhost:5678"
    echo "  • MinIO Console:             http://localhost:9003"
    echo "  • Wiki.js Documentation:     http://localhost:3002"
    echo ""
    echo "🔐 Default Credentials:"
    echo "  • n8n:     admin / robo-researcher-2000"
    echo "  • MinIO:   minioadmin / minioadmin"
    echo ""
    echo "📊 Internal Services:"
    echo "  • MinIO API:                 http://localhost:9002"
    echo "  • PostgreSQL:                localhost:5433"
    echo "  • Redis:                     localhost:6380"
    echo ""
}

# Main script logic
case "${1:-help}" in
    install|setup)
        interactive_install
        ;;
    start)
        start_services
        ;;
    stop)
        stop_services
        ;;
    restart)
        restart_services
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs
        ;;
    update)
        update_services
        ;;
    clean)
        clean_services
        ;;
    health)
        check_health
        ;;
    urls)
        show_urls
        ;;
    config)
        configure_credentials
        ;;
    workflows)
        configure_workflows
        ;;
    minio)
        setup_minio_storage
        ;;
    test)
        validate_installation
        ;;
    help|--help|-h)
        show_usage
        ;;
    *)
        if [ -z "$1" ]; then
            echo "🤖 ROBO-RESEARCHER-2000 Interactive Installation & Management"
            echo ""
            echo "💡 For first-time setup, run: $0 install"
            echo "📋 For help, run: $0 help"
            echo ""
        else
            print_error "Unknown command: $1"
            echo ""
            show_usage
            exit 1
        fi
        ;;
esac

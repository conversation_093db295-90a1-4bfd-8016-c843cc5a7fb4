#!/bin/bash

# ROBO-RESEARCHER-2000 MinIO Setup Script
# This script creates the required bucket and sets up basic configuration

echo "🚀 Setting up MinIO for ROBO-RESEARCHER-2000..."

# Wait for MinIO to be ready
echo "⏳ Waiting for Min<PERSON> to be ready..."
until curl -s http://localhost:9002/minio/health/live > /dev/null; do
    echo "   Waiting for MinIO..."
    sleep 2
done

echo "✅ MinIO is ready!"

# Install mc (MinIO Client) if not present
if ! command -v mc &> /dev/null; then
    echo "📦 Installing MinIO Client..."
    
    # Download mc binary
    curl -O https://dl.min.io/client/mc/release/linux-amd64/mc
    chmod +x mc
    sudo mv mc /usr/local/bin/
    
    echo "✅ MinIO Client installed!"
fi

# Configure mc alias
echo "🔧 Configuring MinIO client..."
mc alias set robo-minio http://localhost:9002 minioadmin minioadmin

# Create bucket
echo "📁 Creating robo-researcher-data bucket..."
mc mb robo-minio/robo-researcher-data --ignore-existing

# Set bucket policy for public read (optional)
echo "🔒 Setting bucket policy..."
cat > /tmp/bucket-policy.json << EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Principal": {
                "AWS": ["*"]
            },
            "Action": ["s3:GetObject"],
            "Resource": ["arn:aws:s3:::robo-researcher-data/*"]
        }
    ]
}
EOF

mc policy set-json /tmp/bucket-policy.json robo-minio/robo-researcher-data

# Create directory structure
echo "📂 Creating directory structure..."
mc mkdir robo-minio/robo-researcher-data/transcripts
mc mkdir robo-minio/robo-researcher-data/results
mc mkdir robo-minio/robo-researcher-data/presentations
mc mkdir robo-minio/robo-researcher-data/temp

# Upload test file
echo "📄 Creating test file..."
echo "This is a test transcription for ROBO-RESEARCHER-2000" > /tmp/test-transcript.txt
mc cp /tmp/test-transcript.txt robo-minio/robo-researcher-data/transcripts/

# List bucket contents
echo "📋 Bucket contents:"
mc ls robo-minio/robo-researcher-data --recursive

echo ""
echo "🎉 MinIO setup complete!"
echo ""
echo "📊 Access URLs:"
echo "   MinIO API: http://localhost:9002"
echo "   MinIO Console: http://localhost:9003"
echo "   Login: minioadmin / minioadmin"
echo ""
echo "✅ Bucket 'robo-researcher-data' is ready for use!"

# Cleanup
rm -f /tmp/bucket-policy.json /tmp/test-transcript.txt

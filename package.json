{"name": "robo-researcher-2000", "version": "1.0.0", "description": "Sistema de Investigación UX Semi-Automatizado con n8n y herramientas open-source", "main": "index.js", "scripts": {"dev": "live-server client/", "build": "npm run build:client", "build:client": "echo 'Building client for GitHub Pages...'", "deploy": "gh-pages -d client", "test": "echo 'No tests specified yet'", "lint": "eslint client/js/**/*.js", "format": "prettier --write client/**/*.{html,css,js}", "setup:infrastructure": "cd infrastructure && docker-compose up -d", "setup:python": "pip install -r requirements.txt", "validate:workflow": "node scripts/validate-n8n-workflow.js"}, "repository": {"type": "git", "url": "git+https://github.com/tu-usuario/robo-researcher-2000.git"}, "keywords": ["ux-research", "automation", "n8n", "qualitative-analysis", "open-source", "self-hosted", "research-tools"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/tu-usuario/robo-researcher-2000/issues"}, "homepage": "https://github.com/tu-usuario/robo-researcher-2000#readme", "devDependencies": {"eslint": "^8.0.0", "gh-pages": "^4.0.0", "live-server": "^1.2.2", "prettier": "^2.8.0"}, "dependencies": {"@marp-team/marp-cli": "^3.4.0"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}
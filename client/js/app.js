// ROBO-RESEARCHER-2000 Main Application
class RoboResearcher {
    constructor() {
        this.config = {
            pollInterval: 5000, // 5 seconds
            maxFileSize: 10 * 1024 * 1024, // 10MB
            allowedFileTypes: ['.txt'],
            steps: [
                { id: 1, name: 'Webhook Trigger', description: 'Receiving data from client' },
                { id: 2, name: 'Validate Input', description: 'Validating format and content' },
                { id: 3, name: 'Upload to MinIO', description: 'Storing transcription' },
                { id: 4, name: 'Text Preprocessing', description: 'Cleaning and anonymization' },
                { id: 5, name: 'Segmentation', description: 'Dividing by topics' },
                { id: 6, name: 'Deductive Coding', description: 'Applying predefined codes' },
                { id: 7, name: 'Open Coding AI', description: 'AI suggests emergent codes' },
                { id: 8, name: 'Category Grouping', description: 'Grouping similar codes' },
                { id: 9, name: 'Affinity Mapping', description: 'Generating visualizations' },
                { id: 10, name: 'Quantitative Analysis', description: 'Calculating metrics and frequencies' },
                { id: 11, name: 'Pattern Detection', description: 'AI detects patterns' },
                { id: 12, name: 'Insight Generation', description: 'Generating structured insights' },
                { id: 13, name: 'Archetype Creation', description: 'Creating user archetypes' },
                { id: 14, name: 'HMW Generation', description: 'Generating "How Might We" questions' },
                { id: 15, name: 'Opportunity Prioritization', description: 'Applying RICE matrix' },
                { id: 16, name: 'Presentation Generation', description: 'Generating presentation with Marp' },
                { id: 17, name: 'Documentation & Email', description: 'Documenting in Wiki.js and sending email' }
            ]
        };
        
        this.state = {
            currentStep: 0,
            workflowId: null,
            isProcessing: false,
            results: null,
            currentProjectName: null
        };

        this.apiManager = null;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupFileUpload();
        this.initializeAPIManager();

        // Listen for configuration updates
        window.addEventListener('configUpdated', (e) => {
            this.initializeAPIManager();
        });
    }

    initializeAPIManager() {
        // Wait for config manager to be available
        if (window.configManager) {
            const config = window.configManager.getConfig();
            this.apiManager = new APIManager(config);
        } else {
            // Retry after a short delay
            setTimeout(() => this.initializeAPIManager(), 100);
        }
    }
    
    setupEventListeners() {
        // Form submission
        const form = document.getElementById('uploadForm');
        if (form) {
            form.addEventListener('submit', this.handleFormSubmit.bind(this));
        }
        
        // Navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', this.handleNavigation.bind(this));
        });
        
        // Auto-save form data
        document.querySelectorAll('input, select, textarea').forEach(input => {
            input.addEventListener('change', this.saveFormData.bind(this));
        });

        // Results section buttons
        document.getElementById('viewWikiBtn')?.addEventListener('click', () => {
            this.openWikiResults();
        });

        document.getElementById('newAnalysisBtn')?.addEventListener('click', () => {
            this.startNewAnalysis();
        });
    }
    
    setupFileUpload() {
        const fileInput = document.getElementById('transcriptionFile');
        const uploadArea = document.getElementById('fileUploadArea');
        const fileInfo = document.getElementById('fileInfo');
        
        if (!fileInput || !uploadArea) return;
        
        // Click to upload
        uploadArea.addEventListener('click', () => fileInput.click());
        
        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                this.handleFileSelection(files[0]);
            }
        });
        
        // File input change
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                this.handleFileSelection(e.target.files[0]);
            }
        });
    }
    
    handleFileSelection(file) {
        const fileInfo = document.getElementById('fileInfo');
        const fileInput = document.getElementById('transcriptionFile');
        
        // Validate file
        const validation = this.validateFile(file);
        if (!validation.valid) {
            this.showNotification('error', 'Invalid file', validation.message);
            return;
        }
        
        // Update UI
        fileInfo.innerHTML = `
            <i class="fas fa-file-text"></i>
            <span><strong>${file.name}</strong> (${this.formatFileSize(file.size)})</span>
            <button type="button" class="btn-secondary" onclick="app.removeFile()">
                <i class="fas fa-times"></i>
            </button>
        `;
        fileInfo.classList.remove('d-none');
        
        // Set file to input (for form submission)
        const dt = new DataTransfer();
        dt.items.add(file);
        fileInput.files = dt.files;
        
        this.showNotification('success', 'Archivo cargado', `${file.name} listo para análisis`);
    }
    
    validateFile(file) {
        // Check file type
        const extension = '.' + file.name.split('.').pop().toLowerCase();
        if (!this.config.allowedFileTypes.includes(extension)) {
            return {
                valid: false,
                message: `Solo se permiten archivos ${this.config.allowedFileTypes.join(', ')}`
            };
        }
        
        // Check file size
        if (file.size > this.config.maxFileSize) {
            return {
                valid: false,
                message: `El archivo es demasiado grande. Máximo ${this.formatFileSize(this.config.maxFileSize)}`
            };
        }
        
        return { valid: true };
    }
    
    removeFile() {
        const fileInput = document.getElementById('transcriptionFile');
        const fileInfo = document.getElementById('fileInfo');
        
        fileInput.value = '';
        fileInfo.classList.add('d-none');
        
        this.showNotification('info', 'Archivo removido', 'Selecciona otro archivo para continuar');
    }
    

    
    setupPasswordToggles() {
        document.querySelectorAll('.toggle-password').forEach(toggle => {
            toggle.addEventListener('click', () => {
                const targetId = toggle.dataset.target;
                const input = document.getElementById(targetId);
                
                if (input.type === 'password') {
                    input.type = 'text';
                    toggle.classList.remove('fa-eye');
                    toggle.classList.add('fa-eye-slash');
                } else {
                    input.type = 'password';
                    toggle.classList.remove('fa-eye-slash');
                    toggle.classList.add('fa-eye');
                }
            });
        });
    }
    
    async handleFormSubmit(e) {
        e.preventDefault();
        
        if (this.state.isProcessing) {
            this.showNotification('warning', 'Processing', 'An analysis is already in progress');
            return;
        }
        
        const formData = this.getFormData();
        const validation = this.validateFormData(formData);

        if (!validation.valid) {
            this.showNotification('error', 'Validation Error', validation.message);
            return;
        }

        // Store project name for later use
        this.state.currentProjectName = formData.projectName;
        
        try {
            this.state.isProcessing = true;
            this.showLoadingOverlay('Starting analysis...');
            this.showProgressSection();
            
            // Start workflow
            const response = await this.startWorkflow(formData);
            
            if (response.success) {
                this.state.workflowId = response.workflowId;
                this.startProgressPolling();
                this.showNotification('success', 'Analysis started', 'Your transcription is being processed');
            } else {
                throw new Error(response.message || 'Error al iniciar el workflow');
            }
            
        } catch (error) {
            console.error('Error starting workflow:', error);
            this.showNotification('error', 'Error', error.message);
            this.state.isProcessing = false;
            this.hideLoadingOverlay();
        }
    }
    
    getFormData() {
        const form = document.getElementById('uploadForm');
        const formData = new FormData(form);
        
        // Convert to object
        const data = {};
        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }
        
        // Add file content
        const fileInput = document.getElementById('transcriptionFile');
        if (fileInput.files.length > 0) {
            data.file = fileInput.files[0];
        }
        
        return data;
    }
    
    validateFormData(data) {
        const required = ['projectName', 'email', 'studyType', 'openrouterKey'];
        const missing = required.filter(field => !data[field]);
        
        if (missing.length > 0) {
            return {
                valid: false,
                message: `Campos requeridos: ${missing.join(', ')}`
            };
        }
        
        if (!data.file) {
            return {
                valid: false,
                message: 'You must select a transcription file'
            };
        }
        
        // Validate email
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(data.email)) {
            return {
                valid: false,
                message: 'Invalid email'
            };
        }
        
        return { valid: true };
    }
    
    async startWorkflow(formData) {
        // Read file content
        const fileContent = await this.readFileAsText(formData.file);
        
        // Prepare payload
        const payload = {
            project_name: formData.projectName,
            email: formData.email,
            transcription: fileContent,
            study_type: formData.studyType,
            objectives: formData.objectives || '',
            language: formData.language || 'es',
            analysis_depth: formData.analysisDepth || 'standard',
            api_keys: {
                openrouter: formData.openrouterKey,
                smtp_password: formData.smtpPassword || ''
            },
            options: {
                enable_sentiment: formData.enableSentiment === 'on',
                enable_entities: formData.enableEntities === 'on',
                generate_presentation: formData.generatePresentation === 'on'
            },
            timestamp: new Date().toISOString()
        };
        
        // Send to n8n webhook
        const response = await fetch(this.config.n8nWebhookUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(payload)
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        return await response.json();
    }
    
    readFileAsText(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = e => resolve(e.target.result);
            reader.onerror = e => reject(new Error('Error reading file'));
            reader.readAsText(file);
        });
    }
    
    showProgressSection() {
        document.getElementById('upload').style.display = 'none';
        document.getElementById('progress').classList.remove('d-none');
        this.renderProgressSteps();
    }

    renderProgressSteps() {
        const container = document.getElementById('progressSteps');
        container.innerHTML = this.config.steps.map(step => `
            <div class="col-md-6 col-lg-4 mb-3">
                <div class="card progress-step" data-step="${step.id}">
                    <div class="card-body d-flex align-items-center">
                        <div class="step-icon bg-secondary text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px; font-weight: bold;">
                            ${step.id}
                        </div>
                        <div class="step-content">
                            <div class="step-title fw-semibold">${step.name}</div>
                            <div class="step-description text-muted small">${step.description}</div>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    startProgressPolling() {
        this.pollInterval = setInterval(() => {
            this.checkWorkflowProgress();
        }, this.config.pollInterval);
    }

    async checkWorkflowProgress() {
        try {
            // Simulate progress for demo (replace with actual API call)
            this.simulateProgress();
        } catch (error) {
            console.error('Error checking progress:', error);
            this.showNotification('error', 'Error', 'Error al verificar progreso');
        }
    }

    simulateProgress() {
        if (this.state.currentStep < this.config.steps.length) {
            this.state.currentStep++;
            this.updateProgressUI();

            if (this.state.currentStep >= this.config.steps.length) {
                this.completeWorkflow();
            }
        }
    }

    updateProgressUI() {
        const progress = (this.state.currentStep / this.config.steps.length) * 100;
        document.getElementById('progressFill').style.width = `${progress}%`;

        const currentStepName = this.config.steps[this.state.currentStep - 1]?.name || 'Completado';
        document.getElementById('progressText').textContent = `Paso ${this.state.currentStep}/${this.config.steps.length}: ${currentStepName}`;

        // Update step indicators
        document.querySelectorAll('.progress-step').forEach((step, index) => {
            const card = step;
            const icon = step.querySelector('.step-icon');

            // Reset classes
            card.classList.remove('border-success', 'border-primary', 'bg-light');
            icon.classList.remove('bg-success', 'bg-primary', 'bg-secondary');

            if (index < this.state.currentStep - 1) {
                // Completed step
                card.classList.add('border-success', 'bg-light');
                icon.classList.add('bg-success');
            } else if (index === this.state.currentStep - 1) {
                // Active step
                card.classList.add('border-primary');
                icon.classList.add('bg-primary');
            } else {
                // Pending step
                icon.classList.add('bg-secondary');
            }
        });
    }

    completeWorkflow() {
        clearInterval(this.pollInterval);
        this.state.isProcessing = false;
        this.hideLoadingOverlay();

        // Show results
        this.showResultsSection();
        this.showNotification('success', 'Analysis completed', 'Your UX research has been processed successfully');
    }

    showResultsSection() {
        document.getElementById('progress').style.display = 'none';
        document.getElementById('results').classList.remove('d-none');
        // Results are now handled by the static HTML with Wiki.js integration
    }

    openWikiResults() {
        if (!this.state.currentProjectName) {
            this.showNotification('error', 'Error', 'No project name available');
            return;
        }

        const config = window.configManager?.getConfig() || {};
        const wikijsUrl = config.wikijsUrl || 'http://localhost:3002';
        const projectSlug = this.createProjectSlug(this.state.currentProjectName);
        const wikiUrl = `${wikijsUrl}/research/${projectSlug}`;

        // Open Wiki.js in new tab
        window.open(wikiUrl, '_blank');
    }

    createProjectSlug(projectName) {
        return projectName
            .toLowerCase()
            .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
            .replace(/\s+/g, '-') // Replace spaces with hyphens
            .replace(/-+/g, '-') // Replace multiple hyphens with single
            .trim('-'); // Remove leading/trailing hyphens
    }

    startNewAnalysis() {
        // Reset state
        this.state = {
            currentStep: 0,
            workflowId: null,
            isProcessing: false,
            results: null,
            currentProjectName: null
        };

        // Show upload section
        document.getElementById('results').classList.add('d-none');
        document.getElementById('progress').classList.add('d-none');
        document.getElementById('upload').style.display = 'block';

        // Reset form
        document.getElementById('uploadForm').reset();

        // Clear file info
        const fileInfo = document.getElementById('fileInfo');
        if (fileInfo) {
            fileInfo.classList.add('d-none');
            fileInfo.innerHTML = '';
        }

        // Scroll to upload section
        this.scrollToSection('upload');
    }

                <div class="result-card">
                    <div class="result-card-header">
                        <div class="result-card-icon visualization">
                            <i class="fas fa-project-diagram"></i>
                        </div>
                        <div class="result-card-title">Visualizaciones</div>
                    </div>
                    <div class="result-card-description">
                        Mapas de afinidad y diagramas generados automáticamente en formato SVG.
                    </div>
                    <div class="result-card-actions">
                        <button class="btn btn-primary" onclick="app.downloadFile('visualizations')">
                            <i class="fas fa-download"></i> Descargar SVG
                        </button>
                        <button class="btn btn-outline" onclick="app.previewFile('visualizations')">
                            <i class="fas fa-eye"></i> Ver online
                        </button>
                    </div>
                </div>

                <div class="result-card">
                    <div class="result-card-header">
                        <div class="result-card-icon data">
                            <i class="fas fa-database"></i>
                        </div>
                        <div class="result-card-title">Datos de Análisis</div>
                    </div>
                    <div class="result-card-description">
                        Datos estructurados de codificación, insights y métricas en formato JSON.
                    </div>
                    <div class="result-card-actions">
                        <button class="btn btn-primary" onclick="app.downloadFile('data')">
                            <i class="fas fa-download"></i> Descargar JSON
                        </button>
                    </div>
                </div>
            </div>

            <div class="text-center" style="margin-top: 2rem;">
                <button class="btn btn-outline" onclick="app.startNewAnalysis()">
                    <i class="fas fa-plus"></i> Nuevo Análisis
                </button>
            </div>
        `;
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    saveFormData() {
        const formData = this.getFormData();
        localStorage.setItem('robo-researcher-form', JSON.stringify(formData));
    }
    
    loadSavedConfig() {
        const saved = localStorage.getItem('robo-researcher-form');
        if (saved) {
            try {
                const data = JSON.parse(saved);
                // Restore form fields (except sensitive data)
                Object.keys(data).forEach(key => {
                    if (key !== 'openrouterKey' && key !== 'smtpPassword') {
                        const element = document.getElementById(key);
                        if (element && element.type !== 'file') {
                            element.value = data[key];
                        }
                    }
                });
            } catch (error) {
                console.warn('Error loading saved config:', error);
            }
        }
    }

    // Additional utility methods
    showLoadingOverlay(message) {
        const overlay = document.getElementById('loadingOverlay');
        const messageElement = document.getElementById('loadingMessage');

        if (messageElement) messageElement.textContent = message;
        if (overlay) overlay.classList.remove('d-none');
    }

    hideLoadingOverlay() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) overlay.classList.add('d-none');
    }

    handleNavigation(e) {
        e.preventDefault();
        const target = e.target.getAttribute('href');
        if (target) {
            document.querySelector(target)?.scrollIntoView({ behavior: 'smooth' });
        }
    }

    // File action handlers
    downloadFile(type) {
        const projectName = document.getElementById('projectName')?.value || 'proyecto';
        this.showNotification('info', 'Descargando...', `Preparando ${type} para descarga`);

        // Simulate download (replace with actual API call)
        setTimeout(() => {
            this.showNotification('success', 'Descarga completada', `${type} descargado exitosamente`);
        }, 2000);
    }

    previewFile(type) {
        this.showNotification('info', 'Abriendo vista previa...', `Cargando ${type}`);
        // Implement preview functionality
    }

    openWiki() {
        const projectName = document.getElementById('projectName')?.value || 'proyecto';
        const wikiUrl = `http://localhost:3000/research/${projectName.toLowerCase().replace(/\s+/g, '-')}`;
        window.open(wikiUrl, '_blank');
    }

    startNewAnalysis() {
        // Reset form and state
        this.state = {
            currentStep: 0,
            workflowId: null,
            isProcessing: false,
            results: null
        };

        // Show upload section
        document.getElementById('results').classList.add('d-none');
        document.getElementById('progress').classList.add('d-none');
        document.getElementById('upload').style.display = 'block';

        // Reset form
        document.getElementById('uploadForm').reset();
        this.removeFile();

        this.showNotification('info', 'New analysis', 'Form reset for new project');
    }

    showNotification(type, title, message, options = {}) {
        if (window.notifications) {
            return window.notifications.show(type, title, message, options);
        } else {
            // Fallback to console if notifications not available
            console.log(`${type.toUpperCase()}: ${title} - ${message}`);
        }
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new RoboResearcher();
});

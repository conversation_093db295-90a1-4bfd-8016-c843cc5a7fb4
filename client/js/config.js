// ROBO-RESEARCHER-2000 Configuration Manager
class ConfigManager {
    constructor() {
        this.storageKey = 'robo-researcher-config';
        this.defaultConfig = {
            openRouterKey: '',
            smtpHost: 'smtp.gmail.com',
            smtpPort: 587,
            smtpUsername: '',
            smtpPassword: '',
            minioEndpoint: 'http://localhost:9002',
            wikijsUrl: 'http://localhost:3002',
            n8nUrl: 'http://localhost:5678'
        };
        
        this.config = this.loadConfig();
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.setupPasswordToggles();
        this.updateConfigStatus();
        this.populateForm();
    }
    
    setupEventListeners() {
        // Modal controls
        document.getElementById('configBtn')?.addEventListener('click', () => this.openModal());
        document.getElementById('openConfigBtn')?.addEventListener('click', () => this.openModal());

        // Form controls
        document.getElementById('saveConfigBtn')?.addEventListener('click', () => this.saveConfig());
        document.getElementById('resetConfigBtn')?.addEventListener('click', () => this.resetConfig());

        // Test buttons
        document.querySelectorAll('.btn-test').forEach(btn => {
            btn.addEventListener('click', (e) => this.testConnection(e.target.dataset.test));
        });

        // Initialize Bootstrap modal
        this.modal = new bootstrap.Modal(document.getElementById('configModal'));
    }
    
    setupPasswordToggles() {
        document.querySelectorAll('.toggle-password').forEach(toggle => {
            toggle.addEventListener('click', (e) => {
                const targetId = e.target.dataset.target;
                const input = document.getElementById(targetId);
                const icon = e.target;
                
                if (input.type === 'password') {
                    input.type = 'text';
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    input.type = 'password';
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
            });
        });
    }
    
    openModal() {
        this.populateForm();
        this.modal.show();
    }

    closeModal() {
        this.modal.hide();
    }
    
    loadConfig() {
        try {
            const stored = localStorage.getItem(this.storageKey);
            return stored ? { ...this.defaultConfig, ...JSON.parse(stored) } : { ...this.defaultConfig };
        } catch (error) {
            console.error('Error loading config:', error);
            return { ...this.defaultConfig };
        }
    }
    
    saveConfig() {
        try {
            // Get form data
            const formData = new FormData(document.getElementById('configForm'));
            const newConfig = {};
            
            for (const [key, value] of formData.entries()) {
                newConfig[key] = value;
            }
            
            // Validate OpenRouter key format
            if (newConfig.openRouterKey && !this.validateOpenRouterKey(newConfig.openRouterKey)) {
                this.showNotification('error', 'Invalid OpenRouter API key format. Expected: sk-or-v1-...');
                return;
            }
            
            // Update config
            this.config = { ...this.config, ...newConfig };
            
            // Save to localStorage
            localStorage.setItem(this.storageKey, JSON.stringify(this.config));
            
            // Update status display
            this.updateConfigStatus();
            
            // Close modal
            this.closeModal();
            
            this.showNotification('success', 'Configuration saved successfully');
            
            // Trigger config update event
            window.dispatchEvent(new CustomEvent('configUpdated', { detail: this.config }));
            
        } catch (error) {
            console.error('Error saving config:', error);
            this.showNotification('error', 'Failed to save configuration');
        }
    }
    
    resetConfig() {
        if (confirm('Are you sure you want to reset all configuration settings?')) {
            this.config = { ...this.defaultConfig };
            localStorage.removeItem(this.storageKey);
            this.populateForm();
            this.updateConfigStatus();
            this.showNotification('info', 'Configuration reset to defaults');
        }
    }
    
    populateForm() {
        Object.keys(this.config).forEach(key => {
            const input = document.getElementById(`config${key.charAt(0).toUpperCase() + key.slice(1)}`);
            if (input) {
                input.value = this.config[key] || '';
            }
        });
    }
    
    updateConfigStatus() {
        // OpenRouter status
        const openRouterStatus = document.getElementById('configOpenRouter');
        if (openRouterStatus) {
            const hasKey = this.config.openRouterKey && this.validateOpenRouterKey(this.config.openRouterKey);
            this.updateStatusItem(openRouterStatus, hasKey, hasKey ? 'Configured' : 'Not configured');
        }
        
        // SMTP status
        const smtpStatus = document.getElementById('configSMTP');
        if (smtpStatus) {
            const hasSmtp = this.config.smtpHost && this.config.smtpUsername;
            this.updateStatusItem(smtpStatus, hasSmtp, hasSmtp ? 'Configured' : 'Not configured');
        }
        
        // Services status
        const servicesStatus = document.getElementById('configServices');
        if (servicesStatus) {
            const hasServices = this.config.minioEndpoint && this.config.wikijsUrl && this.config.n8nUrl;
            this.updateStatusItem(servicesStatus, hasServices, hasServices ? 'Configured' : 'Using defaults');
        }
    }
    
    updateStatusItem(element, isConfigured, statusText) {
        const icon = element.querySelector('.status-icon');
        const small = element.querySelector('small');
        
        if (isConfigured) {
            icon.className = 'fas fa-check-circle status-icon status-success';
            small.textContent = statusText;
        } else {
            icon.className = 'fas fa-exclamation-circle status-icon status-warning';
            small.textContent = statusText;
        }
    }
    
    validateOpenRouterKey(key) {
        return /^sk-or-v1-[a-zA-Z0-9]{64}$/.test(key);
    }
    
    async testConnection(type) {
        const button = document.querySelector(`[data-test="${type}"]`);
        const originalText = button.innerHTML;
        
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Testing...';
        button.disabled = true;
        
        try {
            switch (type) {
                case 'openrouter':
                    await this.testOpenRouter();
                    break;
                case 'smtp':
                    await this.testSMTP();
                    break;
                case 'services':
                    await this.testServices();
                    break;
            }
        } catch (error) {
            console.error(`Test ${type} failed:`, error);
        } finally {
            button.innerHTML = originalText;
            button.disabled = false;
        }
    }
    
    async testOpenRouter() {
        const key = document.getElementById('configOpenRouterKey').value;
        if (!key) {
            this.showNotification('warning', 'Please enter an OpenRouter API key');
            return;
        }
        
        if (!this.validateOpenRouterKey(key)) {
            this.showNotification('error', 'Invalid OpenRouter API key format');
            return;
        }
        
        try {
            const response = await fetch('https://openrouter.ai/api/v1/models', {
                headers: {
                    'Authorization': `Bearer ${key}`,
                    'Content-Type': 'application/json'
                }
            });
            
            if (response.ok) {
                this.showNotification('success', 'OpenRouter API key is valid');
            } else {
                this.showNotification('error', 'OpenRouter API key is invalid');
            }
        } catch (error) {
            this.showNotification('error', 'Failed to test OpenRouter connection');
        }
    }
    
    async testSMTP() {
        this.showNotification('info', 'SMTP testing requires server-side validation');
    }
    
    async testServices() {
        const minioUrl = document.getElementById('configMinioEndpoint').value;
        const wikijsUrl = document.getElementById('configWikijsUrl').value;
        const n8nUrl = document.getElementById('configN8nUrl').value;
        
        let allGood = true;
        
        // Test MinIO
        try {
            const response = await fetch(`${minioUrl}/minio/health/live`);
            if (!response.ok) allGood = false;
        } catch {
            allGood = false;
        }
        
        // Test Wiki.js
        try {
            const response = await fetch(`${wikijsUrl}/healthz`);
            if (!response.ok) allGood = false;
        } catch {
            allGood = false;
        }
        
        // Test n8n
        try {
            const response = await fetch(`${n8nUrl}/healthz`);
            if (!response.ok) allGood = false;
        } catch {
            allGood = false;
        }
        
        if (allGood) {
            this.showNotification('success', 'All services are accessible');
        } else {
            this.showNotification('warning', 'Some services may not be accessible');
        }
    }
    
    showNotification(type, message) {
        // Use existing notification system if available
        if (window.showNotification) {
            window.showNotification(type, 'Configuration', message);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }
    
    getConfig() {
        return { ...this.config };
    }
    
    isConfigured() {
        return this.config.openRouterKey && this.validateOpenRouterKey(this.config.openRouterKey);
    }
}

// Initialize configuration manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.configManager = new ConfigManager();
});

// ROBO-RESEARCHER-2000 Notification System
class NotificationManager {
    constructor() {
        this.container = null;
        this.notifications = new Map();
        this.defaultDuration = 5000; // 5 seconds
        this.maxNotifications = 5;
        
        this.init();
    }
    
    init() {
        this.createContainer();
        this.setupStyles();
    }
    
    createContainer() {
        this.container = document.getElementById('toastContainer');
        if (!this.container) {
            this.container = document.createElement('div');
            this.container.id = 'toastContainer';
            this.container.className = 'toast-container';
            document.body.appendChild(this.container);
        }
    }
    
    setupStyles() {
        // Styles are already defined in components.css
        // This method can be used for dynamic style adjustments if needed
    }
    
    show(type, title, message, options = {}) {
        const id = this.generateId();
        const duration = options.duration || this.defaultDuration;
        const persistent = options.persistent || false;
        const actions = options.actions || [];
        
        // Remove oldest notification if we have too many
        if (this.notifications.size >= this.maxNotifications) {
            const oldestId = this.notifications.keys().next().value;
            this.remove(oldestId);
        }
        
        const notification = this.createNotification(id, type, title, message, actions, persistent);
        this.container.appendChild(notification);
        this.notifications.set(id, notification);
        
        // Auto-remove after duration (unless persistent)
        if (!persistent && duration > 0) {
            setTimeout(() => {
                this.remove(id);
            }, duration);
        }
        
        // Trigger entrance animation
        requestAnimationFrame(() => {
            notification.classList.add('show');
        });
        
        return id;
    }
    
    createNotification(id, type, title, message, actions, persistent) {
        const notification = document.createElement('div');
        notification.className = `toast ${type}`;
        notification.dataset.id = id;
        
        const icon = this.getIcon(type);
        
        notification.innerHTML = `
            <div class="toast-icon">
                <i class="${icon}"></i>
            </div>
            <div class="toast-content">
                <div class="toast-title">${title}</div>
                <div class="toast-message">${message}</div>
                ${actions.length > 0 ? this.createActions(actions) : ''}
            </div>
            ${!persistent ? '<button class="toast-close" onclick="notifications.remove(\'' + id + '\')"><i class="fas fa-times"></i></button>' : ''}
        `;
        
        return notification;
    }
    
    createActions(actions) {
        return `
            <div class="toast-actions">
                ${actions.map(action => `
                    <button class="btn btn-sm ${action.class || 'btn-outline'}" 
                            onclick="${action.onclick}">
                        ${action.icon ? `<i class="${action.icon}"></i>` : ''}
                        ${action.text}
                    </button>
                `).join('')}
            </div>
        `;
    }
    
    getIcon(type) {
        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle',
            loading: 'fas fa-spinner fa-spin'
        };
        
        return icons[type] || icons.info;
    }
    
    remove(id) {
        const notification = this.notifications.get(id);
        if (!notification) return;
        
        // Trigger exit animation
        notification.classList.add('removing');
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
            this.notifications.delete(id);
        }, 300);
    }
    
    removeAll() {
        this.notifications.forEach((notification, id) => {
            this.remove(id);
        });
    }
    
    update(id, updates) {
        const notification = this.notifications.get(id);
        if (!notification) return;
        
        if (updates.title) {
            const titleElement = notification.querySelector('.toast-title');
            if (titleElement) titleElement.textContent = updates.title;
        }
        
        if (updates.message) {
            const messageElement = notification.querySelector('.toast-message');
            if (messageElement) messageElement.textContent = updates.message;
        }
        
        if (updates.type) {
            notification.className = `toast ${updates.type}`;
            const iconElement = notification.querySelector('.toast-icon i');
            if (iconElement) {
                iconElement.className = this.getIcon(updates.type);
            }
        }
    }
    
    // Convenience methods
    success(title, message, options = {}) {
        return this.show('success', title, message, options);
    }
    
    error(title, message, options = {}) {
        return this.show('error', title, message, { ...options, duration: 8000 });
    }
    
    warning(title, message, options = {}) {
        return this.show('warning', title, message, { ...options, duration: 6000 });
    }
    
    info(title, message, options = {}) {
        return this.show('info', title, message, options);
    }
    
    loading(title, message, options = {}) {
        return this.show('loading', title, message, { ...options, persistent: true });
    }
    
    // Progress notification
    progress(title, message, percentage, options = {}) {
        const id = options.id || this.generateId();
        
        if (this.notifications.has(id)) {
            // Update existing progress notification
            const notification = this.notifications.get(id);
            const titleElement = notification.querySelector('.toast-title');
            const messageElement = notification.querySelector('.toast-message');
            
            if (titleElement) titleElement.textContent = `${title} (${percentage}%)`;
            if (messageElement) messageElement.textContent = message;
            
            // Update progress bar if it exists
            let progressBar = notification.querySelector('.progress-bar');
            if (!progressBar) {
                progressBar = document.createElement('div');
                progressBar.className = 'progress-bar';
                progressBar.innerHTML = '<div class="progress-fill"></div>';
                notification.querySelector('.toast-content').appendChild(progressBar);
            }
            
            const progressFill = progressBar.querySelector('.progress-fill');
            progressFill.style.width = `${percentage}%`;
            
            return id;
        } else {
            // Create new progress notification
            const notification = this.createNotification(
                id, 
                'info', 
                `${title} (${percentage}%)`, 
                message, 
                [], 
                true
            );
            
            // Add progress bar
            const progressBar = document.createElement('div');
            progressBar.className = 'progress-bar';
            progressBar.innerHTML = `<div class="progress-fill" style="width: ${percentage}%"></div>`;
            notification.querySelector('.toast-content').appendChild(progressBar);
            
            this.container.appendChild(notification);
            this.notifications.set(id, notification);
            
            requestAnimationFrame(() => {
                notification.classList.add('show');
            });
            
            return id;
        }
    }
    
    // Workflow-specific notifications
    workflowStarted(projectName) {
        return this.loading(
            'Analysis started',
            `Processing "${projectName}"...`,
            { persistent: true }
        );
    }
    
    workflowProgress(step, total, stepName) {
        const percentage = Math.round((step / total) * 100);
        return this.progress(
            'Processing analysis',
            `Step ${step}/${total}: ${stepName}`,
            percentage,
            { id: 'workflow-progress' }
        );
    }
    
    workflowCompleted(projectName, results) {
        this.remove('workflow-progress');
        
        const actions = [
            {
                text: 'Ver resultados',
                class: 'btn-primary',
                onclick: 'app.showResultsSection()',
                icon: 'fas fa-eye'
            }
        ];
        
        return this.success(
            'Analysis completed',
            `"${projectName}" processed successfully`,
            { actions, duration: 10000 }
        );
    }
    
    workflowError(error) {
        this.remove('workflow-progress');
        
        const actions = [
            {
                text: 'Reintentar',
                class: 'btn-outline',
                onclick: 'app.retryWorkflow()',
                icon: 'fas fa-redo'
            },
            {
                text: 'Soporte',
                class: 'btn-secondary',
                onclick: 'app.openSupport()',
                icon: 'fas fa-question-circle'
            }
        ];
        
        return this.error(
            'Analysis error',
            error.message || 'Unknown error',
            { actions, duration: 0 } // Persistent error
        );
    }
    
    // File operation notifications
    fileUploaded(filename) {
        return this.success(
            'Archivo cargado',
            `${filename} listo para análisis`
        );
    }
    
    fileDownloaded(filename) {
        return this.success(
            'Descarga iniciada',
            `Descargando ${filename}...`
        );
    }
    
    fileError(filename, error) {
        return this.error(
            'Error de archivo',
            `No se pudo procesar ${filename}: ${error}`
        );
    }
    
    // System notifications
    serviceUnavailable(serviceName) {
        return this.warning(
            'Servicio no disponible',
            `${serviceName} no está respondiendo. Verifica la configuración.`,
            { duration: 8000 }
        );
    }
    
    configurationSaved() {
        return this.success(
            'Configuration saved',
            'Your preferences have been saved'
        );
    }
    
    // Utility methods
    generateId() {
        return 'notification_' + Math.random().toString(36).substr(2, 9);
    }
    
    getNotificationCount() {
        return this.notifications.size;
    }
    
    hasNotification(id) {
        return this.notifications.has(id);
    }
    
    // Cleanup
    destroy() {
        this.removeAll();
        if (this.container && this.container.parentNode) {
            this.container.parentNode.removeChild(this.container);
        }
    }
}

// Initialize global notification manager
window.notifications = new NotificationManager();

// Add to app instance for convenience
document.addEventListener('DOMContentLoaded', () => {
    if (window.app) {
        window.app.showNotification = (type, title, message, options) => {
            return window.notifications.show(type, title, message, options);
        };
    }
});

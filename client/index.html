<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ROBO-RESEARCHER-2000 | Automated UX Research</title>
    <meta name="description" content="Semi-automated UX research system that transforms transcriptions into actionable insights">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="assets/icons/robot.svg">
    
    <!-- Bootstrap Lumen Theme -->
    <link href="https://bootswatch.com/5/lumen/bootstrap.css" rel="stylesheet">

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Custom Styles (after Bootstrap) -->
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/components.css">
</head>
<body>
    <!-- Header -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-robot me-2"></i>
                ROBO-RESEARCHER-2000
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#upload">Upload</a>
                    </li>
                    <li class="nav-item">
                        <button id="configBtn" class="nav-link btn btn-link text-white">
                            <i class="fas fa-cog me-1"></i> Settings
                        </button>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#help">Help</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main">
        <!-- Hero Section -->
        <section class="bg-light py-5">
            <div class="container">
                <div class="row justify-content-center text-center">
                    <div class="col-lg-8">
                        <h2 class="display-5 fw-bold mb-3">Transform transcriptions into actionable insights</h2>
                        <p class="lead mb-4">Semi-automated UX research system that analyzes user interviews and generates professional presentations in minutes.</p>
                        <div class="row g-4 mt-4">
                            <div class="col-md-4">
                                <div class="card border-0 bg-transparent">
                                    <div class="card-body">
                                        <h3 class="display-6 text-primary fw-bold">17</h3>
                                        <p class="text-muted mb-0">Automated steps</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card border-0 bg-transparent">
                                    <div class="card-body">
                                        <h3 class="display-6 text-primary fw-bold">15-20</h3>
                                        <p class="text-muted mb-0">Processing minutes</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card border-0 bg-transparent">
                                    <div class="card-body">
                                        <h3 class="display-6 text-primary fw-bold">100%</h3>
                                        <p class="text-muted mb-0">Open source</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Upload Section -->
        <section id="upload" class="py-5">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-lg-8">
                        <div class="text-center mb-5">
                            <h3><i class="fas fa-upload me-2"></i> Upload Transcription</h3>
                            <p class="text-muted">Upload your transcription file and configure analysis parameters</p>
                        </div>

                        <div class="card shadow">
                            <div class="card-body p-4">
                                <form id="uploadForm">
                                    <!-- Project Info -->
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label for="projectName" class="form-label">Project Name</label>
                                            <input type="text" class="form-control" id="projectName" name="projectName" required
                                                   placeholder="e.g., Mobile App Research Q1 2025">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="email" class="form-label">Email for Results</label>
                                            <input type="email" class="form-control" id="email" name="email" required
                                                   placeholder="<EMAIL>">
                                        </div>
                                    </div>

                                    <!-- File Upload -->
                                    <div class="mb-3">
                                        <label for="transcriptionFile" class="form-label">Transcription File (.txt)</label>
                                        <div class="border border-2 border-dashed rounded p-4 text-center" id="fileUploadArea" style="cursor: pointer;">
                                            <input type="file" id="transcriptionFile" name="transcriptionFile"
                                                   accept=".txt" required class="d-none">
                                            <div class="file-upload-content">
                                                <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                                <p class="mb-1">Drag your .txt file here or <span class="text-primary">click to select</span></p>
                                                <small class="text-muted">Maximum 10MB</small>
                                            </div>
                                        </div>
                                        <div id="fileInfo" class="mt-2 d-none"></div>
                                    </div>

                                    <!-- Study Context -->
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label for="studyType" class="form-label">Study Type</label>
                                            <select class="form-select" id="studyType" name="studyType" required>
                                                <option value="">Select type</option>
                                                <option value="user_interview">User Interview</option>
                                                <option value="usability_test">Usability Test</option>
                                                <option value="focus_group">Focus Group</option>
                                                <option value="contextual_inquiry">Contextual Inquiry</option>
                                                <option value="other">Other</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="objectives" class="form-label">Research Objectives</label>
                                            <textarea class="form-control" id="objectives" name="objectives" rows="3"
                                                      placeholder="Describe the main objectives of this research..."></textarea>
                                        </div>
                                    </div>

                                    <!-- Configuration Status -->
                                    <div class="mt-4">
                                        <h5><i class="fas fa-cog me-2"></i> Configuration Status</h5>
                                        <div id="configStatus" class="row g-2 mb-3">
                                            <div class="col-md-4">
                                                <div class="d-flex align-items-center" id="configOpenRouter">
                                                    <i class="fas fa-circle status-icon me-2"></i>
                                                    <div>
                                                        <div class="fw-semibold">OpenRouter API</div>
                                                        <small class="text-muted">Required for AI analysis</small>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="d-flex align-items-center" id="configSMTP">
                                                    <i class="fas fa-circle status-icon me-2"></i>
                                                    <div>
                                                        <div class="fw-semibold">Email Settings</div>
                                                        <small class="text-muted">Optional for notifications</small>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="d-flex align-items-center" id="configServices">
                                                    <i class="fas fa-circle status-icon me-2"></i>
                                                    <div>
                                                        <div class="fw-semibold">Service Endpoints</div>
                                                        <small class="text-muted">MinIO and Wiki.js URLs</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <button type="button" id="openConfigBtn" class="btn btn-outline-secondary">
                                            <i class="fas fa-cog me-1"></i> Configure Settings
                                        </button>
                                    </div>

                                    <!-- Advanced Options -->
                                    <div class="mt-4">
                                        <div class="accordion" id="advancedOptionsAccordion">
                                            <div class="accordion-item">
                                                <h2 class="accordion-header">
                                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#advancedOptions">
                                                        <i class="fas fa-sliders-h me-2"></i> Advanced Options
                                                    </button>
                                                </h2>
                                                <div id="advancedOptions" class="accordion-collapse collapse" data-bs-parent="#advancedOptionsAccordion">
                                                    <div class="accordion-body">
                                                        <div class="row g-3">
                                                            <div class="col-md-6">
                                                                <label for="language" class="form-label">Analysis Language</label>
                                                                <select class="form-select" id="language" name="language">
                                                                    <option value="en">English</option>
                                                                    <option value="es">Español</option>
                                                                </select>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <label for="analysisDepth" class="form-label">Analysis Depth</label>
                                                                <select class="form-select" id="analysisDepth" name="analysisDepth">
                                                                    <option value="standard">Standard (recommended)</option>
                                                                    <option value="deep">Deep (more time)</option>
                                                                    <option value="quick">Quick (less detail)</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                        <div class="mt-3">
                                                            <div class="form-check">
                                                                <input class="form-check-input" type="checkbox" id="enableSentiment" name="enableSentiment" checked>
                                                                <label class="form-check-label" for="enableSentiment">
                                                                    Sentiment analysis
                                                                </label>
                                                            </div>
                                                            <div class="form-check">
                                                                <input class="form-check-input" type="checkbox" id="enableEntities" name="enableEntities" checked>
                                                                <label class="form-check-label" for="enableEntities">
                                                                    Entity extraction
                                                                </label>
                                                            </div>
                                                            <div class="form-check">
                                                                <input class="form-check-input" type="checkbox" id="generatePresentation" name="generatePresentation" checked>
                                                                <label class="form-check-label" for="generatePresentation">
                                                                    Generate automatic presentation
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Submit Button -->
                                    <div class="d-grid mt-4">
                                        <button type="submit" class="btn btn-primary btn-lg" id="submitBtn">
                                            <i class="fas fa-rocket me-2"></i>
                                            Start Analysis
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Progress Section -->
        <section id="progress" class="py-5 d-none">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-lg-8">
                        <div class="text-center mb-4">
                            <h3><i class="fas fa-tasks me-2"></i> Analysis Progress</h3>
                            <p class="text-muted">Your transcription is being processed...</p>
                        </div>

                        <div class="card shadow">
                            <div class="card-body p-4">
                                <div class="progress mb-3" style="height: 10px;">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated"
                                         id="progressFill" role="progressbar" style="width: 0%"></div>
                                </div>
                                <div class="text-center mb-4">
                                    <span id="progressText" class="text-muted">Starting analysis...</span>
                                </div>

                                <div id="progressSteps" class="row g-3">
                                    <!-- Steps will be populated by JavaScript -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Results Section -->
        <section id="results" class="py-5 d-none">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-lg-8">
                        <div class="text-center mb-4">
                            <h3><i class="fas fa-chart-line me-2"></i> Analysis Complete</h3>
                            <p class="text-muted">Your research has been processed and documented</p>
                        </div>

                        <div id="resultsContainer">
                            <div class="card shadow">
                                <div class="card-header bg-success text-white">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-book-open me-2"></i>
                                        Research Documentation
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <p class="card-text">Your analysis has been completed and documented in Wiki.js with detailed insights, visualizations, and recommendations.</p>
                                    <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                                        <button id="viewWikiBtn" class="btn btn-primary">
                                            <i class="fas fa-external-link-alt me-1"></i>
                                            View Results in Wiki
                                        </button>
                                        <button id="newAnalysisBtn" class="btn btn-outline-secondary">
                                            <i class="fas fa-plus me-1"></i>
                                            Start New Analysis
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-light py-5 mt-5">
        <div class="container">
            <div class="row g-4">
                <div class="col-md-4">
                    <h5>ROBO-RESEARCHER-2000</h5>
                    <p class="text-muted">Automated UX research system with open-source tools.</p>
                </div>
                <div class="col-md-4">
                    <h5>Links</h5>
                    <ul class="list-unstyled">
                        <li><a href="https://github.com/your-username/robo-researcher-2000" class="text-muted text-decoration-none">GitHub</a></li>
                        <li><a href="#help" class="text-muted text-decoration-none">Documentation</a></li>
                        <li><a href="#contact" class="text-muted text-decoration-none">Support</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Technologies</h5>
                    <ul class="list-unstyled text-muted">
                        <li>n8n Workflows</li>
                        <li>Python + NLTK/spaCy</li>
                        <li>MinIO Storage</li>
                        <li>Wiki.js Docs</li>
                    </ul>
                </div>
            </div>
            <hr class="my-4">
            <div class="text-center">
                <p class="text-muted mb-0">&copy; 2025 ROBO-RESEARCHER-2000. Open Source under MIT license.</p>
            </div>
        </div>
    </footer>

    <!-- Toast Notifications -->
    <div id="toastContainer" class="toast-container"></div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay hidden">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <h3>Processing analysis...</h3>
            <p id="loadingMessage">Starting UX research workflow</p>
        </div>
    </div>

    <!-- Configuration Modal -->
    <div class="modal fade" id="configModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-cog me-2"></i> Configuration Settings</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="configForm">
                        <!-- OpenRouter API Configuration -->
                        <div class="mb-4">
                            <h6 class="border-bottom pb-2"><i class="fas fa-robot me-2"></i> OpenRouter API</h6>
                            <div class="mb-3">
                                <label for="configOpenRouterKey" class="form-label">API Key</label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="configOpenRouterKey" name="openRouterKey"
                                           placeholder="sk-or-v1-...">
                                    <button class="btn btn-outline-secondary toggle-password" type="button" data-target="configOpenRouterKey">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="form-text">Required for AI analysis. <a href="https://openrouter.ai/keys" target="_blank">Get API Key</a></div>
                            </div>
                            <button type="button" class="btn btn-outline-success btn-sm" data-test="openrouter">
                                <i class="fas fa-check-circle me-1"></i> Test Connection
                            </button>
                        </div>

                        <!-- SMTP Configuration -->
                        <div class="mb-4">
                            <h6 class="border-bottom pb-2"><i class="fas fa-envelope me-2"></i> Email Settings (Optional)</h6>
                            <div class="row g-3">
                                <div class="col-md-8">
                                    <label for="configSmtpHost" class="form-label">SMTP Host</label>
                                    <input type="text" class="form-control" id="configSmtpHost" name="smtpHost"
                                           placeholder="smtp.gmail.com">
                                </div>
                                <div class="col-md-4">
                                    <label for="configSmtpPort" class="form-label">Port</label>
                                    <input type="number" class="form-control" id="configSmtpPort" name="smtpPort"
                                           placeholder="587" value="587">
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="configSmtpUsername" class="form-label">Username</label>
                                <input type="email" class="form-control" id="configSmtpUsername" name="smtpUsername"
                                       placeholder="<EMAIL>">
                            </div>
                            <div class="mb-3">
                                <label for="configSmtpPassword" class="form-label">Password</label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="configSmtpPassword" name="smtpPassword"
                                           placeholder="App password or SMTP password">
                                    <button class="btn btn-outline-secondary toggle-password" type="button" data-target="configSmtpPassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                            <button type="button" class="btn btn-outline-success btn-sm" data-test="smtp">
                                <i class="fas fa-check-circle me-1"></i> Test Email
                            </button>
                        </div>

                        <!-- Service Endpoints -->
                        <div class="mb-4">
                            <h6 class="border-bottom pb-2"><i class="fas fa-server me-2"></i> Service Endpoints</h6>
                            <div class="mb-3">
                                <label for="configMinioEndpoint" class="form-label">MinIO Endpoint</label>
                                <input type="url" class="form-control" id="configMinioEndpoint" name="minioEndpoint"
                                       placeholder="http://localhost:9002" value="http://localhost:9002">
                            </div>
                            <div class="mb-3">
                                <label for="configWikijsUrl" class="form-label">Wiki.js URL</label>
                                <input type="url" class="form-control" id="configWikijsUrl" name="wikijsUrl"
                                       placeholder="http://localhost:3002" value="http://localhost:3002">
                            </div>
                            <div class="mb-3">
                                <label for="configN8nUrl" class="form-label">n8n Webhook URL</label>
                                <input type="url" class="form-control" id="configN8nUrl" name="n8nUrl"
                                       placeholder="http://localhost:5678" value="http://localhost:5678">
                            </div>
                            <button type="button" class="btn btn-outline-success btn-sm" data-test="services">
                                <i class="fas fa-check-circle me-1"></i> Test Services
                            </button>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-danger" id="resetConfigBtn">
                        <i class="fas fa-trash me-1"></i> Reset All
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" id="saveConfigBtn">
                        <i class="fas fa-save me-1"></i> Save Configuration
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Application Scripts -->
    <script src="js/config.js"></script>
    <script src="js/app.js"></script>
    <script src="js/api.js"></script>
    <script src="js/notifications.js"></script>
</body>
</html>

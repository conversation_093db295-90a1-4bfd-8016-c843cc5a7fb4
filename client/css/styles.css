/* ROBO-RESEARCHER-2000 Custom Styles for Bootstrap */

/* Custom variables to complement Bootstrap */
:root {
  --robo-transition: all 0.2s ease-in-out;
  --robo-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
}

/* Custom enhancements for Bootstrap components */

/* File upload area styling */
.file-upload-area {
  transition: var(--robo-transition);
  cursor: pointer;
}

.file-upload-area:hover,
.file-upload-area.dragover {
  border-color: var(--bs-primary) !important;
  background-color: var(--bs-light);
}

/* Progress step cards */
.progress-step {
  transition: var(--robo-transition);
  border: 2px solid var(--bs-border-color);
}

.progress-step .step-icon {
  transition: var(--robo-transition);
}

/* Loading overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  text-align: center;
  color: white;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Toast notifications */
.toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1050;
}

/* Status icons */
.status-icon {
  font-size: 0.75rem;
}

.status-icon.text-success {
  color: var(--bs-success) !important;
}

.status-icon.text-warning {
  color: var(--bs-warning) !important;
}

.status-icon.text-danger {
  color: var(--bs-danger) !important;
}

/* Password toggle buttons */
.toggle-password {
  cursor: pointer;
}

/* Responsive improvements */
@media (max-width: 768px) {
  .navbar-brand {
    font-size: 1rem;
  }

  .display-5 {
    font-size: 2rem !important;
  }

  .lead {
    font-size: 1rem !important;
  }

  .card-body {
    padding: 1rem !important;
  }

  .modal-dialog {
    margin: 0.5rem !important;
  }

  .progress-step .step-icon {
    width: 30px !important;
    height: 30px !important;
    font-size: 0.8rem !important;
  }

  .step-title {
    font-size: 0.9rem !important;
  }

  .step-description {
    font-size: 0.75rem !important;
  }
}

@media (max-width: 576px) {
  .container {
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
  }

  .py-5 {
    padding-top: 2rem !important;
    padding-bottom: 2rem !important;
  }

  .btn-lg {
    padding: 0.5rem 1rem !important;
    font-size: 1rem !important;
  }
}




# ROBO-RESEARCHER-2000 Client Application Dockerfile
FROM nginx:alpine

# Install curl for health checks
RUN apk add --no-cache curl

# Remove default nginx website
RUN rm -rf /usr/share/nginx/html/*

# Copy client application files
COPY . /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Expose port 80
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:80/health || exit 1

# Labels for metadata
LABEL maintainer="ROBO-RESEARCHER-2000 Team"
LABEL description="Client web application for ROBO-RESEARCHER-2000 UX research analysis system"
LABEL version="1.0.0"

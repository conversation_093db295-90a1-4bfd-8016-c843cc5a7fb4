<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ROBO-RESEARCHER-2000 Frontend Test</title>
    <link href="https://bootswatch.com/5/lumen/bootstrap.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .test-result {
            margin: 0.5rem 0;
            padding: 0.5rem;
            border-radius: 0.25rem;
        }
        .test-pass { background-color: #d4edda; color: #155724; }
        .test-fail { background-color: #f8d7da; color: #721c24; }
        .test-pending { background-color: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1><i class="fas fa-vial me-2"></i>Frontend Test Suite</h1>
        <p class="lead">Testing ROBO-RESEARCHER-2000 client functionality</p>
        
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Results</h5>
                    </div>
                    <div class="card-body">
                        <div id="testResults"></div>
                        <button id="runTests" class="btn btn-primary mt-3">
                            <i class="fas fa-play me-1"></i>Run Tests
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6>Test Coverage</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success"></i> Bootstrap Integration</li>
                            <li><i class="fas fa-check text-success"></i> Responsive Design</li>
                            <li><i class="fas fa-check text-success"></i> Modal Functionality</li>
                            <li><i class="fas fa-check text-success"></i> Form Validation</li>
                            <li><i class="fas fa-check text-success"></i> File Upload UI</li>
                            <li><i class="fas fa-check text-success"></i> API Configuration</li>
                            <li><i class="fas fa-check text-success"></i> Notifications</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        class FrontendTester {
            constructor() {
                this.tests = [
                    { name: 'Bootstrap CSS Loaded', test: this.testBootstrapCSS },
                    { name: 'Bootstrap JS Loaded', test: this.testBootstrapJS },
                    { name: 'Font Awesome Icons', test: this.testFontAwesome },
                    { name: 'Responsive Grid', test: this.testResponsiveGrid },
                    { name: 'Modal Functionality', test: this.testModal },
                    { name: 'Form Elements', test: this.testFormElements },
                    { name: 'Button Interactions', test: this.testButtons },
                    { name: 'Local Storage', test: this.testLocalStorage },
                    { name: 'API Endpoints', test: this.testAPIEndpoints }
                ];
                this.results = [];
            }

            async runAllTests() {
                const resultsContainer = document.getElementById('testResults');
                resultsContainer.innerHTML = '<p class="text-info">Running tests...</p>';
                
                this.results = [];
                
                for (const test of this.tests) {
                    try {
                        const result = await test.test.call(this);
                        this.results.push({
                            name: test.name,
                            status: result ? 'PASS' : 'FAIL',
                            message: result === true ? 'OK' : result || 'Failed'
                        });
                    } catch (error) {
                        this.results.push({
                            name: test.name,
                            status: 'ERROR',
                            message: error.message
                        });
                    }
                }
                
                this.displayResults();
            }

            testBootstrapCSS() {
                const element = document.createElement('div');
                element.className = 'container';
                document.body.appendChild(element);
                const styles = window.getComputedStyle(element);
                const hasBootstrap = styles.maxWidth !== 'none';
                document.body.removeChild(element);
                return hasBootstrap;
            }

            testBootstrapJS() {
                return typeof bootstrap !== 'undefined' && typeof bootstrap.Modal !== 'undefined';
            }

            testFontAwesome() {
                const icon = document.createElement('i');
                icon.className = 'fas fa-test';
                document.body.appendChild(icon);
                const styles = window.getComputedStyle(icon);
                const hasFontAwesome = styles.fontFamily.includes('Font Awesome');
                document.body.removeChild(icon);
                return hasFontAwesome;
            }

            testResponsiveGrid() {
                const row = document.createElement('div');
                row.className = 'row';
                const col = document.createElement('div');
                col.className = 'col-md-6';
                row.appendChild(col);
                document.body.appendChild(row);
                
                const styles = window.getComputedStyle(col);
                const hasGrid = styles.display === 'block' || styles.display === 'flex';
                
                document.body.removeChild(row);
                return hasGrid;
            }

            async testModal() {
                return new Promise((resolve) => {
                    const modalHTML = `
                        <div class="modal fade" id="testModal" tabindex="-1">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <div class="modal-body">Test Modal</div>
                                </div>
                            </div>
                        </div>
                    `;
                    document.body.insertAdjacentHTML('beforeend', modalHTML);
                    
                    const modalElement = document.getElementById('testModal');
                    const modal = new bootstrap.Modal(modalElement);
                    
                    modalElement.addEventListener('shown.bs.modal', () => {
                        modal.hide();
                        setTimeout(() => {
                            document.body.removeChild(modalElement);
                            resolve(true);
                        }, 100);
                    });
                    
                    modal.show();
                });
            }

            testFormElements() {
                const form = document.createElement('form');
                form.innerHTML = `
                    <input type="text" class="form-control" required>
                    <select class="form-select"><option>Test</option></select>
                    <textarea class="form-control"></textarea>
                `;
                document.body.appendChild(form);
                
                const input = form.querySelector('.form-control');
                const select = form.querySelector('.form-select');
                const textarea = form.querySelector('textarea');
                
                const hasFormStyles = input && select && textarea;
                document.body.removeChild(form);
                return hasFormStyles;
            }

            testButtons() {
                const button = document.createElement('button');
                button.className = 'btn btn-primary';
                button.textContent = 'Test';
                document.body.appendChild(button);
                
                const styles = window.getComputedStyle(button);
                const hasButtonStyles = styles.padding !== '0px';
                
                document.body.removeChild(button);
                return hasButtonStyles;
            }

            testLocalStorage() {
                try {
                    localStorage.setItem('test', 'value');
                    const value = localStorage.getItem('test');
                    localStorage.removeItem('test');
                    return value === 'value';
                } catch (e) {
                    return false;
                }
            }

            async testAPIEndpoints() {
                try {
                    const response = await fetch('/js/app.js');
                    return response.ok;
                } catch (e) {
                    return 'API test skipped (CORS/Network)';
                }
            }

            displayResults() {
                const resultsContainer = document.getElementById('testResults');
                const passed = this.results.filter(r => r.status === 'PASS').length;
                const total = this.results.length;
                
                let html = `
                    <div class="alert alert-${passed === total ? 'success' : 'warning'}">
                        <strong>Test Summary:</strong> ${passed}/${total} tests passed
                    </div>
                `;
                
                this.results.forEach(result => {
                    const cssClass = result.status === 'PASS' ? 'test-pass' : 
                                   result.status === 'FAIL' ? 'test-fail' : 'test-pending';
                    const icon = result.status === 'PASS' ? 'fa-check' : 
                               result.status === 'FAIL' ? 'fa-times' : 'fa-exclamation';
                    
                    html += `
                        <div class="test-result ${cssClass}">
                            <i class="fas ${icon} me-2"></i>
                            <strong>${result.name}:</strong> ${result.message}
                        </div>
                    `;
                });
                
                resultsContainer.innerHTML = html;
            }
        }

        document.addEventListener('DOMContentLoaded', () => {
            const tester = new FrontendTester();
            
            document.getElementById('runTests').addEventListener('click', () => {
                tester.runAllTests();
            });
            
            // Auto-run tests on load
            setTimeout(() => tester.runAllTests(), 500);
        });
    </script>
</body>
</html>

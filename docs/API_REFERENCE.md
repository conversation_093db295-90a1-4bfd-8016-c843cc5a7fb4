# ROBO-RESEARCHER-2000 API Reference 🔌

## Overview

The ROBO-RESEARCHER-2000 API provides programmatic access to the UX research automation platform. All endpoints are RESTful and return JSON responses.

## Base URL

```
http://localhost:5678
```

## Authentication

Currently, the API uses basic authentication for n8n endpoints. For production deployments, configure proper API keys.

## Endpoints

### 1. Workflow Execution

#### Start Analysis
**POST** `/webhook/robo-researcher`

Initiates a new UX research analysis workflow.

**Request Headers:**
```
Content-Type: application/json
```

**Request Body:**
```json
{
  "projectName": "string (required)",
  "email": "string (required, valid email)",
  "transcription": "string (required, min 100 chars)",
  "studyType": "string (optional)",
  "objectives": "string (optional)",
  "language": "string (optional, default: 'en')",
  "analysisDepth": "string (optional, default: 'standard')",
  "enableSentiment": "boolean (optional, default: true)",
  "enableEntities": "boolean (optional, default: true)",
  "generatePresentation": "boolean (optional, default: true)"
}
```

**Response (Success):**
```json
{
  "success": true,
  "executionId": "exec_2025-01-22T10-30-00-000Z",
  "message": "Analysis started successfully",
  "estimatedCompletion": "2025-01-22T10:50:00Z",
  "trackingUrl": "http://localhost:3002/projects/exec_2025-01-22T10-30-00-000Z"
}
```

**Response (Error):**
```json
{
  "success": false,
  "error": "Missing required fields: projectName, email",
  "step": "validation",
  "timestamp": "2025-01-22T10:30:00Z"
}
```

**Status Codes:**
- `200` - Success
- `400` - Invalid input data
- `429` - Rate limit exceeded
- `500` - Internal server error

### 2. System Health

#### Health Check
**GET** `/healthz`

Returns system health status.

**Response:**
```json
{
  "status": "ok",
  "timestamp": "2025-01-22T10:30:00Z",
  "version": "2025.1",
  "services": {
    "n8n": "healthy",
    "minio": "healthy",
    "wikijs": "healthy"
  }
}
```

### 3. Project Management

#### Get Project Status
**GET** `/api/projects/{executionId}`

Returns the status of a specific analysis project.

**Parameters:**
- `executionId` - The execution ID returned from the start analysis endpoint

**Response:**
```json
{
  "executionId": "exec_2025-01-22T10-30-00-000Z",
  "status": "running",
  "currentStep": 7,
  "totalSteps": 17,
  "progress": 41,
  "startTime": "2025-01-22T10:30:00Z",
  "estimatedCompletion": "2025-01-22T10:50:00Z",
  "results": {
    "wikiUrl": "http://localhost:3002/projects/exec_2025-01-22T10-30-00-000Z",
    "files": []
  }
}
```

**Status Values:**
- `queued` - Waiting to start
- `running` - Currently processing
- `completed` - Successfully finished
- `failed` - Error occurred
- `cancelled` - Manually cancelled

### 4. File Management

#### Download File
**GET** `/files/{executionId}/{filename}`

Downloads a specific file from the analysis results.

**Parameters:**
- `executionId` - The execution ID
- `filename` - The file to download

**Response:**
- Binary file content with appropriate Content-Type header

**Available Files:**
- `original.txt` - Original transcription
- `processed.txt` - Cleaned text
- `segments.json` - Segmented data
- `codes.json` - Coding results
- `analysis.json` - AI analysis
- `insights.json` - Generated insights
- `presentation.pptx` - Generated presentation
- `summary.pdf` - Executive summary

#### List Files
**GET** `/files/{executionId}`

Lists all available files for a project.

**Response:**
```json
{
  "executionId": "exec_2025-01-22T10-30-00-000Z",
  "files": [
    {
      "name": "original.txt",
      "size": 15420,
      "type": "text/plain",
      "url": "/files/exec_2025-01-22T10-30-00-000Z/original.txt",
      "created": "2025-01-22T10:30:00Z"
    },
    {
      "name": "presentation.pptx",
      "size": 2048576,
      "type": "application/vnd.openxmlformats-officedocument.presentationml.presentation",
      "url": "/files/exec_2025-01-22T10-30-00-000Z/presentation.pptx",
      "created": "2025-01-22T10:45:00Z"
    }
  ]
}
```

## Error Handling

### Error Response Format
```json
{
  "success": false,
  "error": "Error description",
  "code": "ERROR_CODE",
  "timestamp": "2025-01-22T10:30:00Z",
  "details": {
    "field": "Additional error details"
  }
}
```

### Common Error Codes
- `VALIDATION_ERROR` - Input validation failed
- `MISSING_CREDENTIALS` - API credentials not configured
- `RATE_LIMIT_EXCEEDED` - Too many requests
- `WORKFLOW_ERROR` - n8n workflow execution failed
- `STORAGE_ERROR` - File storage operation failed
- `AI_API_ERROR` - AI service unavailable

## Rate Limiting

- **Analysis Requests**: 5 per minute per IP
- **Status Checks**: 60 per minute per IP
- **File Downloads**: 30 per minute per IP

Rate limit headers are included in responses:
```
X-RateLimit-Limit: 5
X-RateLimit-Remaining: 4
X-RateLimit-Reset: 1642857600
```

## SDK Examples

### JavaScript/Node.js
```javascript
const axios = require('axios');

class RoboResearcherClient {
  constructor(baseUrl = 'http://localhost:5678') {
    this.baseUrl = baseUrl;
  }

  async startAnalysis(data) {
    const response = await axios.post(`${this.baseUrl}/webhook/robo-researcher`, data);
    return response.data;
  }

  async getProjectStatus(executionId) {
    const response = await axios.get(`${this.baseUrl}/api/projects/${executionId}`);
    return response.data;
  }

  async downloadFile(executionId, filename) {
    const response = await axios.get(`${this.baseUrl}/files/${executionId}/${filename}`, {
      responseType: 'stream'
    });
    return response.data;
  }
}

// Usage
const client = new RoboResearcherClient();

const analysis = await client.startAnalysis({
  projectName: 'Mobile App Research',
  email: '<EMAIL>',
  transcription: 'Interview transcript...'
});

console.log('Analysis started:', analysis.executionId);
```

### Python
```python
import requests
import json

class RoboResearcherClient:
    def __init__(self, base_url='http://localhost:5678'):
        self.base_url = base_url

    def start_analysis(self, data):
        response = requests.post(f'{self.base_url}/webhook/robo-researcher', json=data)
        return response.json()

    def get_project_status(self, execution_id):
        response = requests.get(f'{self.base_url}/api/projects/{execution_id}')
        return response.json()

    def download_file(self, execution_id, filename):
        response = requests.get(f'{self.base_url}/files/{execution_id}/{filename}')
        return response.content

# Usage
client = RoboResearcherClient()

analysis = client.start_analysis({
    'projectName': 'Mobile App Research',
    'email': '<EMAIL>',
    'transcription': 'Interview transcript...'
})

print(f"Analysis started: {analysis['executionId']}")
```

### cURL Examples

**Start Analysis:**
```bash
curl -X POST http://localhost:5678/webhook/robo-researcher \
  -H "Content-Type: application/json" \
  -d '{
    "projectName": "Mobile App Research",
    "email": "<EMAIL>",
    "transcription": "Interview transcript content..."
  }'
```

**Check Status:**
```bash
curl http://localhost:5678/api/projects/exec_2025-01-22T10-30-00-000Z
```

**Download File:**
```bash
curl -O http://localhost:5678/files/exec_2025-01-22T10-30-00-000Z/presentation.pptx
```

## Webhooks

### Result Notifications

Configure webhook URLs to receive notifications when analysis completes:

**POST** `{your-webhook-url}`

**Payload:**
```json
{
  "event": "analysis_completed",
  "executionId": "exec_2025-01-22T10-30-00-000Z",
  "projectName": "Mobile App Research",
  "status": "completed",
  "results": {
    "wikiUrl": "http://localhost:3002/projects/exec_2025-01-22T10-30-00-000Z",
    "files": ["presentation.pptx", "summary.pdf"]
  },
  "timestamp": "2025-01-22T10:45:00Z"
}
```

## Support

For API support and questions:
- GitHub Issues: [Create an issue](https://github.com/your-username/robo-researcher-2000/issues)
- Documentation: [Complete Documentation](./COMPLETE_DOCUMENTATION.md)
- Email: <EMAIL>

# ROBO-RESEARCHER-2000 Setup Guide

## Overview

This comprehensive setup guide will walk you through installing and configuring ROBO-RESEARCHER-2000 from scratch. Follow these steps to get your automated UX research system up and running.

## Prerequisites

### System Requirements

#### Minimum Requirements
- **OS**: Ubuntu 20.04+, macOS 10.15+, or Windows 10+ with WSL2
- **CPU**: 2 cores
- **RAM**: 8GB
- **Storage**: 20GB free space
- **Network**: Stable internet connection

#### Recommended Requirements
- **OS**: Ubuntu 22.04 LTS
- **CPU**: 4+ cores
- **RAM**: 16GB
- **Storage**: 50GB+ SSD
- **Network**: High-speed internet for AI API calls

### Software Dependencies

#### Required Software
```bash
# Docker & Docker Compose
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh
sudo usermod -aG docker $USER

# Docker Compose (if not included)
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Git
sudo apt update && sudo apt install git -y

# Python 3.8+ (for testing and scripts)
sudo apt install python3 python3-pip -y
```

#### Optional Software
```bash
# Node.js (for client development)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# MinIO Client (for storage management)
curl -O https://dl.min.io/client/mc/release/linux-amd64/mc
chmod +x mc && sudo mv mc /usr/local/bin/
```

## Step 1: Repository Setup

### Clone Repository
```bash
# Clone the repository
git clone https://github.com/your-username/robo-researcher-2000.git
cd robo-researcher-2000

# Verify structure
ls -la
```

### Environment Configuration
```bash
# Create environment file
cp .env.example .env

# Edit configuration
nano .env
```

#### Required Environment Variables
```bash
# API Configuration
OPENROUTER_API_KEY=your_openrouter_api_key_here
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password

# Service Configuration
N8N_HOST=localhost
N8N_PORT=5678
MINIO_HOST=localhost
MINIO_PORT=9000
WIKIJS_HOST=localhost
WIKIJS_PORT=3000

# Database Configuration
POSTGRES_PASSWORD=secure_password_here
N8N_DB_PASSWORD=n8n_password_here
WIKIJS_DB_PASSWORD=wikijs_password_here

# Security
JWT_SECRET=$(openssl rand -base64 32)
ENCRYPTION_KEY=$(openssl rand -base64 32)
```

## Step 2: Infrastructure Deployment

### Quick Setup (Recommended)
```bash
# Navigate to infrastructure directory
cd infrastructure

# Make setup script executable
chmod +x setup.sh

# Run automated setup
./setup.sh

# Wait for services to start (5-10 minutes)
```

### Manual Setup (Advanced)
```bash
# Start services manually
docker-compose up -d

# Check service status
docker-compose ps

# View logs
docker-compose logs -f
```

### Verify Services
```bash
# Check service health
curl http://localhost:5678/healthz    # n8n
curl http://localhost:9000/minio/health/live  # MinIO
curl http://localhost:3000           # Wiki.js

# Check Docker containers
docker ps
```

## Step 3: Service Configuration

### n8n Configuration

#### Initial Setup
1. **Access n8n**: Open `http://localhost:5678`
2. **Create Admin Account**:
   - Email: `<EMAIL>`
   - Password: `robo-researcher-2000`
   - First Name: `Admin`
   - Last Name: `User`

#### Import Workflows
1. **Go to Workflows** → **Import from file**
2. **Import Test Workflow**:
   - Select `workflows/ux-research-test-simple-workflow.json`
   - Click **Import**
3. **Import Main Workflow**:
   - Select `workflows/ux-research-complete-17step-workflow.json`
   - Click **Import**

#### Configure Credentials
1. **Go to Settings** → **Credentials**
2. **Add OpenRouter API**:
   - Type: `HTTP Header Auth`
   - Name: `Authorization`
   - Value: `Bearer your_openrouter_api_key`
3. **Add MinIO S3**:
   - Type: `S3`
   - Access Key: `minioadmin`
   - Secret Key: `minioadmin`
   - Region: `us-east-1`
   - Custom Endpoint: `http://localhost:9000`
   - Force Path Style: `true`
4. **Add SMTP**:
   - Type: `SMTP`
   - Host: `smtp.gmail.com`
   - Port: `587`
   - Security: `STARTTLS`
   - Username: `<EMAIL>`
   - Password: `your_app_password`

### MinIO Configuration

#### Access MinIO Console
1. **Open**: `http://localhost:9001`
2. **Login**:
   - Username: `minioadmin`
   - Password: `minioadmin`

#### Create Buckets
```bash
# Using MinIO client
mc alias set robo-researcher http://localhost:9000 minioadmin minioadmin

# Create required buckets
mc mb robo-researcher/robo-researcher-data
mc mb robo-researcher/transcripts
mc mb robo-researcher/results
mc mb robo-researcher/presentations
mc mb robo-researcher/backups

# Set bucket policies (public read for results)
mc policy set public robo-researcher/results
```

### Wiki.js Configuration

#### Initial Setup
1. **Access Wiki.js**: Open `http://localhost:3000`
2. **Follow Setup Wizard**:
   - **Database Type**: PostgreSQL
   - **Host**: `robo-researcher-postgres`
   - **Port**: `5432`
   - **Database**: `wikijs`
   - **Username**: `wikijs`
   - **Password**: `wikijs_password`
3. **Create Admin Account**:
   - Email: `<EMAIL>`
   - Password: `robo-researcher-2000`

#### Configure API Access
1. **Go to Administration** → **API Access**
2. **Generate API Key**
3. **Copy key** for n8n configuration

## Step 4: Client Application Setup

### Serve Client Application
```bash
# Navigate to client directory
cd client

# Option 1: Python HTTP Server
python3 -m http.server 8080

# Option 2: Node.js serve (if Node.js installed)
npx serve . -p 8080

# Option 3: PHP built-in server (if PHP installed)
php -S localhost:8080
```

### Access Client
- **URL**: `http://localhost:8080`
- **Test the interface** with sample data

## Step 5: Testing and Validation

### Run Automated Tests
```bash
# Install Python dependencies
pip3 install -r requirements.txt

# Run deployment validation
python3 tests/deployment_validator.py

# Run integration tests
python3 tests/integration_test.py

# Run complete test suite
python3 tests/run_tests.py --mode all
```

### Manual Testing

#### Test Simple Workflow
```bash
curl -X POST http://localhost:5678/webhook/test-robo-researcher \
  -H "Content-Type: application/json" \
  -d '{
    "project_name": "Test Project",
    "email": "<EMAIL>",
    "transcription": "This is a test transcription for validation."
  }'
```

#### Test Client Application
1. **Open**: `http://localhost:8080`
2. **Fill form** with test data
3. **Upload** sample transcription
4. **Submit** and monitor progress

## Step 6: Production Preparation

### Security Configuration
```bash
# Change default passwords
# Update .env file with secure passwords

# Configure firewall
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw enable

# Set up SSL certificates (optional)
# Use Let's Encrypt or commercial certificates
```

### Backup Configuration
```bash
# Create backup script
cat > backup.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/$DATE"
mkdir -p $BACKUP_DIR

# Backup databases
docker exec robo-researcher-postgres pg_dump -U postgres n8n > $BACKUP_DIR/n8n.sql
docker exec robo-researcher-postgres pg_dump -U postgres wikijs > $BACKUP_DIR/wikijs.sql

# Backup MinIO data
mc mirror robo-researcher/robo-researcher-data $BACKUP_DIR/minio/

# Backup workflows
docker exec robo-researcher-n8n n8n export:workflow --all --output=$BACKUP_DIR/workflows.json

# Compress backup
tar -czf $BACKUP_DIR.tar.gz $BACKUP_DIR
rm -rf $BACKUP_DIR
EOF

chmod +x backup.sh

# Schedule daily backups
echo "0 2 * * * /path/to/backup.sh" | crontab -
```

## Troubleshooting

### Common Issues

#### Services Not Starting
```bash
# Check Docker status
sudo systemctl status docker

# Check container logs
docker-compose logs [service-name]

# Restart services
docker-compose restart [service-name]
```

#### Port Conflicts
```bash
# Check port usage
sudo netstat -tulpn | grep :5678

# Change ports in docker-compose.yml if needed
```

#### Permission Issues
```bash
# Fix Docker permissions
sudo usermod -aG docker $USER
newgrp docker

# Fix file permissions
sudo chown -R $USER:$USER .
```

### Getting Help

#### Documentation
- **User Manual**: `docs/user-manual.md`
- **API Reference**: `docs/api-reference.md`
- **Deployment Guide**: `docs/deployment-guide.md`

#### Support Channels
- **GitHub Issues**: Report bugs and feature requests
- **Community Forum**: Ask questions and share experiences
- **Email Support**: `<EMAIL>`

## Next Steps

### After Successful Setup
1. **Configure API keys** for production use
2. **Import your first transcription** for analysis
3. **Customize coding schemes** for your domain
4. **Set up monitoring** and alerting
5. **Train your team** on the system

### Advanced Configuration
- **Custom AI prompts** for domain-specific analysis
- **Integration** with existing tools (Slack, Jira, etc.)
- **Custom presentation templates**
- **Advanced security** configurations

Congratulations! Your ROBO-RESEARCHER-2000 system is now ready for automated UX research analysis.

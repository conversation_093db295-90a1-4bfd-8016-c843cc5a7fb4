# ROBO-RESEARCHER-2000 Deployment Guide 🚀

## Overview

This guide covers deployment options for ROBO-RESEARCHER-2000, from local development to production environments.

## 📋 Table of Contents

1. [Local Development](#local-development)
2. [Docker Deployment](#docker-deployment)
3. [Production Deployment](#production-deployment)
4. [Cloud Deployment](#cloud-deployment)
5. [Configuration](#configuration)
6. [Monitoring](#monitoring)
7. [Backup & Recovery](#backup--recovery)

## 🏠 Local Development

### Prerequisites
- Docker & Docker Compose
- Node.js 18+
- Git
- 4GB+ RAM
- 10GB+ disk space

### Quick Start
```bash
# Clone repository
git clone https://github.com/your-username/robo-researcher-2000.git
cd robo-researcher-2000

# Start development environment
docker-compose -f docker-compose.dev.yml up -d

# Verify services
node test-connectivity.js
```

### Development Services
- **Client**: http://localhost:8081 (development server)
- **n8n**: http://localhost:5678
- **MinIO**: http://localhost:9002
- **Wiki.js**: http://localhost:3002

## 🐳 Docker Deployment

### Single-Host Deployment

**docker-compose.yml**
```yaml
version: '3.8'

services:
  n8n:
    image: n8nio/n8n:latest
    ports:
      - "5678:5678"
    environment:
      - N8N_HOST=localhost
      - N8N_PROTOCOL=http
      - N8N_SECURE_COOKIE=false
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin
      - N8N_BASIC_AUTH_PASSWORD=robo-researcher-2000
    volumes:
      - n8n_data:/home/<USER>/.n8n
      - ./workflows:/home/<USER>/.n8n/workflows

  minio:
    image: minio/minio:latest
    ports:
      - "9002:9000"
      - "9001:9001"
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin
    command: server /data --console-address ":9001"
    volumes:
      - minio_data:/data

  wikijs:
    image: requarks/wiki:2
    ports:
      - "3002:3000"
    environment:
      - DB_TYPE=sqlite
      - DB_FILEPATH=/wiki/database.sqlite
    volumes:
      - wikijs_data:/wiki

  client:
    image: nginx:alpine
    ports:
      - "8080:80"
    volumes:
      - ./client:/usr/share/nginx/html:ro

volumes:
  n8n_data:
  minio_data:
  wikijs_data:
```

### Deployment Commands
```bash
# Start all services
docker-compose up -d

# Check status
docker-compose ps

# View logs
docker-compose logs -f

# Stop services
docker-compose down

# Update services
docker-compose pull
docker-compose up -d
```

## 🌐 Production Deployment

### Infrastructure Requirements

#### Minimum Specifications
- **CPU**: 4 cores
- **RAM**: 8GB
- **Storage**: 50GB SSD
- **Network**: 100Mbps

#### Recommended Specifications
- **CPU**: 8 cores
- **RAM**: 16GB
- **Storage**: 200GB SSD
- **Network**: 1Gbps

### Production Configuration

**docker-compose.prod.yml**
```yaml
version: '3.8'

services:
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - ./client:/usr/share/nginx/html:ro
    depends_on:
      - n8n
      - wikijs

  n8n:
    image: n8nio/n8n:latest
    environment:
      - N8N_HOST=robo.yourdomain.com
      - N8N_PROTOCOL=https
      - N8N_SECURE_COOKIE=true
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=${N8N_USER}
      - N8N_BASIC_AUTH_PASSWORD=${N8N_PASSWORD}
      - DB_TYPE=postgresdb
      - DB_POSTGRESDB_HOST=postgres
      - DB_POSTGRESDB_DATABASE=n8n
      - DB_POSTGRESDB_USER=${POSTGRES_USER}
      - DB_POSTGRESDB_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - n8n_data:/home/<USER>/.n8n
    depends_on:
      - postgres

  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=n8n
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data

  minio:
    image: minio/minio:latest
    environment:
      - MINIO_ROOT_USER=${MINIO_USER}
      - MINIO_ROOT_PASSWORD=${MINIO_PASSWORD}
    command: server /data --console-address ":9001"
    volumes:
      - minio_data:/data

  wikijs:
    image: requarks/wiki:2
    environment:
      - DB_TYPE=postgres
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=wikijs
      - DB_USER=${POSTGRES_USER}
      - DB_PASS=${POSTGRES_PASSWORD}
    depends_on:
      - postgres

volumes:
  n8n_data:
  postgres_data:
  minio_data:
```

### SSL Configuration

**nginx.conf**
```nginx
events {
    worker_connections 1024;
}

http {
    upstream n8n {
        server n8n:5678;
    }

    upstream wikijs {
        server wikijs:3000;
    }

    server {
        listen 80;
        server_name robo.yourdomain.com;
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name robo.yourdomain.com;

        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;

        location / {
            root /usr/share/nginx/html;
            index index.html;
        }

        location /webhook/ {
            proxy_pass http://n8n;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }

        location /wiki/ {
            proxy_pass http://wikijs/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }
}
```

## ☁️ Cloud Deployment

### AWS Deployment

#### Using ECS Fargate
```yaml
# ecs-task-definition.json
{
  "family": "robo-researcher-2000",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "2048",
  "memory": "4096",
  "executionRoleArn": "arn:aws:iam::account:role/ecsTaskExecutionRole",
  "containerDefinitions": [
    {
      "name": "n8n",
      "image": "n8nio/n8n:latest",
      "portMappings": [
        {
          "containerPort": 5678,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "N8N_HOST",
          "value": "robo.yourdomain.com"
        }
      ]
    }
  ]
}
```

#### Using EC2
```bash
# User data script for EC2 instance
#!/bin/bash
yum update -y
yum install -y docker
service docker start
usermod -a -G docker ec2-user

# Install Docker Compose
curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# Clone and start application
git clone https://github.com/your-username/robo-researcher-2000.git
cd robo-researcher-2000
docker-compose -f docker-compose.prod.yml up -d
```

### Google Cloud Platform

#### Using Cloud Run
```yaml
# cloudrun.yaml
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: robo-researcher-2000
spec:
  template:
    metadata:
      annotations:
        autoscaling.knative.dev/maxScale: "10"
    spec:
      containers:
      - image: gcr.io/project-id/robo-researcher-2000
        ports:
        - containerPort: 8080
        env:
        - name: N8N_HOST
          value: "robo.yourdomain.com"
```

### Azure Deployment

#### Using Container Instances
```bash
# Deploy to Azure Container Instances
az container create \
  --resource-group robo-researcher-rg \
  --name robo-researcher-2000 \
  --image your-registry/robo-researcher-2000:latest \
  --ports 80 443 \
  --dns-name-label robo-researcher \
  --environment-variables \
    N8N_HOST=robo-researcher.azurecontainer.io
```

## ⚙️ Configuration

### Environment Variables

**.env.prod**
```bash
# Application
APP_ENV=production
APP_DEBUG=false

# n8n Configuration
N8N_HOST=robo.yourdomain.com
N8N_PROTOCOL=https
N8N_SECURE_COOKIE=true
N8N_USER=admin
N8N_PASSWORD=secure-password-here

# Database
POSTGRES_USER=robo_user
POSTGRES_PASSWORD=secure-db-password
POSTGRES_DB=robo_researcher

# Storage
MINIO_USER=minio_admin
MINIO_PASSWORD=secure-minio-password

# API Keys
OPENROUTER_API_KEY=your-openrouter-key
SMTP_HOST=smtp.yourdomain.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=smtp-password

# Security
JWT_SECRET=your-jwt-secret
ENCRYPTION_KEY=your-encryption-key
```

### Security Configuration

#### Firewall Rules
```bash
# Allow HTTP/HTTPS
ufw allow 80/tcp
ufw allow 443/tcp

# Allow SSH (restrict to your IP)
ufw allow from YOUR_IP to any port 22

# Block all other traffic
ufw default deny incoming
ufw default allow outgoing
ufw enable
```

#### SSL Certificate (Let's Encrypt)
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Generate certificate
sudo certbot --nginx -d robo.yourdomain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 📊 Monitoring

### Health Checks
```bash
# Create health check script
cat > health-check.sh << 'EOF'
#!/bin/bash
curl -f http://localhost:5678/healthz || exit 1
curl -f http://localhost:3002 || exit 1
curl -f http://localhost:9002/minio/health/live || exit 1
EOF

chmod +x health-check.sh
```

### Logging Configuration
```yaml
# docker-compose.prod.yml logging section
logging:
  driver: "json-file"
  options:
    max-size: "10m"
    max-file: "3"
```

### Monitoring with Prometheus
```yaml
# Add to docker-compose.prod.yml
prometheus:
  image: prom/prometheus
  ports:
    - "9090:9090"
  volumes:
    - ./prometheus.yml:/etc/prometheus/prometheus.yml

grafana:
  image: grafana/grafana
  ports:
    - "3000:3000"
  environment:
    - GF_SECURITY_ADMIN_PASSWORD=admin
```

## 💾 Backup & Recovery

### Automated Backup Script
```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="/backups/$(date +%Y%m%d)"
mkdir -p $BACKUP_DIR

# Backup volumes
docker run --rm -v robo-researcher-2000_n8n_data:/data -v $BACKUP_DIR:/backup alpine tar czf /backup/n8n_data.tar.gz -C /data .
docker run --rm -v robo-researcher-2000_minio_data:/data -v $BACKUP_DIR:/backup alpine tar czf /backup/minio_data.tar.gz -C /data .
docker run --rm -v robo-researcher-2000_postgres_data:/data -v $BACKUP_DIR:/backup alpine tar czf /backup/postgres_data.tar.gz -C /data .

# Upload to S3 (optional)
aws s3 sync $BACKUP_DIR s3://your-backup-bucket/$(date +%Y%m%d)/

# Cleanup old backups (keep 30 days)
find /backups -type d -mtime +30 -exec rm -rf {} \;
```

### Recovery Process
```bash
#!/bin/bash
# restore.sh

BACKUP_DATE=$1
BACKUP_DIR="/backups/$BACKUP_DATE"

# Stop services
docker-compose down

# Restore volumes
docker run --rm -v robo-researcher-2000_n8n_data:/data -v $BACKUP_DIR:/backup alpine tar xzf /backup/n8n_data.tar.gz -C /data
docker run --rm -v robo-researcher-2000_minio_data:/data -v $BACKUP_DIR:/backup alpine tar xzf /backup/minio_data.tar.gz -C /data
docker run --rm -v robo-researcher-2000_postgres_data:/data -v $BACKUP_DIR:/backup alpine tar xzf /backup/postgres_data.tar.gz -C /data

# Start services
docker-compose up -d
```

## 🔧 Maintenance

### Update Procedure
```bash
# 1. Backup current state
./backup.sh

# 2. Pull latest images
docker-compose pull

# 3. Update with zero downtime
docker-compose up -d --no-deps --build service-name

# 4. Verify health
./health-check.sh
```

### Performance Tuning
```bash
# Increase Docker memory
echo '{"default-ulimits":{"memlock":{"Hard":-1,"Name":"memlock","Soft":-1}}}' > /etc/docker/daemon.json
systemctl restart docker

# Optimize PostgreSQL
echo "shared_preload_libraries = 'pg_stat_statements'" >> /var/lib/postgresql/data/postgresql.conf
echo "max_connections = 200" >> /var/lib/postgresql/data/postgresql.conf
```

---

**For deployment support, please refer to the troubleshooting section or create an issue on GitHub.**

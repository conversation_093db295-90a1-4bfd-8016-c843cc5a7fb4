# ROBO-RESEARCHER-2000 API Reference

## Overview

The ROBO-RESEARCHER-2000 API provides programmatic access to the automated UX research analysis system. The API is built on n8n webhooks and provides RESTful endpoints for triggering analysis workflows.

## Base URLs

- **Development**: `http://localhost:5678`
- **Production**: `https://your-domain.com`

## Authentication

### API Keys
For production deployments, API keys are required:

```bash
# Include in headers
Authorization: Bearer your-api-key-here
```

### Basic Authentication
n8n basic authentication (if enabled):

```bash
# Include in headers
Authorization: Basic base64(username:password)
```

## Endpoints

### 1. Health Check

Check system health and availability.

```http
GET /healthz
```

#### Response
```json
{
  "status": "ok",
  "timestamp": "2025-01-17T10:30:00Z",
  "services": {
    "n8n": "healthy",
    "minio": "healthy",
    "postgres": "healthy"
  }
}
```

### 2. Simple Analysis (Test Workflow)

Quick analysis for testing and validation.

```http
POST /webhook/test-robo-researcher
Content-Type: application/json
```

#### Request Body
```json
{
  "project_name": "Test Project",
  "email": "<EMAIL>",
  "transcription": "User interview transcription text here..."
}
```

#### Response
```json
{
  "success": true,
  "project_name": "Test Project",
  "email": "<EMAIL>",
  "analysis_summary": {
    "word_count": 150,
    "character_count": 890,
    "overall_sentiment": "positive",
    "positive_indicators": 3,
    "negative_indicators": 1
  },
  "processing_steps": [
    {
      "step": 1,
      "name": "Input Validation",
      "status": "completed",
      "duration": "0.1s"
    }
  ],
  "test_mode": true,
  "timestamp": "2025-01-17T10:30:00Z"
}
```

### 3. Complete Analysis (Main Workflow)

Full 17-step analysis workflow.

```http
POST /webhook/robo-researcher
Content-Type: application/json
```

#### Request Body
```json
{
  "project_name": "Mobile App UX Research",
  "email": "<EMAIL>",
  "transcription": "Complete interview transcription...",
  "study_type": "user_interview",
  "objectives": "Evaluate mobile app usability and identify pain points",
  "language": "en",
  "analysis_depth": "standard",
  "api_keys": {
    "openrouter": "sk-or-your-key-here",
    "smtp_password": "your-smtp-password"
  },
  "options": {
    "enable_sentiment": true,
    "enable_entities": true,
    "generate_presentation": true,
    "custom_codes": []
  }
}
```

#### Request Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `project_name` | string | Yes | Descriptive name for the research project |
| `email` | string | Yes | Email address for results delivery |
| `transcription` | string | Yes | Raw interview transcription (min 100 chars) |
| `study_type` | string | No | Type of study (default: "user_interview") |
| `objectives` | string | No | Research objectives and goals |
| `language` | string | No | Transcription language (default: "en") |
| `analysis_depth` | string | No | Analysis depth: "standard" or "deep" |
| `api_keys` | object | Yes | Required API keys for external services |
| `options` | object | No | Additional processing options |

#### Study Types
- `user_interview`: One-on-one user interviews
- `usability_test`: Moderated usability testing sessions
- `focus_group`: Group discussions and feedback sessions
- `contextual_inquiry`: Field research and observation
- `card_sorting`: Information architecture research

#### Analysis Depth Options
- `standard`: Basic coding and analysis (15-20 minutes)
- `deep`: Advanced AI analysis with detailed insights (25-30 minutes)

#### Response
```json
{
  "success": true,
  "execution_id": "exec_20250117_103000",
  "project_name": "Mobile App UX Research",
  "email": "<EMAIL>",
  "status": "processing",
  "estimated_completion": "2025-01-17T11:00:00Z",
  "progress_url": "/api/v1/executions/exec_20250117_103000",
  "webhook_url": "/webhook/status/exec_20250117_103000",
  "timestamp": "2025-01-17T10:30:00Z"
}
```

### 4. Execution Status

Check the status of a running analysis.

```http
GET /api/v1/executions/{execution_id}
```

#### Response
```json
{
  "execution_id": "exec_20250117_103000",
  "status": "running",
  "progress": {
    "current_step": 7,
    "total_steps": 17,
    "percentage": 41,
    "current_step_name": "Open Coding AI",
    "estimated_remaining": "12 minutes"
  },
  "steps": [
    {
      "step": 1,
      "name": "Webhook Trigger",
      "status": "completed",
      "duration": "0.1s",
      "timestamp": "2025-01-17T10:30:01Z"
    },
    {
      "step": 7,
      "name": "Open Coding AI",
      "status": "running",
      "started_at": "2025-01-17T10:35:00Z"
    }
  ],
  "results_available": false,
  "error": null
}
```

#### Status Values
- `queued`: Analysis is queued for processing
- `running`: Analysis is currently processing
- `completed`: Analysis completed successfully
- `failed`: Analysis failed with errors
- `cancelled`: Analysis was cancelled by user

### 5. Results Retrieval

Get analysis results after completion.

```http
GET /api/v1/results/{execution_id}
```

#### Response
```json
{
  "execution_id": "exec_20250117_103000",
  "project_name": "Mobile App UX Research",
  "status": "completed",
  "completion_time": "2025-01-17T10:50:00Z",
  "duration": "20 minutes",
  "results": {
    "executive_summary": {
      "key_findings": [
        "Navigation confusion is the primary usability issue",
        "Users appreciate the visual design but struggle with functionality",
        "Checkout process needs significant improvement"
      ],
      "sentiment_analysis": {
        "overall": "mixed",
        "positive": 35,
        "negative": 45,
        "neutral": 20
      },
      "priority_recommendations": [
        {
          "title": "Redesign navigation structure",
          "impact": "high",
          "effort": "medium",
          "rice_score": 8.5
        }
      ]
    },
    "quantitative_analysis": {
      "total_segments": 45,
      "coded_segments": 42,
      "unique_codes": 15,
      "participants": 1,
      "code_frequencies": {
        "navigation_confusion": 8,
        "visual_appeal": 5,
        "checkout_frustration": 6
      }
    },
    "user_archetypes": [
      {
        "name": "Frustrated Mobile User",
        "characteristics": ["Task-oriented", "Time-constrained", "Mobile-first"],
        "pain_points": ["Complex navigation", "Small buttons", "Slow loading"],
        "representative_quotes": ["The menu button wasn't obvious to me"]
      }
    ],
    "downloads": {
      "presentation": "/downloads/exec_20250117_103000/presentation.pptx",
      "report": "/downloads/exec_20250117_103000/report.pdf",
      "data": "/downloads/exec_20250117_103000/data.json",
      "visualizations": "/downloads/exec_20250117_103000/affinity_map.svg"
    }
  }
}
```

### 6. File Downloads

Download generated files and reports.

```http
GET /downloads/{execution_id}/{filename}
```

#### Available Files
- `presentation.pptx`: Executive presentation
- `presentation.pdf`: PDF version of presentation
- `report.pdf`: Detailed analysis report
- `data.json`: Raw analysis data
- `data.csv`: Coded segments in CSV format
- `affinity_map.svg`: Affinity mapping visualization
- `charts.png`: Statistical charts and graphs

## Error Handling

### Error Response Format
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Missing required field: transcription",
    "details": {
      "field": "transcription",
      "expected": "string with minimum 100 characters"
    }
  },
  "timestamp": "2025-01-17T10:30:00Z"
}
```

### Error Codes

| Code | Description | HTTP Status |
|------|-------------|-------------|
| `VALIDATION_ERROR` | Invalid input data | 400 |
| `AUTHENTICATION_ERROR` | Invalid or missing credentials | 401 |
| `AUTHORIZATION_ERROR` | Insufficient permissions | 403 |
| `NOT_FOUND` | Resource not found | 404 |
| `RATE_LIMIT_EXCEEDED` | Too many requests | 429 |
| `PROCESSING_ERROR` | Analysis processing failed | 500 |
| `SERVICE_UNAVAILABLE` | System temporarily unavailable | 503 |

## Rate Limiting

### Limits
- **Test Workflow**: 10 requests per minute
- **Main Workflow**: 2 requests per minute
- **Status Checks**: 60 requests per minute
- **File Downloads**: 30 requests per minute

### Headers
```http
X-RateLimit-Limit: 10
X-RateLimit-Remaining: 7
X-RateLimit-Reset: 1642428000
```

## Webhooks

### Status Updates
Register a webhook URL to receive real-time status updates:

```json
{
  "webhook_url": "https://your-app.com/webhook/status",
  "events": ["step_completed", "analysis_completed", "analysis_failed"]
}
```

### Webhook Payload
```json
{
  "event": "step_completed",
  "execution_id": "exec_20250117_103000",
  "step": {
    "number": 5,
    "name": "Segmentation",
    "status": "completed",
    "duration": "2.3s"
  },
  "progress": {
    "percentage": 29,
    "estimated_remaining": "15 minutes"
  },
  "timestamp": "2025-01-17T10:35:00Z"
}
```

## SDK and Libraries

### JavaScript/Node.js
```javascript
const RoboResearcher = require('robo-researcher-sdk');

const client = new RoboResearcher({
  baseUrl: 'http://localhost:5678',
  apiKey: 'your-api-key'
});

// Start analysis
const analysis = await client.analyze({
  projectName: 'My Research',
  email: '<EMAIL>',
  transcription: 'Interview text...'
});

// Monitor progress
const status = await client.getStatus(analysis.executionId);

// Get results
const results = await client.getResults(analysis.executionId);
```

### Python
```python
from robo_researcher import RoboResearcherClient

client = RoboResearcherClient(
    base_url='http://localhost:5678',
    api_key='your-api-key'
)

# Start analysis
analysis = client.analyze(
    project_name='My Research',
    email='<EMAIL>',
    transcription='Interview text...'
)

# Monitor progress
status = client.get_status(analysis['execution_id'])

# Get results
results = client.get_results(analysis['execution_id'])
```

### cURL Examples

#### Start Analysis
```bash
curl -X POST http://localhost:5678/webhook/robo-researcher \
  -H "Content-Type: application/json" \
  -d '{
    "project_name": "API Test",
    "email": "<EMAIL>",
    "transcription": "User: The app is confusing. Interviewer: What would you change? User: Make it simpler.",
    "api_keys": {
      "openrouter": "your-key"
    }
  }'
```

#### Check Status
```bash
curl -X GET http://localhost:5678/api/v1/executions/exec_20250117_103000
```

#### Download Results
```bash
curl -X GET http://localhost:5678/downloads/exec_20250117_103000/presentation.pptx \
  -o presentation.pptx
```

This API reference provides comprehensive documentation for integrating with ROBO-RESEARCHER-2000 programmatically.

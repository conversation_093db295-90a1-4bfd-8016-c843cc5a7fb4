# ROBO-RESEARCHER-2000 Deployment Guide

## Overview

This guide covers deployment options for ROBO-RESEARCHER-2000, from local development to production cloud deployment.

## Deployment Options

### 1. Local Development Deployment

**Best for**: Development, testing, and small-scale usage

#### Prerequisites
- Docker & Docker Compose
- 4GB RAM minimum, 8GB recommended
- 20GB free disk space
- Python 3.8+

#### Quick Setup
```bash
# Clone repository
git clone https://github.com/your-username/robo-researcher-2000.git
cd robo-researcher-2000

# Start services
cd infrastructure
chmod +x setup.sh
./setup.sh

# Verify deployment
python ../tests/deployment_validator.py
```

#### Services
- **n8n**: http://localhost:5678
- **MinIO Console**: http://localhost:9001
- **Wiki.js**: http://localhost:3000
- **Client App**: http://localhost:8080

### 2. Production Self-Hosted Deployment

**Best for**: Organizations with on-premise requirements

#### Server Requirements
- **CPU**: 4+ cores
- **RAM**: 16GB minimum, 32GB recommended
- **Storage**: 100GB+ SSD
- **OS**: Ubuntu 20.04+ or CentOS 8+
- **Network**: Static IP, domain name

#### Production Setup

1. **Prepare Server**
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

2. **Configure Environment**
```bash
# Create production environment file
cat > .env.production << EOF
# Domain Configuration
DOMAIN=your-domain.com
SUBDOMAIN_N8N=n8n.your-domain.com
SUBDOMAIN_MINIO=minio.your-domain.com
SUBDOMAIN_WIKIJS=wiki.your-domain.com

# SSL Configuration
SSL_EMAIL=<EMAIL>
ENABLE_SSL=true

# Database Configuration
POSTGRES_PASSWORD=your_secure_password
POSTGRES_DB=robo_researcher_prod

# MinIO Configuration
MINIO_ROOT_USER=admin
MINIO_ROOT_PASSWORD=your_secure_minio_password

# n8n Configuration
N8N_BASIC_AUTH_ACTIVE=true
N8N_BASIC_AUTH_USER=admin
N8N_BASIC_AUTH_PASSWORD=your_secure_n8n_password

# API Keys
OPENROUTER_API_KEY=your_openrouter_key
SMTP_HOST=smtp.your-provider.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_smtp_password

# Security
JWT_SECRET=$(openssl rand -base64 32)
ENCRYPTION_KEY=$(openssl rand -base64 32)
EOF
```

3. **Deploy with SSL**
```bash
# Use production compose file
docker-compose -f docker-compose.prod.yml --env-file .env.production up -d

# Setup SSL certificates (Let's Encrypt)
docker-compose -f docker-compose.prod.yml exec nginx certbot --nginx -d your-domain.com -d n8n.your-domain.com -d minio.your-domain.com -d wiki.your-domain.com
```

4. **Configure Firewall**
```bash
# UFW configuration
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable
```

### 3. Cloud Deployment (AWS)

**Best for**: Scalable production deployments

#### AWS Architecture
```
Internet Gateway
    │
Application Load Balancer (ALB)
    │
┌───────────────┬───────────────┬───────────────┐
│   ECS Service │   ECS Service │   ECS Service │
│     (n8n)     │   (MinIO)     │   (Wiki.js)   │
└───────────────┴───────────────┴───────────────┘
    │               │               │
┌───────────────┬───────────────┬───────────────┐
│   RDS         │      S3       │   CloudWatch  │
│ (PostgreSQL)  │   (Storage)   │  (Monitoring) │
└───────────────┴───────────────┴───────────────┘
```

#### AWS Deployment Steps

1. **Create Infrastructure**
```bash
# Use AWS CDK or Terraform
cd infrastructure/aws
terraform init
terraform plan -var="domain=your-domain.com"
terraform apply
```

2. **Deploy Services**
```bash
# Build and push Docker images
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin 123456789012.dkr.ecr.us-east-1.amazonaws.com

# Build images
docker build -t robo-researcher-n8n ./infrastructure/n8n/
docker build -t robo-researcher-client ./client/

# Tag and push
docker tag robo-researcher-n8n:latest 123456789012.dkr.ecr.us-east-1.amazonaws.com/robo-researcher-n8n:latest
docker push 123456789012.dkr.ecr.us-east-1.amazonaws.com/robo-researcher-n8n:latest
```

3. **Configure ECS Services**
```json
{
  "family": "robo-researcher-n8n",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "1024",
  "memory": "2048",
  "containerDefinitions": [
    {
      "name": "n8n",
      "image": "123456789012.dkr.ecr.us-east-1.amazonaws.com/robo-researcher-n8n:latest",
      "portMappings": [
        {
          "containerPort": 5678,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "DB_TYPE",
          "value": "postgresdb"
        },
        {
          "name": "DB_POSTGRESDB_HOST",
          "value": "your-rds-endpoint.amazonaws.com"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/robo-researcher",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "n8n"
        }
      }
    }
  ]
}
```

### 4. Cloud Deployment (Google Cloud)

**Best for**: Organizations using Google Cloud Platform

#### GCP Architecture
```
Cloud Load Balancer
    │
Cloud Run Services
    │
┌───────────────┬───────────────┬───────────────┐
│   Cloud Run   │   Cloud Run   │   Cloud Run   │
│     (n8n)     │   (MinIO)     │   (Wiki.js)   │
└───────────────┴───────────────┴───────────────┘
    │               │               │
┌───────────────┬───────────────┬───────────────┐
│  Cloud SQL    │ Cloud Storage │ Cloud Logging │
│ (PostgreSQL)  │   (Buckets)   │ (Monitoring)  │
└───────────────┴───────────────┴───────────────┘
```

#### GCP Deployment
```bash
# Set up project
gcloud config set project your-project-id

# Deploy to Cloud Run
gcloud run deploy robo-researcher-n8n \
  --image gcr.io/your-project-id/robo-researcher-n8n \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --set-env-vars="DB_TYPE=postgresdb,DB_POSTGRESDB_HOST=your-cloud-sql-ip"
```

### 5. Kubernetes Deployment

**Best for**: Large-scale, multi-tenant deployments

#### Kubernetes Manifests

```yaml
# namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: robo-researcher

---
# n8n-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: n8n
  namespace: robo-researcher
spec:
  replicas: 2
  selector:
    matchLabels:
      app: n8n
  template:
    metadata:
      labels:
        app: n8n
    spec:
      containers:
      - name: n8n
        image: robo-researcher/n8n:latest
        ports:
        - containerPort: 5678
        env:
        - name: DB_TYPE
          value: "postgresdb"
        - name: DB_POSTGRESDB_HOST
          valueFrom:
            secretKeyRef:
              name: db-credentials
              key: host
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"

---
# n8n-service.yaml
apiVersion: v1
kind: Service
metadata:
  name: n8n-service
  namespace: robo-researcher
spec:
  selector:
    app: n8n
  ports:
  - protocol: TCP
    port: 80
    targetPort: 5678
  type: LoadBalancer
```

#### Deploy to Kubernetes
```bash
# Apply manifests
kubectl apply -f infrastructure/kubernetes/

# Check deployment
kubectl get pods -n robo-researcher
kubectl get services -n robo-researcher
```

## Configuration Management

### Environment Variables

#### Required Variables
```bash
# API Configuration
OPENROUTER_API_KEY=sk-or-...
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=app_password

# Database
POSTGRES_PASSWORD=secure_password
POSTGRES_DB=robo_researcher

# MinIO
MINIO_ROOT_USER=admin
MINIO_ROOT_PASSWORD=secure_password
```

#### Optional Variables
```bash
# Customization
COMPANY_NAME="Your Company"
COMPANY_LOGO_URL="https://your-domain.com/logo.png"
DEFAULT_LANGUAGE=en
TIMEZONE=UTC

# Performance
MAX_CONCURRENT_WORKFLOWS=5
WORKFLOW_TIMEOUT=1800
MAX_FILE_SIZE=10485760

# Security
ENABLE_2FA=true
SESSION_TIMEOUT=3600
ALLOWED_DOMAINS=your-domain.com,partner-domain.com
```

### Secrets Management

#### Using Docker Secrets
```bash
# Create secrets
echo "your_openrouter_key" | docker secret create openrouter_api_key -
echo "your_smtp_password" | docker secret create smtp_password -

# Reference in compose file
services:
  n8n:
    secrets:
      - openrouter_api_key
      - smtp_password
```

#### Using Kubernetes Secrets
```bash
# Create secret
kubectl create secret generic api-keys \
  --from-literal=openrouter-key=your_key \
  --from-literal=smtp-password=your_password \
  -n robo-researcher
```

## Monitoring and Maintenance

### Health Checks
```bash
# Automated health monitoring
curl -f http://localhost:5678/healthz || exit 1
curl -f http://localhost:9000/minio/health/live || exit 1
curl -f http://localhost:3000/healthz || exit 1
```

### Backup Procedures
```bash
#!/bin/bash
# backup.sh - Daily backup script

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/$DATE"

# Create backup directory
mkdir -p $BACKUP_DIR

# Backup PostgreSQL
docker exec robo-researcher-postgres pg_dump -U postgres robo_researcher > $BACKUP_DIR/postgres.sql

# Backup MinIO data
docker exec robo-researcher-minio mc mirror /data $BACKUP_DIR/minio/

# Backup n8n workflows
docker exec robo-researcher-n8n n8n export:workflow --all --output=$BACKUP_DIR/workflows.json

# Compress backup
tar -czf $BACKUP_DIR.tar.gz $BACKUP_DIR
rm -rf $BACKUP_DIR

# Upload to cloud storage (optional)
aws s3 cp $BACKUP_DIR.tar.gz s3://your-backup-bucket/robo-researcher/
```

### Log Management
```bash
# Configure log rotation
cat > /etc/logrotate.d/robo-researcher << EOF
/var/lib/docker/containers/*/*-json.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    create 0644 root root
}
EOF
```

### Performance Monitoring
```bash
# Monitor resource usage
docker stats --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"

# Monitor workflow execution times
curl -s http://localhost:5678/api/v1/executions | jq '.data[] | {id: .id, duration: .stoppedAt - .startedAt}'
```

## Security Considerations

### Network Security
- Use HTTPS/TLS for all external communications
- Implement proper firewall rules
- Use VPN for administrative access
- Regular security updates

### Data Protection
- Encrypt data at rest and in transit
- Implement proper access controls
- Regular security audits
- GDPR/CCPA compliance measures

### API Security
- Rate limiting on webhooks
- API key rotation
- Input validation and sanitization
- Audit logging

This deployment guide ensures ROBO-RESEARCHER-2000 can be deployed securely and efficiently across various environments.

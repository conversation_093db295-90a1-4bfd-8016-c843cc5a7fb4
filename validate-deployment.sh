#!/bin/bash

# ROBO-RESEARCHER-2000 Deployment Validation Script
# This script validates that all services are running correctly

echo "🔍 ROBO-RESEARCHER-2000 Deployment Validation"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to check service health
check_service() {
    local service_name=$1
    local url=$2
    local expected_code=${3:-200}
    
    echo -n "🔍 Checking $service_name... "
    
    response=$(curl -s -o /dev/null -w "%{http_code}" "$url" 2>/dev/null)
    
    if [ "$response" = "$expected_code" ]; then
        echo -e "${GREEN}✅ OK${NC} (HTTP $response)"
        return 0
    else
        echo -e "${RED}❌ FAILED${NC} (HTTP $response)"
        return 1
    fi
}

# Function to check container status
check_container() {
    local container_name=$1
    
    echo -n "🐳 Checking container $container_name... "
    
    status=$(docker inspect --format='{{.State.Health.Status}}' "$container_name" 2>/dev/null)
    
    if [ "$status" = "healthy" ]; then
        echo -e "${GREEN}✅ HEALTHY${NC}"
        return 0
    elif [ "$status" = "starting" ]; then
        echo -e "${YELLOW}⏳ STARTING${NC}"
        return 1
    else
        echo -e "${RED}❌ UNHEALTHY${NC}"
        return 1
    fi
}

echo ""
echo "📋 Container Health Check"
echo "------------------------"

containers=(
    "robo-researcher-postgres"
    "robo-researcher-redis"
    "robo-researcher-minio"
    "robo-researcher-n8n"
    "robo-researcher-wikijs"
    "robo-researcher-client"
)

container_failures=0
for container in "${containers[@]}"; do
    if ! check_container "$container"; then
        ((container_failures++))
    fi
done

echo ""
echo "🌐 Service Connectivity Check"
echo "-----------------------------"

service_failures=0

# Check Client Application
if ! check_service "Client Application" "http://localhost:8080/health"; then
    ((service_failures++))
fi

# Check n8n
if ! check_service "n8n Workflow Engine" "http://localhost:5678/healthz"; then
    ((service_failures++))
fi

# Check MinIO
if ! check_service "MinIO Storage" "http://localhost:9002/minio/health/live"; then
    ((service_failures++))
fi

# Check MinIO Console
if ! check_service "MinIO Console" "http://localhost:9003/minio/health/live"; then
    ((service_failures++))
fi

# Check Wiki.js (might take longer to start)
if ! check_service "Wiki.js Documentation" "http://localhost:3002" "200"; then
    echo "   ℹ️  Wiki.js might still be starting up..."
fi

# Check Redis
echo -n "🔍 Checking Redis... "
if redis-cli -h localhost -p 6380 ping > /dev/null 2>&1; then
    echo -e "${GREEN}✅ OK${NC}"
else
    echo -e "${RED}❌ FAILED${NC}"
    ((service_failures++))
fi

# Check PostgreSQL
echo -n "🔍 Checking PostgreSQL... "
if PGPASSWORD=n8n_password psql -h localhost -p 5433 -U n8n -d n8n -c "SELECT 1;" > /dev/null 2>&1; then
    echo -e "${GREEN}✅ OK${NC}"
else
    echo -e "${RED}❌ FAILED${NC}"
    ((service_failures++))
fi

echo ""
echo "📊 Port Usage Check"
echo "------------------"

ports=(
    "5678:n8n"
    "8080:Client"
    "9002:MinIO API"
    "9003:MinIO Console"
    "3002:Wiki.js"
    "6380:Redis"
    "5433:PostgreSQL"
)

for port_info in "${ports[@]}"; do
    port=$(echo "$port_info" | cut -d: -f1)
    service=$(echo "$port_info" | cut -d: -f2)
    
    echo -n "🔍 Port $port ($service)... "
    
    if netstat -tuln 2>/dev/null | grep ":$port " > /dev/null; then
        echo -e "${GREEN}✅ LISTENING${NC}"
    else
        echo -e "${RED}❌ NOT LISTENING${NC}"
    fi
done

echo ""
echo "📁 Docker Resources Check"
echo "-------------------------"

# Check volumes
echo "📦 Docker Volumes:"
docker volume ls | grep robo-researcher

# Check network
echo ""
echo "🌐 Docker Network:"
docker network ls | grep robo-researcher

echo ""
echo "📈 Summary"
echo "----------"

total_containers=${#containers[@]}
total_services=$((service_failures + 6)) # Adjust based on services checked

if [ $container_failures -eq 0 ] && [ $service_failures -eq 0 ]; then
    echo -e "${GREEN}🎉 ALL SYSTEMS OPERATIONAL!${NC}"
    echo ""
    echo "🌐 Access URLs:"
    echo "   Client Application: http://localhost:8080"
    echo "   n8n Interface: http://localhost:5678 (admin/robo-researcher-2000)"
    echo "   MinIO Console: http://localhost:9003 (minioadmin/minioadmin)"
    echo "   Wiki.js: http://localhost:3002"
    echo ""
    echo "📋 Next Steps:"
    echo "   1. Configure n8n workflows (see setup-n8n.md)"
    echo "   2. Set up MinIO bucket (run ./setup-minio.sh)"
    echo "   3. Add your OpenRouter API key to .env file"
    echo "   4. Test the complete workflow"
    exit 0
else
    echo -e "${RED}⚠️  ISSUES DETECTED${NC}"
    echo "   Container failures: $container_failures/$total_containers"
    echo "   Service failures: $service_failures"
    echo ""
    echo "🔧 Troubleshooting:"
    echo "   1. Check container logs: docker logs <container-name>"
    echo "   2. Restart services: docker compose restart"
    echo "   3. Check port conflicts: netstat -tuln"
    exit 1
fi

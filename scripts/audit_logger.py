#!/usr/bin/env python3
"""
Comprehensive Audit Logging System for ROBO-RESEARCHER-2000
Tracks all user actions, system events, data access, and security events
"""

import os
import json
import logging
import sqlite3
import threading
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
import hashlib
import gzip
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EventType(Enum):
    """Types of audit events"""
    USER_LOGIN = "user_login"
    USER_LOGOUT = "user_logout"
    USER_REGISTRATION = "user_registration"
    FILE_UPLOAD = "file_upload"
    FILE_DOWNLOAD = "file_download"
    FILE_DELETE = "file_delete"
    WORKFLOW_START = "workflow_start"
    WORKFLOW_COMPLETE = "workflow_complete"
    WORKFLOW_ERROR = "workflow_error"
    DATA_ACCESS = "data_access"
    DATA_MODIFICATION = "data_modification"
    DATA_DELETION = "data_deletion"
    SECURITY_VIOLATION = "security_violation"
    SYSTEM_ERROR = "system_error"
    API_CALL = "api_call"
    CONFIGURATION_CHANGE = "configuration_change"
    BACKUP_OPERATION = "backup_operation"

class EventSeverity(Enum):
    """Severity levels for audit events"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class AuditEvent:
    """Audit event data structure"""
    event_id: str
    timestamp: datetime
    event_type: EventType
    severity: EventSeverity
    user_id: Optional[str]
    session_id: Optional[str]
    ip_address: Optional[str]
    user_agent: Optional[str]
    resource: Optional[str]
    action: str
    details: Dict[str, Any]
    success: bool
    error_message: Optional[str] = None

class AuditDatabase:
    """Database for audit logs"""
    
    def __init__(self, db_path: str = "data/audit.db"):
        self.db_path = db_path
        self.lock = threading.Lock()
        self._init_database()
    
    def _init_database(self):
        """Initialize audit database"""
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS audit_events (
                    event_id TEXT PRIMARY KEY,
                    timestamp TEXT NOT NULL,
                    event_type TEXT NOT NULL,
                    severity TEXT NOT NULL,
                    user_id TEXT,
                    session_id TEXT,
                    ip_address TEXT,
                    user_agent TEXT,
                    resource TEXT,
                    action TEXT NOT NULL,
                    details TEXT,
                    success INTEGER NOT NULL,
                    error_message TEXT
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS audit_summaries (
                    date TEXT PRIMARY KEY,
                    total_events INTEGER,
                    events_by_type TEXT,
                    events_by_severity TEXT,
                    unique_users INTEGER,
                    security_violations INTEGER
                )
            """)
            
            # Create indexes for performance
            conn.execute("CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_events(timestamp)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_events(user_id, timestamp)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_audit_type ON audit_events(event_type, timestamp)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_audit_severity ON audit_events(severity, timestamp)")
    
    def log_event(self, event: AuditEvent) -> bool:
        """Log an audit event"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT INTO audit_events 
                    (event_id, timestamp, event_type, severity, user_id, session_id,
                     ip_address, user_agent, resource, action, details, success, error_message)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    event.event_id,
                    event.timestamp.isoformat(),
                    event.event_type.value,
                    event.severity.value,
                    event.user_id,
                    event.session_id,
                    event.ip_address,
                    event.user_agent,
                    event.resource,
                    event.action,
                    json.dumps(event.details),
                    event.success,
                    event.error_message
                ))
                return True
        except Exception as e:
            logger.error(f"Failed to log audit event: {e}")
            return False
    
    def get_events(self, start_date: datetime = None, end_date: datetime = None,
                   event_type: EventType = None, user_id: str = None,
                   severity: EventSeverity = None, limit: int = 1000) -> List[Dict[str, Any]]:
        """Retrieve audit events with filters"""
        
        query = "SELECT * FROM audit_events WHERE 1=1"
        params = []
        
        if start_date:
            query += " AND timestamp >= ?"
            params.append(start_date.isoformat())
        
        if end_date:
            query += " AND timestamp <= ?"
            params.append(end_date.isoformat())
        
        if event_type:
            query += " AND event_type = ?"
            params.append(event_type.value)
        
        if user_id:
            query += " AND user_id = ?"
            params.append(user_id)
        
        if severity:
            query += " AND severity = ?"
            params.append(severity.value)
        
        query += " ORDER BY timestamp DESC LIMIT ?"
        params.append(limit)
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute(query, params)
            events = []
            
            for row in cursor.fetchall():
                events.append({
                    'event_id': row[0],
                    'timestamp': row[1],
                    'event_type': row[2],
                    'severity': row[3],
                    'user_id': row[4],
                    'session_id': row[5],
                    'ip_address': row[6],
                    'user_agent': row[7],
                    'resource': row[8],
                    'action': row[9],
                    'details': json.loads(row[10]) if row[10] else {},
                    'success': bool(row[11]),
                    'error_message': row[12]
                })
            
            return events
    
    def get_daily_summary(self, date: datetime) -> Dict[str, Any]:
        """Get daily audit summary"""
        date_str = date.strftime('%Y-%m-%d')
        
        with sqlite3.connect(self.db_path) as conn:
            # Check if summary exists
            cursor = conn.execute(
                "SELECT * FROM audit_summaries WHERE date = ?",
                (date_str,)
            )
            row = cursor.fetchone()
            
            if row:
                return {
                    'date': row[0],
                    'total_events': row[1],
                    'events_by_type': json.loads(row[2]),
                    'events_by_severity': json.loads(row[3]),
                    'unique_users': row[4],
                    'security_violations': row[5]
                }
            else:
                # Generate summary
                return self._generate_daily_summary(date)
    
    def _generate_daily_summary(self, date: datetime) -> Dict[str, Any]:
        """Generate daily summary for a specific date"""
        date_str = date.strftime('%Y-%m-%d')
        start_time = date.replace(hour=0, minute=0, second=0, microsecond=0)
        end_time = start_time + timedelta(days=1)
        
        with sqlite3.connect(self.db_path) as conn:
            # Total events
            cursor = conn.execute("""
                SELECT COUNT(*) FROM audit_events 
                WHERE timestamp >= ? AND timestamp < ?
            """, (start_time.isoformat(), end_time.isoformat()))
            total_events = cursor.fetchone()[0]
            
            # Events by type
            cursor = conn.execute("""
                SELECT event_type, COUNT(*) FROM audit_events 
                WHERE timestamp >= ? AND timestamp < ?
                GROUP BY event_type
            """, (start_time.isoformat(), end_time.isoformat()))
            events_by_type = dict(cursor.fetchall())
            
            # Events by severity
            cursor = conn.execute("""
                SELECT severity, COUNT(*) FROM audit_events 
                WHERE timestamp >= ? AND timestamp < ?
                GROUP BY severity
            """, (start_time.isoformat(), end_time.isoformat()))
            events_by_severity = dict(cursor.fetchall())
            
            # Unique users
            cursor = conn.execute("""
                SELECT COUNT(DISTINCT user_id) FROM audit_events 
                WHERE timestamp >= ? AND timestamp < ? AND user_id IS NOT NULL
            """, (start_time.isoformat(), end_time.isoformat()))
            unique_users = cursor.fetchone()[0]
            
            # Security violations
            cursor = conn.execute("""
                SELECT COUNT(*) FROM audit_events 
                WHERE timestamp >= ? AND timestamp < ? AND event_type = ?
            """, (start_time.isoformat(), end_time.isoformat(), EventType.SECURITY_VIOLATION.value))
            security_violations = cursor.fetchone()[0]
            
            summary = {
                'date': date_str,
                'total_events': total_events,
                'events_by_type': events_by_type,
                'events_by_severity': events_by_severity,
                'unique_users': unique_users,
                'security_violations': security_violations
            }
            
            # Store summary
            conn.execute("""
                INSERT OR REPLACE INTO audit_summaries 
                (date, total_events, events_by_type, events_by_severity, unique_users, security_violations)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (
                date_str, total_events,
                json.dumps(events_by_type),
                json.dumps(events_by_severity),
                unique_users, security_violations
            ))
            
            return summary

class AuditLogger:
    """Main audit logging system"""
    
    def __init__(self):
        self.db = AuditDatabase()
        self.log_dir = Path("data/audit_logs")
        self.log_dir.mkdir(parents=True, exist_ok=True)
        self.current_context = {}
        
    def set_context(self, user_id: str = None, session_id: str = None,
                   ip_address: str = None, user_agent: str = None):
        """Set context for subsequent audit events"""
        self.current_context = {
            'user_id': user_id,
            'session_id': session_id,
            'ip_address': ip_address,
            'user_agent': user_agent
        }
    
    def log_event(self, event_type: EventType, action: str, 
                  resource: str = None, details: Dict[str, Any] = None,
                  severity: EventSeverity = EventSeverity.MEDIUM,
                  success: bool = True, error_message: str = None,
                  user_id: str = None, session_id: str = None,
                  ip_address: str = None, user_agent: str = None) -> str:
        """Log an audit event"""
        
        # Generate event ID
        event_id = hashlib.sha256(
            f"{datetime.now().isoformat()}{action}{resource or ''}".encode()
        ).hexdigest()[:16]
        
        # Use provided context or current context
        event = AuditEvent(
            event_id=event_id,
            timestamp=datetime.now(),
            event_type=event_type,
            severity=severity,
            user_id=user_id or self.current_context.get('user_id'),
            session_id=session_id or self.current_context.get('session_id'),
            ip_address=ip_address or self.current_context.get('ip_address'),
            user_agent=user_agent or self.current_context.get('user_agent'),
            resource=resource,
            action=action,
            details=details or {},
            success=success,
            error_message=error_message
        )
        
        # Log to database
        if self.db.log_event(event):
            logger.info(f"Audit event logged: {event_type.value} - {action}")
        
        # Log to file for backup
        self._log_to_file(event)
        
        return event_id
    
    def _log_to_file(self, event: AuditEvent):
        """Log event to file for backup"""
        try:
            log_file = self.log_dir / f"audit_{event.timestamp.strftime('%Y%m%d')}.log"
            
            log_entry = {
                'event_id': event.event_id,
                'timestamp': event.timestamp.isoformat(),
                'event_type': event.event_type.value,
                'severity': event.severity.value,
                'user_id': event.user_id,
                'session_id': event.session_id,
                'ip_address': event.ip_address,
                'resource': event.resource,
                'action': event.action,
                'details': event.details,
                'success': event.success,
                'error_message': event.error_message
            }
            
            with open(log_file, 'a') as f:
                f.write(json.dumps(log_entry) + '\n')
                
        except Exception as e:
            logger.error(f"Failed to write audit log to file: {e}")
    
    def log_user_action(self, action: str, resource: str = None, 
                       details: Dict[str, Any] = None, success: bool = True):
        """Log user action"""
        return self.log_event(
            EventType.DATA_ACCESS if 'view' in action.lower() or 'read' in action.lower()
            else EventType.DATA_MODIFICATION if 'update' in action.lower() or 'modify' in action.lower()
            else EventType.DATA_DELETION if 'delete' in action.lower()
            else EventType.USER_LOGIN if 'login' in action.lower()
            else EventType.USER_LOGOUT if 'logout' in action.lower()
            else EventType.API_CALL,
            action=action,
            resource=resource,
            details=details,
            success=success
        )
    
    def log_security_event(self, action: str, details: Dict[str, Any] = None,
                          severity: EventSeverity = EventSeverity.HIGH):
        """Log security-related event"""
        return self.log_event(
            EventType.SECURITY_VIOLATION,
            action=action,
            details=details,
            severity=severity,
            success=False
        )
    
    def log_system_event(self, action: str, details: Dict[str, Any] = None,
                        success: bool = True, error_message: str = None):
        """Log system event"""
        return self.log_event(
            EventType.SYSTEM_ERROR if not success else EventType.API_CALL,
            action=action,
            details=details,
            success=success,
            error_message=error_message,
            severity=EventSeverity.HIGH if not success else EventSeverity.LOW
        )
    
    def get_user_activity(self, user_id: str, days: int = 7) -> List[Dict[str, Any]]:
        """Get user activity for specified period"""
        start_date = datetime.now() - timedelta(days=days)
        return self.db.get_events(
            start_date=start_date,
            user_id=user_id,
            limit=500
        )
    
    def get_security_events(self, days: int = 30) -> List[Dict[str, Any]]:
        """Get security events for specified period"""
        start_date = datetime.now() - timedelta(days=days)
        return self.db.get_events(
            start_date=start_date,
            event_type=EventType.SECURITY_VIOLATION,
            limit=1000
        )
    
    def get_system_health(self, days: int = 1) -> Dict[str, Any]:
        """Get system health metrics"""
        start_date = datetime.now() - timedelta(days=days)
        
        # Get all events for the period
        events = self.db.get_events(start_date=start_date, limit=10000)
        
        # Calculate metrics
        total_events = len(events)
        error_events = len([e for e in events if not e['success']])
        security_events = len([e for e in events if e['event_type'] == EventType.SECURITY_VIOLATION.value])
        unique_users = len(set(e['user_id'] for e in events if e['user_id']))
        
        return {
            'period_days': days,
            'total_events': total_events,
            'error_events': error_events,
            'security_events': security_events,
            'unique_users': unique_users,
            'error_rate': (error_events / total_events * 100) if total_events > 0 else 0,
            'events_per_hour': total_events / (days * 24) if days > 0 else 0
        }
    
    def rotate_logs(self, days_to_keep: int = 90):
        """Rotate and compress old log files"""
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)
        
        for log_file in self.log_dir.glob("audit_*.log"):
            try:
                # Extract date from filename
                date_str = log_file.stem.split('_')[1]
                file_date = datetime.strptime(date_str, '%Y%m%d')
                
                if file_date < cutoff_date:
                    # Compress and archive
                    compressed_file = log_file.with_suffix('.log.gz')
                    with open(log_file, 'rb') as f_in:
                        with gzip.open(compressed_file, 'wb') as f_out:
                            f_out.writelines(f_in)
                    
                    # Remove original
                    log_file.unlink()
                    logger.info(f"Rotated log file: {log_file} -> {compressed_file}")
                    
            except Exception as e:
                logger.error(f"Failed to rotate log file {log_file}: {e}")

# Global instance
audit_logger = AuditLogger()

if __name__ == "__main__":
    # Test audit logging
    def test():
        # Set context
        audit_logger.set_context(
            user_id="test_user",
            session_id="test_session",
            ip_address="127.0.0.1"
        )
        
        # Log various events
        audit_logger.log_user_action("login", details={"method": "password"})
        audit_logger.log_user_action("upload_file", resource="test.txt", 
                                   details={"file_size": 1024})
        audit_logger.log_security_event("failed_login_attempt", 
                                       details={"attempts": 3})
        audit_logger.log_system_event("workflow_execution", 
                                    details={"workflow_id": "test_workflow"})
        
        # Get metrics
        health = audit_logger.get_system_health()
        print(f"System health: {health}")
        
        # Get user activity
        activity = audit_logger.get_user_activity("test_user")
        print(f"User activity: {len(activity)} events")
    
    test()

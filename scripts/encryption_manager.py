#!/usr/bin/env python3
"""
Data Encryption Manager for ROBO-RESEARCHER-2000
Handles encryption/decryption of sensitive data at rest and in transit
"""

import os
import json
import base64
import logging
from typing import Dict, Any, Optional, Union
from cryptography.fernet import Fe<PERSON><PERSON>
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
import sqlite3
from datetime import datetime
import secrets

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EncryptionManager:
    """Manages encryption/decryption operations"""
    
    def __init__(self, key_file: str = "data/encryption.key"):
        self.key_file = key_file
        self.db_path = "data/encryption_keys.db"
        self._ensure_key_directory()
        self._init_database()
        self.master_key = self._load_or_create_master_key()
        self.fernet = Fernet(self.master_key)
    
    def _ensure_key_directory(self):
        """Ensure key directory exists"""
        os.makedirs(os.path.dirname(self.key_file), exist_ok=True)
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
    
    def _init_database(self):
        """Initialize encryption keys database"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS encryption_keys (
                    key_id TEXT PRIMARY KEY,
                    key_type TEXT,
                    encrypted_key BLOB,
                    created_at TEXT,
                    last_used TEXT,
                    is_active INTEGER DEFAULT 1
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS encrypted_data (
                    data_id TEXT PRIMARY KEY,
                    data_type TEXT,
                    key_id TEXT,
                    encrypted_content BLOB,
                    metadata TEXT,
                    created_at TEXT,
                    FOREIGN KEY (key_id) REFERENCES encryption_keys (key_id)
                )
            """)
    
    def _load_or_create_master_key(self) -> bytes:
        """Load existing master key or create new one"""
        if os.path.exists(self.key_file):
            with open(self.key_file, 'rb') as f:
                return f.read()
        else:
            # Generate new master key
            key = Fernet.generate_key()
            with open(self.key_file, 'wb') as f:
                f.write(key)
            
            # Set restrictive permissions
            os.chmod(self.key_file, 0o600)
            logger.info("Generated new master encryption key")
            return key
    
    def encrypt_text(self, text: str, data_type: str = "general") -> Dict[str, Any]:
        """Encrypt text data"""
        if not text:
            return {"success": False, "error": "No text provided"}
        
        try:
            # Encrypt the text
            encrypted_data = self.fernet.encrypt(text.encode('utf-8'))
            
            # Generate unique data ID
            data_id = secrets.token_urlsafe(16)
            
            # Store in database
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT INTO encrypted_data 
                    (data_id, data_type, key_id, encrypted_content, metadata, created_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    data_id, data_type, "master", encrypted_data,
                    json.dumps({"original_length": len(text)}),
                    datetime.now().isoformat()
                ))
            
            logger.info(f"Encrypted {data_type} data: {data_id}")
            return {
                "success": True,
                "data_id": data_id,
                "encrypted_size": len(encrypted_data)
            }
            
        except Exception as e:
            logger.error(f"Encryption failed: {e}")
            return {"success": False, "error": str(e)}
    
    def decrypt_text(self, data_id: str) -> Dict[str, Any]:
        """Decrypt text data"""
        try:
            # Retrieve from database
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT encrypted_content, data_type, metadata 
                    FROM encrypted_data 
                    WHERE data_id = ?
                """, (data_id,))
                
                row = cursor.fetchone()
                if not row:
                    return {"success": False, "error": "Data not found"}
                
                encrypted_content, data_type, metadata = row
            
            # Decrypt the data
            decrypted_data = self.fernet.decrypt(encrypted_content)
            text = decrypted_data.decode('utf-8')
            
            logger.info(f"Decrypted {data_type} data: {data_id}")
            return {
                "success": True,
                "text": text,
                "data_type": data_type,
                "metadata": json.loads(metadata) if metadata else {}
            }
            
        except Exception as e:
            logger.error(f"Decryption failed: {e}")
            return {"success": False, "error": str(e)}
    
    def encrypt_file(self, file_path: str, data_type: str = "file") -> Dict[str, Any]:
        """Encrypt file contents"""
        try:
            if not os.path.exists(file_path):
                return {"success": False, "error": "File not found"}
            
            # Read file content
            with open(file_path, 'rb') as f:
                file_content = f.read()
            
            # Encrypt the content
            encrypted_content = self.fernet.encrypt(file_content)
            
            # Generate unique data ID
            data_id = secrets.token_urlsafe(16)
            
            # Store in database
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT INTO encrypted_data 
                    (data_id, data_type, key_id, encrypted_content, metadata, created_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    data_id, data_type, "master", encrypted_content,
                    json.dumps({
                        "original_filename": os.path.basename(file_path),
                        "original_size": len(file_content)
                    }),
                    datetime.now().isoformat()
                ))
            
            logger.info(f"Encrypted file: {file_path} -> {data_id}")
            return {
                "success": True,
                "data_id": data_id,
                "encrypted_size": len(encrypted_content)
            }
            
        except Exception as e:
            logger.error(f"File encryption failed: {e}")
            return {"success": False, "error": str(e)}
    
    def decrypt_to_file(self, data_id: str, output_path: str) -> Dict[str, Any]:
        """Decrypt data and save to file"""
        try:
            # Retrieve from database
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT encrypted_content, metadata 
                    FROM encrypted_data 
                    WHERE data_id = ?
                """, (data_id,))
                
                row = cursor.fetchone()
                if not row:
                    return {"success": False, "error": "Data not found"}
                
                encrypted_content, metadata = row
            
            # Decrypt the content
            decrypted_content = self.fernet.decrypt(encrypted_content)
            
            # Write to file
            with open(output_path, 'wb') as f:
                f.write(decrypted_content)
            
            logger.info(f"Decrypted to file: {data_id} -> {output_path}")
            return {
                "success": True,
                "output_path": output_path,
                "metadata": json.loads(metadata) if metadata else {}
            }
            
        except Exception as e:
            logger.error(f"File decryption failed: {e}")
            return {"success": False, "error": str(e)}
    
    def encrypt_json(self, data: Dict[str, Any], data_type: str = "json") -> Dict[str, Any]:
        """Encrypt JSON data"""
        try:
            json_string = json.dumps(data, ensure_ascii=False)
            return self.encrypt_text(json_string, data_type)
        except Exception as e:
            logger.error(f"JSON encryption failed: {e}")
            return {"success": False, "error": str(e)}
    
    def decrypt_json(self, data_id: str) -> Dict[str, Any]:
        """Decrypt JSON data"""
        try:
            result = self.decrypt_text(data_id)
            if result["success"]:
                data = json.loads(result["text"])
                return {
                    "success": True,
                    "data": data,
                    "data_type": result["data_type"],
                    "metadata": result["metadata"]
                }
            else:
                return result
        except Exception as e:
            logger.error(f"JSON decryption failed: {e}")
            return {"success": False, "error": str(e)}
    
    def delete_encrypted_data(self, data_id: str) -> bool:
        """Securely delete encrypted data"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    DELETE FROM encrypted_data WHERE data_id = ?
                """, (data_id,))
                
                if cursor.rowcount > 0:
                    logger.info(f"Deleted encrypted data: {data_id}")
                    return True
                else:
                    return False
                    
        except Exception as e:
            logger.error(f"Failed to delete encrypted data: {e}")
            return False
    
    def list_encrypted_data(self, data_type: str = None, limit: int = 100) -> List[Dict[str, Any]]:
        """List encrypted data entries"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                if data_type:
                    cursor = conn.execute("""
                        SELECT data_id, data_type, created_at, metadata
                        FROM encrypted_data 
                        WHERE data_type = ?
                        ORDER BY created_at DESC 
                        LIMIT ?
                    """, (data_type, limit))
                else:
                    cursor = conn.execute("""
                        SELECT data_id, data_type, created_at, metadata
                        FROM encrypted_data 
                        ORDER BY created_at DESC 
                        LIMIT ?
                    """, (limit,))
                
                entries = []
                for row in cursor.fetchall():
                    entries.append({
                        'data_id': row[0],
                        'data_type': row[1],
                        'created_at': row[2],
                        'metadata': json.loads(row[3]) if row[3] else {}
                    })
                
                return entries
                
        except Exception as e:
            logger.error(f"Failed to list encrypted data: {e}")
            return []
    
    def rotate_master_key(self) -> Dict[str, Any]:
        """Rotate the master encryption key"""
        try:
            # Generate new key
            new_key = Fernet.generate_key()
            new_fernet = Fernet(new_key)
            
            # Re-encrypt all data with new key
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("SELECT data_id, encrypted_content FROM encrypted_data")
                
                for row in cursor.fetchall():
                    data_id, encrypted_content = row
                    
                    # Decrypt with old key
                    decrypted_data = self.fernet.decrypt(encrypted_content)
                    
                    # Encrypt with new key
                    new_encrypted_content = new_fernet.encrypt(decrypted_data)
                    
                    # Update database
                    conn.execute("""
                        UPDATE encrypted_data 
                        SET encrypted_content = ? 
                        WHERE data_id = ?
                    """, (new_encrypted_content, data_id))
            
            # Backup old key
            backup_path = f"{self.key_file}.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            os.rename(self.key_file, backup_path)
            
            # Save new key
            with open(self.key_file, 'wb') as f:
                f.write(new_key)
            
            os.chmod(self.key_file, 0o600)
            
            # Update instance
            self.master_key = new_key
            self.fernet = new_fernet
            
            logger.info("Master key rotated successfully")
            return {
                "success": True,
                "backup_path": backup_path,
                "rotated_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Key rotation failed: {e}")
            return {"success": False, "error": str(e)}
    
    def get_encryption_stats(self) -> Dict[str, Any]:
        """Get encryption statistics"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # Total encrypted items
                cursor = conn.execute("SELECT COUNT(*) FROM encrypted_data")
                total_items = cursor.fetchone()[0]
                
                # Items by type
                cursor = conn.execute("""
                    SELECT data_type, COUNT(*) 
                    FROM encrypted_data 
                    GROUP BY data_type
                """)
                items_by_type = dict(cursor.fetchall())
                
                # Total encrypted size
                cursor = conn.execute("SELECT SUM(LENGTH(encrypted_content)) FROM encrypted_data")
                total_size = cursor.fetchone()[0] or 0
                
                return {
                    "total_items": total_items,
                    "items_by_type": items_by_type,
                    "total_encrypted_size": total_size,
                    "master_key_exists": os.path.exists(self.key_file)
                }
                
        except Exception as e:
            logger.error(f"Failed to get encryption stats: {e}")
            return {"error": str(e)}

# Global instance
encryption_manager = EncryptionManager()

if __name__ == "__main__":
    # Test encryption system
    def test():
        # Test text encryption
        test_text = "This is sensitive user data that needs to be encrypted."
        encrypt_result = encryption_manager.encrypt_text(test_text, "test_data")
        print(f"Encryption result: {encrypt_result}")
        
        if encrypt_result["success"]:
            # Test decryption
            decrypt_result = encryption_manager.decrypt_text(encrypt_result["data_id"])
            print(f"Decryption result: {decrypt_result}")
            
            # Test JSON encryption
            test_data = {"user_id": "123", "analysis": "sensitive analysis results"}
            json_encrypt = encryption_manager.encrypt_json(test_data, "analysis_results")
            print(f"JSON encryption: {json_encrypt}")
            
            if json_encrypt["success"]:
                json_decrypt = encryption_manager.decrypt_json(json_encrypt["data_id"])
                print(f"JSON decryption: {json_decrypt}")
        
        # Get stats
        stats = encryption_manager.get_encryption_stats()
        print(f"Encryption stats: {stats}")
    
    test()

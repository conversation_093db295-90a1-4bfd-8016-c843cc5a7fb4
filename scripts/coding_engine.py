"""
ROBO-RESEARCHER-2000 Coding Engine
Handles deductive and open coding of qualitative data using NLTK/spaCy and AI assistance.
"""

import json
import re
import logging
from typing import Dict, List, Tuple, Optional, Set
from dataclasses import dataclass, asdict
from collections import defaultdict, Counter
import nltk
import spacy
from nltk.tokenize import word_tokenize, sent_tokenize
from nltk.corpus import stopwords
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np

@dataclass
class CodedSegment:
    """Represents a coded segment of text"""
    segment_id: str
    text: str
    codes: List[str]
    intensity: float  # 1-5 scale
    emotion: str  # positive, negative, neutral
    context: str
    participant_id: str
    timestamp: Optional[str] = None
    confidence: float = 1.0

@dataclass
class CodingConfig:
    """Configuration for coding process"""
    language: str = 'en'  # Default to English, supports 'en' and 'es'
    min_segment_length: int = 10
    max_codes_per_segment: int = 5
    intensity_threshold: float = 2.0
    enable_sentiment_analysis: bool = True
    enable_co_occurrence: bool = True
    similarity_threshold: float = 0.7

class CodingEngine:
    """Main coding engine for qualitative analysis"""
    
    def __init__(self, config: CodingConfig = None):
        self.config = config or CodingConfig()
        self.logger = logging.getLogger(__name__)
        
        # Load spaCy model
        try:
            if self.config.language == 'es':
                self.nlp = spacy.load('es_core_news_sm')
            else:
                self.nlp = spacy.load('en_core_web_sm')
        except OSError:
            self.logger.warning(f"spaCy model not found for {self.config.language}")
            self.nlp = None
        
        # Load stopwords
        try:
            self.stopwords = set(stopwords.words('spanish' if self.config.language == 'es' else 'english'))
        except LookupError:
            self.stopwords = set()
        
        # Initialize coding structures
        self.deductive_codes = {}
        self.coded_segments = []
        self.code_frequencies = Counter()
        self.co_occurrence_matrix = defaultdict(lambda: defaultdict(int))
        
    def load_deductive_codes(self, codes_config: Dict) -> None:
        """Load predefined deductive codes from configuration"""
        self.deductive_codes = codes_config.get('deductive_codes', {})
        self.logger.info(f"Loaded {len(self.deductive_codes)} deductive code categories")
    
    def apply_deductive_coding(self, segments: List[Dict]) -> List[CodedSegment]:
        """Apply predefined codes to text segments"""
        self.logger.info("Starting deductive coding")
        coded_segments = []
        
        for segment in segments:
            segment_text = segment.get('content', '')
            segment_id = segment.get('segment_id', '')
            participant_id = segment.get('speaker', 'Unknown')
            
            if len(segment_text) < self.config.min_segment_length:
                continue
            
            # Find matching codes
            matched_codes = self._find_matching_codes(segment_text)
            
            if matched_codes:
                # Calculate intensity and emotion
                intensity = self._calculate_intensity(segment_text, matched_codes)
                emotion = self._detect_emotion(segment_text)
                context = self._extract_context(segment_text)
                
                coded_segment = CodedSegment(
                    segment_id=str(segment_id),
                    text=segment_text,
                    codes=matched_codes,
                    intensity=intensity,
                    emotion=emotion,
                    context=context,
                    participant_id=participant_id
                )
                
                coded_segments.append(coded_segment)
                
                # Update statistics
                for code in matched_codes:
                    self.code_frequencies[code] += 1
                
                # Update co-occurrence matrix
                if self.config.enable_co_occurrence:
                    self._update_co_occurrence(matched_codes)
        
        self.coded_segments.extend(coded_segments)
        self.logger.info(f"Deductive coding completed: {len(coded_segments)} segments coded")
        return coded_segments
    
    def _find_matching_codes(self, text: str) -> List[str]:
        """Find codes that match the text content"""
        matched_codes = []
        text_lower = text.lower()
        
        for category, codes in self.deductive_codes.items():
            for code_name, code_info in codes.items():
                keywords = code_info.get('keywords', [])
                
                # Check for keyword matches
                for keyword in keywords:
                    if re.search(r'\b' + re.escape(keyword.lower()) + r'\b', text_lower):
                        if code_name not in matched_codes:
                            matched_codes.append(code_name)
                        break
                
                # Limit codes per segment
                if len(matched_codes) >= self.config.max_codes_per_segment:
                    break
            
            if len(matched_codes) >= self.config.max_codes_per_segment:
                break
        
        return matched_codes
    
    def _calculate_intensity(self, text: str, codes: List[str]) -> float:
        """Calculate emotional/behavioral intensity of the segment"""
        # Base intensity
        intensity = 1.0
        
        # Intensity indicators
        intensity_markers = {
            'high': ['muy', 'extremadamente', 'súper', 'totalmente', 'completamente', 'absolutamente'],
            'medium': ['bastante', 'algo', 'un poco', 'medio'],
            'emphasis': ['!', '¡', 'MAYÚSCULAS', '???', '...']
        }
        
        text_lower = text.lower()
        
        # Check for intensity markers
        for marker in intensity_markers['high']:
            if marker in text_lower:
                intensity += 1.5
        
        for marker in intensity_markers['medium']:
            if marker in text_lower:
                intensity += 0.5
        
        # Check for emphasis (punctuation, caps)
        if '!' in text or '¡' in text:
            intensity += 0.5
        if text.isupper():
            intensity += 1.0
        if '...' in text:
            intensity += 0.3
        
        # Normalize to 1-5 scale
        intensity = min(5.0, max(1.0, intensity))
        return round(intensity, 1)
    
    def _detect_emotion(self, text: str) -> str:
        """Detect emotional valence of the text"""
        if not self.config.enable_sentiment_analysis:
            return 'neutral'
        
        # Simple sentiment analysis based on keywords
        positive_words = [
            'bueno', 'excelente', 'perfecto', 'genial', 'fantástico', 'fácil', 
            'claro', 'intuitivo', 'rápido', 'útil', 'me gusta', 'satisfecho'
        ]
        
        negative_words = [
            'malo', 'terrible', 'horrible', 'difícil', 'confuso', 'lento',
            'frustrado', 'molesto', 'complicado', 'no me gusta', 'odio'
        ]
        
        text_lower = text.lower()
        
        positive_count = sum(1 for word in positive_words if word in text_lower)
        negative_count = sum(1 for word in negative_words if word in text_lower)
        
        if positive_count > negative_count:
            return 'positive'
        elif negative_count > positive_count:
            return 'negative'
        else:
            return 'neutral'
    
    def _extract_context(self, text: str) -> str:
        """Extract contextual information from the text"""
        # Simple context extraction based on common patterns
        context_patterns = {
            'device': r'\b(móvil|celular|computadora|laptop|tablet|desktop)\b',
            'location': r'\b(casa|oficina|trabajo|calle|público)\b',
            'time': r'\b(mañana|tarde|noche|día|hora)\b',
            'task': r'\b(buscar|encontrar|comprar|navegar|usar)\b'
        }
        
        contexts = []
        text_lower = text.lower()
        
        for context_type, pattern in context_patterns.items():
            matches = re.findall(pattern, text_lower)
            if matches:
                contexts.append(f"{context_type}: {', '.join(set(matches))}")
        
        return '; '.join(contexts) if contexts else 'general'
    
    def _update_co_occurrence(self, codes: List[str]) -> None:
        """Update code co-occurrence matrix"""
        for i, code1 in enumerate(codes):
            for j, code2 in enumerate(codes):
                if i != j:
                    self.co_occurrence_matrix[code1][code2] += 1
    
    def suggest_open_codes(self, segments: List[Dict], ai_suggestions: List[Dict] = None) -> List[str]:
        """Suggest open codes based on text analysis and AI input"""
        self.logger.info("Generating open code suggestions")
        
        # Extract key terms using TF-IDF
        texts = [segment.get('content', '') for segment in segments]
        suggested_codes = []
        
        if texts:
            # Use TF-IDF to find important terms
            vectorizer = TfidfVectorizer(
                max_features=50,
                stop_words=list(self.stopwords),
                ngram_range=(1, 2),
                min_df=2
            )
            
            try:
                tfidf_matrix = vectorizer.fit_transform(texts)
                feature_names = vectorizer.get_feature_names_out()
                
                # Get top terms
                scores = tfidf_matrix.sum(axis=0).A1
                top_indices = scores.argsort()[-20:][::-1]
                
                for idx in top_indices:
                    term = feature_names[idx]
                    # Convert to code format
                    code = term.replace(' ', '_').lower()
                    if self._is_valid_code(code):
                        suggested_codes.append(code)
                
            except Exception as e:
                self.logger.warning(f"TF-IDF analysis failed: {e}")
        
        # Add AI suggestions if provided
        if ai_suggestions:
            for suggestion in ai_suggestions:
                code = suggestion.get('code', '').lower().replace(' ', '_')
                if self._is_valid_code(code) and code not in suggested_codes:
                    suggested_codes.append(code)
        
        # Remove duplicates and limit
        suggested_codes = list(dict.fromkeys(suggested_codes))[:15]
        
        self.logger.info(f"Generated {len(suggested_codes)} open code suggestions")
        return suggested_codes
    
    def _is_valid_code(self, code: str) -> bool:
        """Validate if a suggested code is appropriate"""
        # Basic validation rules
        if len(code) < 3 or len(code) > 30:
            return False
        
        # Avoid common stopwords
        if code in self.stopwords:
            return False
        
        # Avoid numbers-only codes
        if code.isdigit():
            return False
        
        # Avoid codes that are too generic
        generic_terms = ['cosa', 'algo', 'esto', 'eso', 'thing', 'stuff', 'something']
        if code in generic_terms:
            return False
        
        return True
    
    def get_coding_statistics(self) -> Dict:
        """Get comprehensive coding statistics"""
        total_segments = len(self.coded_segments)
        
        if total_segments == 0:
            return {'error': 'No coded segments available'}
        
        # Basic statistics
        stats = {
            'total_segments': total_segments,
            'total_codes': len(self.code_frequencies),
            'average_codes_per_segment': round(sum(len(seg.codes) for seg in self.coded_segments) / total_segments, 2),
            'code_frequencies': dict(self.code_frequencies.most_common(10)),
            'emotion_distribution': self._get_emotion_distribution(),
            'intensity_distribution': self._get_intensity_distribution(),
            'participant_distribution': self._get_participant_distribution()
        }
        
        # Co-occurrence analysis
        if self.config.enable_co_occurrence:
            stats['top_co_occurrences'] = self._get_top_co_occurrences()
        
        return stats
    
    def _get_emotion_distribution(self) -> Dict:
        """Get distribution of emotions across segments"""
        emotions = [seg.emotion for seg in self.coded_segments]
        emotion_counts = Counter(emotions)
        total = len(emotions)
        
        return {
            emotion: {
                'count': count,
                'percentage': round((count / total) * 100, 1)
            }
            for emotion, count in emotion_counts.items()
        }
    
    def _get_intensity_distribution(self) -> Dict:
        """Get distribution of intensity levels"""
        intensities = [seg.intensity for seg in self.coded_segments]
        
        return {
            'average': round(np.mean(intensities), 2),
            'median': round(np.median(intensities), 2),
            'min': min(intensities),
            'max': max(intensities),
            'distribution': {
                'low (1-2)': len([i for i in intensities if 1 <= i < 2]),
                'medium (2-3)': len([i for i in intensities if 2 <= i < 3]),
                'high (3-4)': len([i for i in intensities if 3 <= i < 4]),
                'very_high (4-5)': len([i for i in intensities if 4 <= i <= 5])
            }
        }
    
    def _get_participant_distribution(self) -> Dict:
        """Get distribution of codes by participant"""
        participant_codes = defaultdict(list)
        
        for segment in self.coded_segments:
            participant_codes[segment.participant_id].extend(segment.codes)
        
        return {
            participant: {
                'total_codes': len(codes),
                'unique_codes': len(set(codes)),
                'top_codes': dict(Counter(codes).most_common(5))
            }
            for participant, codes in participant_codes.items()
        }
    
    def _get_top_co_occurrences(self, top_n: int = 10) -> List[Dict]:
        """Get top code co-occurrences"""
        co_occurrences = []
        
        for code1, code2_dict in self.co_occurrence_matrix.items():
            for code2, count in code2_dict.items():
                if count > 1:  # Only include meaningful co-occurrences
                    co_occurrences.append({
                        'code1': code1,
                        'code2': code2,
                        'count': count,
                        'strength': round(count / max(self.code_frequencies[code1], self.code_frequencies[code2]), 3)
                    })
        
        # Sort by count and return top N
        co_occurrences.sort(key=lambda x: x['count'], reverse=True)
        return co_occurrences[:top_n]
    
    def export_coded_data(self) -> Dict:
        """Export all coded data for further analysis"""
        return {
            'coded_segments': [asdict(segment) for segment in self.coded_segments],
            'statistics': self.get_coding_statistics(),
            'configuration': asdict(self.config),
            'deductive_codes_used': self.deductive_codes
        }

def code_segments_for_n8n(segments: List[Dict], codes_config: Dict, ai_suggestions: List[Dict] = None) -> Dict:
    """
    Wrapper function for n8n integration
    
    Args:
        segments: List of text segments to code
        codes_config: Deductive codes configuration
        ai_suggestions: Optional AI-generated code suggestions
        
    Returns:
        Coded segments and analysis results
    """
    config = CodingConfig()
    engine = CodingEngine(config)
    
    # Load deductive codes
    engine.load_deductive_codes(codes_config)
    
    # Apply deductive coding
    coded_segments = engine.apply_deductive_coding(segments)
    
    # Generate open code suggestions
    open_codes = engine.suggest_open_codes(segments, ai_suggestions)
    
    # Get statistics
    statistics = engine.get_coding_statistics()
    
    return {
        'coded_segments': [asdict(segment) for segment in coded_segments],
        'open_code_suggestions': open_codes,
        'statistics': statistics,
        'success': True
    }

if __name__ == "__main__":
    # Example usage
    sample_segments = [
        {
            'segment_id': 1,
            'speaker': 'P001',
            'content': 'Me frustré mucho cuando intenté buscar el producto y no aparecía nada. La búsqueda es muy confusa.'
        },
        {
            'segment_id': 2,
            'speaker': 'P001', 
            'content': 'La aplicación es bastante fácil de usar en general, me gusta el diseño.'
        }
    ]
    
    # Load sample codes
    with open('../workflows/config/default-codes.json', 'r') as f:
        codes_config = json.load(f)
    
    result = code_segments_for_n8n(sample_segments, codes_config)
    print(json.dumps(result, indent=2, ensure_ascii=False))

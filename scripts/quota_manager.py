#!/usr/bin/env python3
"""
Quota and Rate Limiting Manager for ROBO-RESEARCHER-2000
Manages user quotas, project limits, and intelligent request queuing
"""

import os
import json
import time
import asyncio
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from collections import deque
import sqlite3
import threading

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class UserQuota:
    """User quota configuration"""
    user_id: str
    daily_requests: int = 50
    daily_tokens: int = 100000
    monthly_cost_limit: float = 10.0
    priority: int = 1  # 1=normal, 2=premium, 3=enterprise
    
@dataclass
class ProjectQuota:
    """Project-specific quota configuration"""
    project_id: str
    max_requests: int = 10
    max_tokens: int = 50000
    max_cost: float = 5.0
    
@dataclass
class UsageRecord:
    """Usage tracking record"""
    timestamp: datetime
    user_id: str
    project_id: str
    requests: int
    tokens: int
    cost: float
    provider: str

class QuotaDatabase:
    """SQLite database for quota management"""
    
    def __init__(self, db_path: str = "data/quota.db"):
        self.db_path = db_path
        self.lock = threading.Lock()
        self._init_database()
    
    def _init_database(self):
        """Initialize database tables"""
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS user_quotas (
                    user_id TEXT PRIMARY KEY,
                    daily_requests INTEGER,
                    daily_tokens INTEGER,
                    monthly_cost_limit REAL,
                    priority INTEGER
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS project_quotas (
                    project_id TEXT PRIMARY KEY,
                    max_requests INTEGER,
                    max_tokens INTEGER,
                    max_cost REAL
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS usage_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT,
                    user_id TEXT,
                    project_id TEXT,
                    requests INTEGER,
                    tokens INTEGER,
                    cost REAL,
                    provider TEXT
                )
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_usage_timestamp 
                ON usage_records(timestamp)
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_usage_user 
                ON usage_records(user_id, timestamp)
            """)
    
    def get_user_quota(self, user_id: str) -> UserQuota:
        """Get user quota configuration"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute(
                "SELECT * FROM user_quotas WHERE user_id = ?", 
                (user_id,)
            )
            row = cursor.fetchone()
            
            if row:
                return UserQuota(
                    user_id=row[0],
                    daily_requests=row[1],
                    daily_tokens=row[2],
                    monthly_cost_limit=row[3],
                    priority=row[4]
                )
            else:
                # Create default quota
                default_quota = UserQuota(user_id=user_id)
                self.set_user_quota(default_quota)
                return default_quota
    
    def set_user_quota(self, quota: UserQuota):
        """Set user quota configuration"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT OR REPLACE INTO user_quotas 
                (user_id, daily_requests, daily_tokens, monthly_cost_limit, priority)
                VALUES (?, ?, ?, ?, ?)
            """, (quota.user_id, quota.daily_requests, quota.daily_tokens, 
                  quota.monthly_cost_limit, quota.priority))
    
    def get_project_quota(self, project_id: str) -> ProjectQuota:
        """Get project quota configuration"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute(
                "SELECT * FROM project_quotas WHERE project_id = ?", 
                (project_id,)
            )
            row = cursor.fetchone()
            
            if row:
                return ProjectQuota(
                    project_id=row[0],
                    max_requests=row[1],
                    max_tokens=row[2],
                    max_cost=row[3]
                )
            else:
                # Create default quota
                default_quota = ProjectQuota(project_id=project_id)
                self.set_project_quota(default_quota)
                return default_quota
    
    def set_project_quota(self, quota: ProjectQuota):
        """Set project quota configuration"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT OR REPLACE INTO project_quotas 
                (project_id, max_requests, max_tokens, max_cost)
                VALUES (?, ?, ?, ?)
            """, (quota.project_id, quota.max_requests, quota.max_tokens, quota.max_cost))
    
    def record_usage(self, record: UsageRecord):
        """Record usage"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT INTO usage_records 
                (timestamp, user_id, project_id, requests, tokens, cost, provider)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (record.timestamp.isoformat(), record.user_id, record.project_id,
                  record.requests, record.tokens, record.cost, record.provider))
    
    def get_usage_stats(self, user_id: str, days: int = 1) -> Dict[str, Any]:
        """Get usage statistics for user"""
        since = datetime.now() - timedelta(days=days)
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                SELECT 
                    SUM(requests) as total_requests,
                    SUM(tokens) as total_tokens,
                    SUM(cost) as total_cost,
                    COUNT(*) as total_calls
                FROM usage_records 
                WHERE user_id = ? AND timestamp >= ?
            """, (user_id, since.isoformat()))
            
            row = cursor.fetchone()
            return {
                'total_requests': row[0] or 0,
                'total_tokens': row[1] or 0,
                'total_cost': row[2] or 0.0,
                'total_calls': row[3] or 0
            }

class RequestQueue:
    """Intelligent request queuing system"""
    
    def __init__(self):
        self.queues = {
            1: deque(),  # Normal priority
            2: deque(),  # Premium priority
            3: deque()   # Enterprise priority
        }
        self.processing = False
        self.lock = asyncio.Lock()
    
    async def add_request(self, request_data: Dict[str, Any], priority: int = 1):
        """Add request to queue"""
        async with self.lock:
            self.queues[priority].append({
                'data': request_data,
                'timestamp': datetime.now(),
                'future': asyncio.Future()
            })
            
            if not self.processing:
                asyncio.create_task(self._process_queue())
        
        # Return the future for the request
        return self.queues[priority][-1]['future']
    
    async def _process_queue(self):
        """Process requests from queue in priority order"""
        self.processing = True
        
        try:
            while True:
                request = None
                
                async with self.lock:
                    # Check queues in priority order (3=highest, 1=lowest)
                    for priority in [3, 2, 1]:
                        if self.queues[priority]:
                            request = self.queues[priority].popleft()
                            break
                
                if not request:
                    break
                
                try:
                    # Process the request (this would call the AI provider)
                    result = await self._process_request(request['data'])
                    request['future'].set_result(result)
                except Exception as e:
                    request['future'].set_exception(e)
                
                # Small delay to prevent overwhelming APIs
                await asyncio.sleep(0.1)
                
        finally:
            self.processing = False
    
    async def _process_request(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process individual request"""
        # This would integrate with the AI provider manager
        from .ai_provider_manager import ai_manager
        
        return await ai_manager.make_request(
            prompt=request_data['prompt'],
            model_preference=request_data.get('model'),
            max_tokens=request_data.get('max_tokens', 1000),
            temperature=request_data.get('temperature', 0.3)
        )

class QuotaManager:
    """Main quota management system"""
    
    def __init__(self):
        self.db = QuotaDatabase()
        self.queue = RequestQueue()
        self.active_requests = {}  # Track active requests per user
    
    async def check_quota(self, user_id: str, project_id: str, 
                         estimated_tokens: int = 1000) -> Dict[str, Any]:
        """Check if request is within quota limits"""
        
        user_quota = self.db.get_user_quota(user_id)
        project_quota = self.db.get_project_quota(project_id)
        
        # Get current usage
        daily_usage = self.db.get_usage_stats(user_id, days=1)
        monthly_usage = self.db.get_usage_stats(user_id, days=30)
        project_usage = self.db.get_usage_stats(f"project_{project_id}", days=1)
        
        # Check limits
        checks = {
            'user_daily_requests': {
                'current': daily_usage['total_requests'],
                'limit': user_quota.daily_requests,
                'allowed': daily_usage['total_requests'] < user_quota.daily_requests
            },
            'user_daily_tokens': {
                'current': daily_usage['total_tokens'],
                'limit': user_quota.daily_tokens,
                'allowed': daily_usage['total_tokens'] + estimated_tokens <= user_quota.daily_tokens
            },
            'user_monthly_cost': {
                'current': monthly_usage['total_cost'],
                'limit': user_quota.monthly_cost_limit,
                'allowed': monthly_usage['total_cost'] < user_quota.monthly_cost_limit
            },
            'project_requests': {
                'current': project_usage['total_requests'],
                'limit': project_quota.max_requests,
                'allowed': project_usage['total_requests'] < project_quota.max_requests
            },
            'project_tokens': {
                'current': project_usage['total_tokens'],
                'limit': project_quota.max_tokens,
                'allowed': project_usage['total_tokens'] + estimated_tokens <= project_quota.max_tokens
            }
        }
        
        all_allowed = all(check['allowed'] for check in checks.values())
        
        return {
            'allowed': all_allowed,
            'checks': checks,
            'user_priority': user_quota.priority,
            'estimated_cost': self._estimate_cost(estimated_tokens)
        }
    
    async def make_request(self, user_id: str, project_id: str, 
                          prompt: str, **kwargs) -> Dict[str, Any]:
        """Make AI request with quota checking and queuing"""
        
        estimated_tokens = kwargs.get('max_tokens', 1000)
        
        # Check quota
        quota_check = await self.check_quota(user_id, project_id, estimated_tokens)
        if not quota_check['allowed']:
            raise Exception(f"Quota exceeded: {quota_check['checks']}")
        
        # Add to queue
        request_data = {
            'prompt': prompt,
            'user_id': user_id,
            'project_id': project_id,
            **kwargs
        }
        
        try:
            # Track active request
            self.active_requests[f"{user_id}_{project_id}"] = datetime.now()
            
            # Queue and process request
            result = await self.queue.add_request(request_data, quota_check['user_priority'])
            
            # Record usage
            usage_record = UsageRecord(
                timestamp=datetime.now(),
                user_id=user_id,
                project_id=project_id,
                requests=1,
                tokens=result.get('tokens_used', estimated_tokens),
                cost=result.get('cost', quota_check['estimated_cost']),
                provider=result.get('provider', 'unknown')
            )
            
            self.db.record_usage(usage_record)
            
            return result
            
        finally:
            # Remove from active requests
            self.active_requests.pop(f"{user_id}_{project_id}", None)
    
    def _estimate_cost(self, tokens: int) -> float:
        """Estimate cost for tokens"""
        # Average cost per 1k tokens across providers
        return (tokens / 1000) * 0.002
    
    def get_user_status(self, user_id: str) -> Dict[str, Any]:
        """Get comprehensive user status"""
        user_quota = self.db.get_user_quota(user_id)
        daily_usage = self.db.get_usage_stats(user_id, days=1)
        monthly_usage = self.db.get_usage_stats(user_id, days=30)
        
        return {
            'quota': asdict(user_quota),
            'daily_usage': daily_usage,
            'monthly_usage': monthly_usage,
            'remaining': {
                'daily_requests': user_quota.daily_requests - daily_usage['total_requests'],
                'daily_tokens': user_quota.daily_tokens - daily_usage['total_tokens'],
                'monthly_budget': user_quota.monthly_cost_limit - monthly_usage['total_cost']
            },
            'active_requests': len([k for k in self.active_requests.keys() if k.startswith(user_id)])
        }
    
    def update_user_quota(self, user_id: str, **kwargs):
        """Update user quota settings"""
        current_quota = self.db.get_user_quota(user_id)
        
        # Update fields
        for field, value in kwargs.items():
            if hasattr(current_quota, field):
                setattr(current_quota, field, value)
        
        self.db.set_user_quota(current_quota)
        logger.info(f"Updated quota for user {user_id}: {kwargs}")
    
    def get_system_stats(self) -> Dict[str, Any]:
        """Get system-wide statistics"""
        with sqlite3.connect(self.db.db_path) as conn:
            cursor = conn.execute("""
                SELECT 
                    COUNT(DISTINCT user_id) as total_users,
                    COUNT(DISTINCT project_id) as total_projects,
                    SUM(requests) as total_requests,
                    SUM(tokens) as total_tokens,
                    SUM(cost) as total_cost
                FROM usage_records 
                WHERE timestamp >= ?
            """, ((datetime.now() - timedelta(days=1)).isoformat(),))
            
            row = cursor.fetchone()
            
            return {
                'total_users': row[0] or 0,
                'total_projects': row[1] or 0,
                'daily_requests': row[2] or 0,
                'daily_tokens': row[3] or 0,
                'daily_cost': row[4] or 0.0,
                'active_requests': len(self.active_requests),
                'queue_size': sum(len(q) for q in self.queue.queues.values())
            }

# Global instance
quota_manager = QuotaManager()

if __name__ == "__main__":
    # Test the quota manager
    async def test():
        try:
            # Check quota
            quota_check = await quota_manager.check_quota("test_user", "test_project")
            print(f"Quota check: {quota_check}")
            
            # Get user status
            status = quota_manager.get_user_status("test_user")
            print(f"User status: {status}")
            
            # Get system stats
            stats = quota_manager.get_system_stats()
            print(f"System stats: {stats}")
            
        except Exception as e:
            print(f"Error: {e}")
    
    asyncio.run(test())

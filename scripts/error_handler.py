#!/usr/bin/env python3
"""
Comprehensive Error Handling and Recovery System for ROBO-RESEARCHER-2000
Provides retry mechanisms, circuit breakers, and graceful degradation
"""

import os
import json
import time
import logging
import asyncio
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass
from enum import Enum
from datetime import datetime, timedelta
import sqlite3
from functools import wraps
import traceback

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ErrorSeverity(Enum):
    """Error severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class ErrorCategory(Enum):
    """Error categories"""
    NETWORK = "network"
    API = "api"
    DATABASE = "database"
    FILE_SYSTEM = "file_system"
    VALIDATION = "validation"
    AUTHENTICATION = "authentication"
    PROCESSING = "processing"
    SYSTEM = "system"

@dataclass
class ErrorInfo:
    """Error information structure"""
    error_id: str
    timestamp: datetime
    category: ErrorCategory
    severity: ErrorSeverity
    message: str
    details: Dict[str, Any]
    stack_trace: str
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    recovery_attempted: bool = False
    resolved: bool = False

class CircuitBreaker:
    """Circuit breaker pattern implementation"""
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "closed"  # closed, open, half-open
    
    def call(self, func: Callable, *args, **kwargs):
        """Execute function with circuit breaker protection"""
        if self.state == "open":
            if self._should_attempt_reset():
                self.state = "half-open"
            else:
                raise Exception("Circuit breaker is open")
        
        try:
            result = func(*args, **kwargs)
            self._on_success()
            return result
        except Exception as e:
            self._on_failure()
            raise e
    
    def _should_attempt_reset(self) -> bool:
        """Check if circuit breaker should attempt reset"""
        return (self.last_failure_time and 
                datetime.now() - self.last_failure_time > timedelta(seconds=self.recovery_timeout))
    
    def _on_success(self):
        """Handle successful call"""
        self.failure_count = 0
        self.state = "closed"
    
    def _on_failure(self):
        """Handle failed call"""
        self.failure_count += 1
        self.last_failure_time = datetime.now()
        
        if self.failure_count >= self.failure_threshold:
            self.state = "open"

class RetryManager:
    """Manages retry logic with exponential backoff"""
    
    def __init__(self, max_retries: int = 3, base_delay: float = 1.0, max_delay: float = 60.0):
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.max_delay = max_delay
    
    async def retry_async(self, func: Callable, *args, **kwargs) -> Any:
        """Retry async function with exponential backoff"""
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            try:
                if asyncio.iscoroutinefunction(func):
                    return await func(*args, **kwargs)
                else:
                    return func(*args, **kwargs)
            except Exception as e:
                last_exception = e
                
                if attempt < self.max_retries:
                    delay = min(self.base_delay * (2 ** attempt), self.max_delay)
                    logger.warning(f"Attempt {attempt + 1} failed, retrying in {delay}s: {str(e)}")
                    await asyncio.sleep(delay)
                else:
                    logger.error(f"All {self.max_retries + 1} attempts failed")
        
        raise last_exception
    
    def retry_sync(self, func: Callable, *args, **kwargs) -> Any:
        """Retry sync function with exponential backoff"""
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                last_exception = e
                
                if attempt < self.max_retries:
                    delay = min(self.base_delay * (2 ** attempt), self.max_delay)
                    logger.warning(f"Attempt {attempt + 1} failed, retrying in {delay}s: {str(e)}")
                    time.sleep(delay)
                else:
                    logger.error(f"All {self.max_retries + 1} attempts failed")
        
        raise last_exception

class ErrorHandler:
    """Main error handling system"""
    
    def __init__(self):
        self.db_path = "data/errors.db"
        self.circuit_breakers = {}
        self.retry_manager = RetryManager()
        self._init_database()
    
    def _init_database(self):
        """Initialize error tracking database"""
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS error_logs (
                    error_id TEXT PRIMARY KEY,
                    timestamp TEXT,
                    category TEXT,
                    severity TEXT,
                    message TEXT,
                    details TEXT,
                    stack_trace TEXT,
                    user_id TEXT,
                    session_id TEXT,
                    recovery_attempted INTEGER,
                    resolved INTEGER
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS recovery_actions (
                    action_id TEXT PRIMARY KEY,
                    error_id TEXT,
                    action_type TEXT,
                    action_details TEXT,
                    success INTEGER,
                    timestamp TEXT,
                    FOREIGN KEY (error_id) REFERENCES error_logs (error_id)
                )
            """)
    
    def log_error(self, category: ErrorCategory, message: str, 
                  severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                  details: Dict[str, Any] = None, user_id: str = None,
                  session_id: str = None, exception: Exception = None) -> str:
        """Log an error"""
        
        error_id = f"err_{int(datetime.now().timestamp())}_{hash(message) % 10000}"
        stack_trace = traceback.format_exc() if exception else ""
        
        error_info = ErrorInfo(
            error_id=error_id,
            timestamp=datetime.now(),
            category=category,
            severity=severity,
            message=message,
            details=details or {},
            stack_trace=stack_trace,
            user_id=user_id,
            session_id=session_id
        )
        
        # Store in database
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT INTO error_logs 
                (error_id, timestamp, category, severity, message, details, 
                 stack_trace, user_id, session_id, recovery_attempted, resolved)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                error_id, error_info.timestamp.isoformat(),
                category.value, severity.value, message,
                json.dumps(details or {}), stack_trace,
                user_id, session_id, False, False
            ))
        
        logger.error(f"Error logged: {error_id} - {message}")
        
        # Attempt automatic recovery for certain error types
        if severity in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL]:
            self._attempt_recovery(error_info)
        
        return error_id
    
    def _attempt_recovery(self, error_info: ErrorInfo):
        """Attempt automatic recovery based on error type"""
        recovery_actions = []
        
        if error_info.category == ErrorCategory.API:
            recovery_actions.extend([
                ("switch_provider", "Switch to backup AI provider"),
                ("reduce_load", "Reduce API request rate"),
                ("cache_fallback", "Use cached responses if available")
            ])
        
        elif error_info.category == ErrorCategory.DATABASE:
            recovery_actions.extend([
                ("reconnect", "Reconnect to database"),
                ("backup_restore", "Restore from backup if corruption detected"),
                ("read_only_mode", "Switch to read-only mode")
            ])
        
        elif error_info.category == ErrorCategory.FILE_SYSTEM:
            recovery_actions.extend([
                ("disk_cleanup", "Clean up temporary files"),
                ("permission_fix", "Fix file permissions"),
                ("backup_location", "Use backup storage location")
            ])
        
        # Execute recovery actions
        for action_type, description in recovery_actions:
            try:
                success = self._execute_recovery_action(action_type, error_info)
                self._log_recovery_action(error_info.error_id, action_type, description, success)
                
                if success:
                    self._mark_error_resolved(error_info.error_id)
                    break
                    
            except Exception as e:
                logger.error(f"Recovery action {action_type} failed: {e}")
    
    def _execute_recovery_action(self, action_type: str, error_info: ErrorInfo) -> bool:
        """Execute specific recovery action"""
        
        if action_type == "switch_provider":
            # Switch to backup AI provider
            try:
                from .ai_provider_manager import ai_manager
                available_providers = ai_manager.get_available_providers()
                if len(available_providers) > 1:
                    logger.info("Switched to backup AI provider")
                    return True
            except:
                pass
        
        elif action_type == "reconnect":
            # Reconnect to database
            try:
                with sqlite3.connect(self.db_path) as conn:
                    conn.execute("SELECT 1")
                logger.info("Database reconnection successful")
                return True
            except:
                pass
        
        elif action_type == "disk_cleanup":
            # Clean up temporary files
            try:
                temp_dir = "data/temp"
                if os.path.exists(temp_dir):
                    for file in os.listdir(temp_dir):
                        file_path = os.path.join(temp_dir, file)
                        if os.path.isfile(file_path):
                            os.unlink(file_path)
                logger.info("Temporary files cleaned up")
                return True
            except:
                pass
        
        elif action_type == "reduce_load":
            # Reduce system load
            try:
                # Implement load reduction logic
                logger.info("System load reduced")
                return True
            except:
                pass
        
        return False
    
    def _log_recovery_action(self, error_id: str, action_type: str, 
                           description: str, success: bool):
        """Log recovery action"""
        action_id = f"rec_{int(datetime.now().timestamp())}"
        
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT INTO recovery_actions 
                (action_id, error_id, action_type, action_details, success, timestamp)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (
                action_id, error_id, action_type, description,
                success, datetime.now().isoformat()
            ))
    
    def _mark_error_resolved(self, error_id: str):
        """Mark error as resolved"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                UPDATE error_logs 
                SET resolved = 1, recovery_attempted = 1 
                WHERE error_id = ?
            """, (error_id,))
        
        logger.info(f"Error marked as resolved: {error_id}")
    
    def get_circuit_breaker(self, service_name: str) -> CircuitBreaker:
        """Get or create circuit breaker for service"""
        if service_name not in self.circuit_breakers:
            self.circuit_breakers[service_name] = CircuitBreaker()
        return self.circuit_breakers[service_name]
    
    def handle_with_retry(self, func: Callable, *args, **kwargs) -> Any:
        """Execute function with retry logic"""
        return self.retry_manager.retry_sync(func, *args, **kwargs)
    
    async def handle_with_retry_async(self, func: Callable, *args, **kwargs) -> Any:
        """Execute async function with retry logic"""
        return await self.retry_manager.retry_async(func, *args, **kwargs)
    
    def get_error_stats(self, days: int = 7) -> Dict[str, Any]:
        """Get error statistics"""
        start_date = datetime.now() - timedelta(days=days)
        
        with sqlite3.connect(self.db_path) as conn:
            # Total errors
            cursor = conn.execute("""
                SELECT COUNT(*) FROM error_logs 
                WHERE timestamp >= ?
            """, (start_date.isoformat(),))
            total_errors = cursor.fetchone()[0]
            
            # Errors by category
            cursor = conn.execute("""
                SELECT category, COUNT(*) FROM error_logs 
                WHERE timestamp >= ?
                GROUP BY category
            """, (start_date.isoformat(),))
            errors_by_category = dict(cursor.fetchall())
            
            # Errors by severity
            cursor = conn.execute("""
                SELECT severity, COUNT(*) FROM error_logs 
                WHERE timestamp >= ?
                GROUP BY severity
            """, (start_date.isoformat(),))
            errors_by_severity = dict(cursor.fetchall())
            
            # Recovery success rate
            cursor = conn.execute("""
                SELECT 
                    COUNT(*) as total_recovery_attempts,
                    SUM(CASE WHEN resolved = 1 THEN 1 ELSE 0 END) as successful_recoveries
                FROM error_logs 
                WHERE timestamp >= ? AND recovery_attempted = 1
            """, (start_date.isoformat(),))
            
            recovery_row = cursor.fetchone()
            recovery_attempts = recovery_row[0] or 0
            successful_recoveries = recovery_row[1] or 0
            recovery_rate = (successful_recoveries / recovery_attempts * 100) if recovery_attempts > 0 else 0
            
            return {
                "period_days": days,
                "total_errors": total_errors,
                "errors_by_category": errors_by_category,
                "errors_by_severity": errors_by_severity,
                "recovery_attempts": recovery_attempts,
                "successful_recoveries": successful_recoveries,
                "recovery_success_rate": round(recovery_rate, 2)
            }

def error_handler_decorator(category: ErrorCategory, severity: ErrorSeverity = ErrorSeverity.MEDIUM):
    """Decorator for automatic error handling"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                error_handler.log_error(
                    category=category,
                    message=f"Error in {func.__name__}: {str(e)}",
                    severity=severity,
                    exception=e
                )
                raise
        return wrapper
    return decorator

# Global instance
error_handler = ErrorHandler()

if __name__ == "__main__":
    # Test error handling system
    def test():
        # Test error logging
        error_id = error_handler.log_error(
            ErrorCategory.API,
            "Test API error",
            ErrorSeverity.HIGH,
            {"endpoint": "/test", "status_code": 500}
        )
        print(f"Logged error: {error_id}")
        
        # Test circuit breaker
        breaker = error_handler.get_circuit_breaker("test_service")
        print(f"Circuit breaker state: {breaker.state}")
        
        # Get error stats
        stats = error_handler.get_error_stats()
        print(f"Error stats: {stats}")
    
    test()

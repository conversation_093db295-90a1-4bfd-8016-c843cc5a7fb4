"""
ROBO-RESEARCHER-2000 Presentation Builder
Generates presentations using Marp from structured UX research data.
"""

import json
import logging
import os
import subprocess
from typing import Dict, List, Optional
from dataclasses import dataclass
from datetime import datetime
import tempfile
from pathlib import Path

@dataclass
class PresentationConfig:
    """Configuration for presentation generation"""
    theme: str = 'default'
    output_format: str = 'pptx'  # pptx, pdf, html
    include_charts: bool = True
    include_quotes: bool = True
    max_slides: int = 50
    font_size: str = 'medium'
    color_scheme: str = 'professional'

class PresentationBuilder:
    """Builds presentations from UX research analysis data"""
    
    def __init__(self, config: PresentationConfig = None):
        self.config = config or PresentationConfig()
        self.logger = logging.getLogger(__name__)
        
        # Check if Marp CLI is available
        self.marp_available = self._check_marp_availability()
        
    def _check_marp_availability(self) -> bool:
        """Check if Marp CLI is installed and available"""
        try:
            result = subprocess.run(['marp', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                self.logger.info(f"Marp CLI available: {result.stdout.strip()}")
                return True
        except (subprocess.TimeoutExpired, FileNotFoundError):
            pass
        
        self.logger.warning("Marp CLI not available. Presentation generation will be limited.")
        return False
    
    def generate_presentation(self, analysis_data: Dict, project_metadata: Dict) -> Dict:
        """
        Generate a complete UX research presentation
        
        Args:
            analysis_data: Complete analysis results from all modules
            project_metadata: Project information and metadata
            
        Returns:
            Presentation generation results
        """
        self.logger.info("Starting presentation generation")
        
        try:
            # Generate Markdown content
            markdown_content = self._generate_markdown_content(analysis_data, project_metadata)
            
            # Save markdown file
            markdown_path = self._save_markdown(markdown_content, project_metadata.get('project_name', 'ux_research'))
            
            # Generate presentation files if Marp is available
            output_files = {}
            if self.marp_available:
                output_files = self._generate_presentation_files(markdown_path, project_metadata.get('project_name', 'ux_research'))
            
            return {
                'success': True,
                'markdown_content': markdown_content,
                'markdown_path': markdown_path,
                'output_files': output_files,
                'slide_count': self._count_slides(markdown_content),
                'generation_timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Presentation generation failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'generation_timestamp': datetime.now().isoformat()
            }
    
    def _generate_markdown_content(self, analysis_data: Dict, project_metadata: Dict) -> str:
        """Generate complete Markdown content for the presentation"""
        
        # Extract data from analysis results
        basic_stats = analysis_data.get('quantitative_analysis', {}).get('basic_statistics', {})
        coded_segments = analysis_data.get('coded_segments', [])
        insights = analysis_data.get('insights', [])
        patterns = analysis_data.get('patterns', [])
        archetypos = analysis_data.get('archetypos', [])
        hmw_questions = analysis_data.get('hmw_questions', [])
        opportunities = analysis_data.get('opportunities', [])
        
        # Build markdown content
        content = self._generate_header(project_metadata)
        content += self._generate_title_slide(project_metadata, basic_stats)
        content += self._generate_methodology_slide(project_metadata)
        content += self._generate_key_findings_slides(insights, coded_segments)
        content += self._generate_patterns_slides(patterns)
        content += self._generate_archetypos_slides(archetypos)
        content += self._generate_insights_slides(insights)
        content += self._generate_hmw_slides(hmw_questions)
        content += self._generate_opportunities_slides(opportunities)
        content += self._generate_quantitative_slides(analysis_data.get('quantitative_analysis', {}))
        content += self._generate_next_steps_slide(opportunities)
        content += self._generate_appendix_slides(analysis_data)
        
        return content
    
    def _generate_header(self, project_metadata: Dict) -> str:
        """Generate Marp header with configuration"""
        project_name = project_metadata.get('project_name', 'UX Research')
        date = datetime.now().strftime('%Y-%m-%d')

        return f"""---
marp: true
theme: {self.config.theme}
class: lead
paginate: true
backgroundColor: #fff
backgroundImage: url('https://marp.app/assets/hero-background.svg')
header: 'ROBO-RESEARCHER-2000 | UX Research'
footer: '{project_name} | {date}'
---

"""
    
    def _generate_title_slide(self, project_metadata: Dict, basic_stats: Dict) -> str:
        """Generate title slide"""
        project_name = project_metadata.get('project_name', 'UX Research')
        researcher = project_metadata.get('researcher_name', 'ROBO-RESEARCHER-2000')
        date = datetime.now().strftime('%B %d, %Y')
        participant_count = basic_stats.get('total_participants', 'N/A')
        
        return f"""# {project_name}
## UX Research Results

**Date:** {date}
**Researcher:** {researcher}
**Participants:** {participant_count}
**Generated by:** ROBO-RESEARCHER-2000

---

"""
    
    def _generate_methodology_slide(self, project_metadata: Dict) -> str:
        """Generate methodology slide"""
        study_type = project_metadata.get('study_type', 'Entrevistas de usuario')
        objectives = project_metadata.get('objectives', 'Evaluar la experiencia de usuario')
        
        return f"""## 🔍 Methodology

- **Study type:** {study_type}
- **Analysis method:** Automated qualitative analysis
- **Tools:** ROBO-RESEARCHER-2000 (Python + NLTK/spaCy + AI)
- **Processing:** 17 automated steps

**Objectives:**
{objectives}

---

"""
    
    def _generate_key_findings_slides(self, insights: List[Dict], coded_segments: List[Dict]) -> str:
        """Generate key findings slides"""
        content = "## 🎯 Key Findings\n\n"
        
        # Get top insights
        top_insights = sorted(insights, key=lambda x: x.get('priority_score', 0), reverse=True)[:5]
        
        for i, insight in enumerate(top_insights, 1):
            title = insight.get('title', f'Insight {i}')
            description = insight.get('description', '')
            
            content += f"### {i}. {title}\n\n"
            content += f"{description}\n\n"
            
            # Add supporting quote if available
            supporting_evidence = insight.get('supporting_evidence', {})
            quotes = supporting_evidence.get('representative_quotes', [])
            if quotes:
                quote = quotes[0]
                content += f'> "{quote.get("quote", "")}"  \n'
                content += f'> — {quote.get("participant", "Participante")}\n\n'
            
            content += "---\n\n"
        
        return content
    
    def _generate_patterns_slides(self, patterns: List[Dict]) -> str:
        """Generate pattern analysis slides"""
        if not patterns:
            return ""
        
        content = "## 📊 Identified Patterns\n\n"

        for pattern in patterns[:3]:  # Top 3 patterns
            title = pattern.get('title', 'Detected pattern')
            description = pattern.get('description', '')
            frequency = pattern.get('frequency', '')

            content += f"### {title}\n\n"
            content += f"**Description:** {description}\n\n"
            content += f"**Frequency:** {frequency}\n\n"
            
            # Add evidence
            evidence = pattern.get('evidence', [])
            if evidence:
                content += "**Evidencia:**\n"
                for item in evidence[:2]:  # Max 2 evidence items
                    quote = item.get('quote', '')
                    participant = item.get('participant', 'Participante')
                    content += f'- "{quote}" — {participant}\n'
                content += "\n"
            
            content += "---\n\n"
        
        return content
    
    def _generate_archetypos_slides(self, archetypos: List[Dict]) -> str:
        """Generate user archetype slides"""
        if not archetypos:
            return ""
        
        content = "## 👥 Arquetipos de Usuarios\n\n"
        
        for archetype in archetypos:
            name = archetype.get('name', 'Arquetipo')
            description = archetype.get('description', '')
            representative_quote = archetype.get('representative_quote', '')
            jobs_to_be_done = archetype.get('jobs_to_be_done', [])
            pain_points = archetype.get('pain_points', [])
            
            content += f"### {name}\n\n"
            content += f"{description}\n\n"
            
            if representative_quote:
                content += f'**Quote representativa:**\n'
                content += f'> "{representative_quote}"\n\n'
            
            if jobs_to_be_done:
                content += "**Jobs to be Done:**\n"
                for job in jobs_to_be_done[:3]:
                    content += f"- {job}\n"
                content += "\n"
            
            if pain_points:
                content += "**Puntos de dolor:**\n"
                for pain in pain_points[:3]:
                    content += f"- {pain}\n"
                content += "\n"
            
            content += "---\n\n"
        
        return content
    
    def _generate_insights_slides(self, insights: List[Dict]) -> str:
        """Generate detailed insights slides"""
        if not insights:
            return ""
        
        content = "## 💡 Insights Detallados\n\n"
        
        for insight in insights[:5]:  # Top 5 insights
            title = insight.get('title', 'Insight')
            description = insight.get('description', '')
            business_impact = insight.get('business_impact', '')
            ice_score = insight.get('ice_score', {})
            
            content += f"### {title}\n\n"
            content += f"**Description:** {description}\n\n"
            
            if business_impact:
                content += f"**Business impact:** {business_impact}\n\n"
            
            if ice_score:
                impact = ice_score.get('impact', 0)
                confidence = ice_score.get('confidence', 0)
                ease = ice_score.get('ease', 0)
                total = ice_score.get('total_score', 0)
                
                content += f"**Score ICE:** {total:.1f}\n"
                content += f"- Impact: {impact}/5\n"
                content += f"- Confidence: {confidence}/5\n"
                content += f"- Ease: {ease}/5\n\n"
            
            content += "---\n\n"
        
        return content
    
    def _generate_hmw_slides(self, hmw_questions: List[Dict]) -> str:
        """Generate How Might We slides"""
        if not hmw_questions:
            return ""
        
        content = "## 🚀 ¿Cómo podríamos...?\n\n"
        
        for hmw in hmw_questions[:6]:  # Top 6 HMW questions
            question = hmw.get('question', '')
            outcome = hmw.get('outcome', '')
            constraint = hmw.get('constraint', '')
            
            content += f"### {question}\n\n"
            
            if outcome:
                content += f"**Para que:** {outcome}\n\n"
            
            if constraint:
                content += f"**Sin que:** {constraint}\n\n"
            
            content += "---\n\n"
        
        return content
    
    def _generate_opportunities_slides(self, opportunities: List[Dict]) -> str:
        """Generate opportunities and prioritization slides"""
        if not opportunities:
            return ""
        
        content = "## 📈 Oportunidades Priorizadas\n\n"
        content += "| Oportunidad | Reach | Impact | Confidence | Effort | Score |\n"
        content += "|-------------|-------|--------|------------|--------|-------|\n"
        
        for opp in opportunities[:8]:  # Top 8 opportunities
            name = opp.get('name', 'Oportunidad')
            reach = opp.get('reach', 0)
            impact = opp.get('impact', 0)
            confidence = opp.get('confidence', 0)
            effort = opp.get('effort', 0)
            rice_score = opp.get('rice_score', 0)
            
            content += f"| {name} | {reach} | {impact} | {confidence} | {effort} | **{rice_score:.1f}** |\n"
        
        content += "\n---\n\n"
        
        return content
    
    def _generate_quantitative_slides(self, quantitative_data: Dict) -> str:
        """Generate quantitative analysis slides"""
        if not quantitative_data:
            return ""
        
        content = "## 📊 Quantitative Analysis\n\n"
        
        basic_stats = quantitative_data.get('basic_statistics', {})
        code_freq = quantitative_data.get('code_frequency_analysis', {})
        
        # Basic statistics
        content += "### Estadísticas Generales\n\n"
        content += f"- **Total de segmentos:** {basic_stats.get('total_segments', 0)}\n"
        content += f"- **Códigos únicos:** {basic_stats.get('total_codes', 0)}\n"
        content += f"- **Participantes:** {basic_stats.get('total_participants', 0)}\n"
        content += f"- **Intensidad promedio:** {basic_stats.get('average_intensity', 0):.1f}/5\n\n"
        
        # Top codes
        top_codes = code_freq.get('top_codes', {})
        if top_codes:
            content += "### Códigos Más Frecuentes\n\n"
            for code, data in list(top_codes.items())[:5]:
                frequency = data.get('frequency', 0)
                percentage = data.get('percentage', 0)
                content += f"- **{code}:** {frequency} menciones ({percentage}%)\n"
            content += "\n"
        
        content += "---\n\n"
        
        return content
    
    def _generate_next_steps_slide(self, opportunities: List[Dict]) -> str:
        """Generate next steps slide"""
        content = "## 🔄 Próximos Pasos\n\n"
        
        # Categorize by timeline
        immediate = [opp for opp in opportunities if opp.get('timeline', '') == 'immediate']
        short_term = [opp for opp in opportunities if opp.get('timeline', '') == 'short_term']
        long_term = [opp for opp in opportunities if opp.get('timeline', '') == 'long_term']
        
        if immediate:
            content += "### Inmediatos (1-2 semanas)\n"
            for opp in immediate[:3]:
                content += f"- {opp.get('name', 'Acción')}\n"
            content += "\n"
        
        if short_term:
            content += "### Corto plazo (1-3 meses)\n"
            for opp in short_term[:3]:
                content += f"- {opp.get('name', 'Acción')}\n"
            content += "\n"
        
        if long_term:
            content += "### Largo plazo (3-6 meses)\n"
            for opp in long_term[:3]:
                content += f"- {opp.get('name', 'Acción')}\n"
            content += "\n"
        
        content += "---\n\n"
        
        return content
    
    def _generate_appendix_slides(self, analysis_data: Dict) -> str:
        """Generate appendix slides"""
        content = "## 📚 Apéndice\n\n"
        content += "### Metodología Detallada\n"
        content += "- **Procesamiento:** 17 pasos automatizados\n"
        content += "- **Herramientas:** Python + NLTK/spaCy + OpenRouter AI\n"
        content += "- **Análisis:** Codificación deductiva + abierta\n"
        content += "- **Validación:** Análisis cuantitativo + patrones\n\n"
        
        content += "### Datos Adicionales\n"
        content += "- **Transcripciones completas:** Disponibles en Wiki.js\n"
        content += "- **Códigos detallados:** Repositorio de datos\n"
        content += "- **Visualizaciones:** Mapas de afinidad SVG\n\n"
        
        content += "---\n\n"
        content += "# ¿Preguntas?\n\n"
        content += "**Generado por:** ROBO-RESEARCHER-2000\n"
        content += "**Documentación completa:** Wiki.js\n"
        content += "**Próxima revisión:** 90 días\n\n"
        
        return content
    
    def _save_markdown(self, content: str, project_name: str) -> str:
        """Save markdown content to file"""
        # Create safe filename
        safe_name = "".join(c for c in project_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
        safe_name = safe_name.replace(' ', '_').lower()
        
        filename = f"{safe_name}_presentation.md"
        filepath = os.path.join(tempfile.gettempdir(), filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
        
        self.logger.info(f"Markdown saved to: {filepath}")
        return filepath
    
    def _generate_presentation_files(self, markdown_path: str, project_name: str) -> Dict:
        """Generate presentation files using Marp CLI"""
        output_files = {}
        
        if not self.marp_available:
            return output_files
        
        # Create safe filename
        safe_name = "".join(c for c in project_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
        safe_name = safe_name.replace(' ', '_').lower()
        
        # Generate PPTX
        if self.config.output_format in ['pptx', 'all']:
            pptx_path = os.path.join(tempfile.gettempdir(), f"{safe_name}_presentation.pptx")
            try:
                result = subprocess.run([
                    'marp', markdown_path, 
                    '--output', pptx_path,
                    '--theme', self.config.theme
                ], capture_output=True, text=True, timeout=60)
                
                if result.returncode == 0:
                    output_files['pptx'] = pptx_path
                    self.logger.info(f"PPTX generated: {pptx_path}")
                else:
                    self.logger.error(f"PPTX generation failed: {result.stderr}")
            except subprocess.TimeoutExpired:
                self.logger.error("PPTX generation timed out")
        
        # Generate PDF
        if self.config.output_format in ['pdf', 'all']:
            pdf_path = os.path.join(tempfile.gettempdir(), f"{safe_name}_presentation.pdf")
            try:
                result = subprocess.run([
                    'marp', markdown_path,
                    '--output', pdf_path,
                    '--theme', self.config.theme,
                    '--pdf'
                ], capture_output=True, text=True, timeout=60)
                
                if result.returncode == 0:
                    output_files['pdf'] = pdf_path
                    self.logger.info(f"PDF generated: {pdf_path}")
                else:
                    self.logger.error(f"PDF generation failed: {result.stderr}")
            except subprocess.TimeoutExpired:
                self.logger.error("PDF generation timed out")
        
        return output_files
    
    def _count_slides(self, markdown_content: str) -> int:
        """Count the number of slides in the markdown content"""
        return markdown_content.count('---') + 1

def generate_presentation_for_n8n(analysis_data: Dict, project_metadata: Dict, config_dict: Dict = None) -> Dict:
    """
    Wrapper function for n8n integration
    
    Args:
        analysis_data: Complete analysis results
        project_metadata: Project information
        config_dict: Configuration dictionary
        
    Returns:
        Presentation generation results
    """
    config = PresentationConfig()
    if config_dict:
        for key, value in config_dict.items():
            if hasattr(config, key):
                setattr(config, key, value)
    
    builder = PresentationBuilder(config)
    result = builder.generate_presentation(analysis_data, project_metadata)
    
    return result

if __name__ == "__main__":
    # Example usage
    sample_analysis_data = {
        'quantitative_analysis': {
            'basic_statistics': {
                'total_segments': 25,
                'total_codes': 15,
                'total_participants': 5,
                'average_intensity': 3.2
            }
        },
        'insights': [
            {
                'title': 'Navegación confusa',
                'description': 'Los usuarios tienen dificultades para encontrar productos',
                'priority_score': 8.5
            }
        ]
    }
    
    sample_metadata = {
        'project_name': 'Investigación App Móvil',
        'researcher_name': 'Equipo UX',
        'study_type': 'Entrevistas de usuario'
    }
    
    result = generate_presentation_for_n8n(sample_analysis_data, sample_metadata)
    print(json.dumps(result, indent=2, ensure_ascii=False))

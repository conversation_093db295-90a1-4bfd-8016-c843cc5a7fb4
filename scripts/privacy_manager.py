#!/usr/bin/env python3
"""
Data Retention and Privacy Manager for ROBO-RESEARCHER-2000
Implements GDPR compliance, data retention policies, and privacy controls
"""

import os
import json
import sqlite3
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timed<PERSON>ta
from dataclasses import dataclass
from enum import Enum
import shutil
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DataCategory(Enum):
    """Categories of data for retention policies"""
    USER_PROFILE = "user_profile"
    TRANSCRIPTION = "transcription"
    ANALYSIS_RESULTS = "analysis_results"
    UPLOADED_FILES = "uploaded_files"
    SYSTEM_LOGS = "system_logs"
    AUDIT_LOGS = "audit_logs"
    TEMPORARY_DATA = "temporary_data"

class RetentionPeriod(Enum):
    """Standard retention periods"""
    IMMEDIATE = 0  # Delete immediately
    DAYS_30 = 30
    DAYS_90 = 90
    DAYS_180 = 180
    YEAR_1 = 365
    YEARS_2 = 730
    YEARS_7 = 2555  # Legal requirement for some data
    PERMANENT = -1  # Never delete

@dataclass
class RetentionPolicy:
    """Data retention policy configuration"""
    category: DataCategory
    retention_period: RetentionPeriod
    auto_delete: bool = True
    requires_approval: bool = False
    backup_before_delete: bool = True

@dataclass
class DataRecord:
    """Data record for tracking"""
    record_id: str
    category: DataCategory
    user_id: str
    created_at: datetime
    last_accessed: datetime
    file_path: Optional[str] = None
    metadata: Dict[str, Any] = None
    marked_for_deletion: bool = False

class PrivacyManager:
    """Main privacy and data retention manager"""
    
    def __init__(self):
        self.db_path = "data/privacy.db"
        self.backup_dir = Path("data/backups")
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        self._init_database()
        self._load_default_policies()
    
    def _init_database(self):
        """Initialize privacy management database"""
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS retention_policies (
                    category TEXT PRIMARY KEY,
                    retention_days INTEGER,
                    auto_delete INTEGER,
                    requires_approval INTEGER,
                    backup_before_delete INTEGER
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS data_records (
                    record_id TEXT PRIMARY KEY,
                    category TEXT,
                    user_id TEXT,
                    created_at TEXT,
                    last_accessed TEXT,
                    file_path TEXT,
                    metadata TEXT,
                    marked_for_deletion INTEGER DEFAULT 0,
                    deletion_scheduled TEXT
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS deletion_requests (
                    request_id TEXT PRIMARY KEY,
                    user_id TEXT,
                    request_type TEXT,
                    status TEXT,
                    requested_at TEXT,
                    processed_at TEXT,
                    details TEXT
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS consent_records (
                    user_id TEXT PRIMARY KEY,
                    data_processing_consent INTEGER,
                    analytics_consent INTEGER,
                    marketing_consent INTEGER,
                    consent_date TEXT,
                    consent_version TEXT
                )
            """)
    
    def _load_default_policies(self):
        """Load default retention policies"""
        default_policies = [
            RetentionPolicy(DataCategory.USER_PROFILE, RetentionPeriod.YEARS_2),
            RetentionPolicy(DataCategory.TRANSCRIPTION, RetentionPeriod.YEAR_1),
            RetentionPolicy(DataCategory.ANALYSIS_RESULTS, RetentionPeriod.YEAR_1),
            RetentionPolicy(DataCategory.UPLOADED_FILES, RetentionPeriod.DAYS_90),
            RetentionPolicy(DataCategory.SYSTEM_LOGS, RetentionPeriod.DAYS_30),
            RetentionPolicy(DataCategory.AUDIT_LOGS, RetentionPeriod.YEARS_7, auto_delete=False),
            RetentionPolicy(DataCategory.TEMPORARY_DATA, RetentionPeriod.DAYS_30)
        ]
        
        with sqlite3.connect(self.db_path) as conn:
            for policy in default_policies:
                conn.execute("""
                    INSERT OR IGNORE INTO retention_policies 
                    (category, retention_days, auto_delete, requires_approval, backup_before_delete)
                    VALUES (?, ?, ?, ?, ?)
                """, (
                    policy.category.value,
                    policy.retention_period.value,
                    policy.auto_delete,
                    policy.requires_approval,
                    policy.backup_before_delete
                ))
    
    def register_data(self, record_id: str, category: DataCategory, user_id: str,
                     file_path: str = None, metadata: Dict[str, Any] = None) -> bool:
        """Register data for tracking"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO data_records 
                    (record_id, category, user_id, created_at, last_accessed, file_path, metadata)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    record_id, category.value, user_id,
                    datetime.now().isoformat(),
                    datetime.now().isoformat(),
                    file_path,
                    json.dumps(metadata) if metadata else None
                ))
            
            logger.info(f"Registered data record: {record_id} ({category.value})")
            return True
            
        except Exception as e:
            logger.error(f"Failed to register data record: {e}")
            return False
    
    def update_access_time(self, record_id: str):
        """Update last access time for data record"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                UPDATE data_records 
                SET last_accessed = ? 
                WHERE record_id = ?
            """, (datetime.now().isoformat(), record_id))
    
    def request_data_deletion(self, user_id: str, request_type: str = "full_account",
                            details: Dict[str, Any] = None) -> str:
        """Request data deletion (GDPR right to be forgotten)"""
        request_id = f"del_{user_id}_{int(datetime.now().timestamp())}"
        
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT INTO deletion_requests 
                (request_id, user_id, request_type, status, requested_at, details)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (
                request_id, user_id, request_type, "pending",
                datetime.now().isoformat(),
                json.dumps(details) if details else None
            ))
        
        logger.info(f"Data deletion requested: {request_id}")
        return request_id
    
    def process_deletion_request(self, request_id: str) -> Dict[str, Any]:
        """Process a data deletion request"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # Get request details
                cursor = conn.execute("""
                    SELECT user_id, request_type, details 
                    FROM deletion_requests 
                    WHERE request_id = ? AND status = 'pending'
                """, (request_id,))
                
                row = cursor.fetchone()
                if not row:
                    return {"success": False, "error": "Request not found or already processed"}
                
                user_id, request_type, details = row
                
                # Get user data records
                cursor = conn.execute("""
                    SELECT record_id, category, file_path 
                    FROM data_records 
                    WHERE user_id = ?
                """, (user_id,))
                
                records = cursor.fetchall()
                deleted_count = 0
                errors = []
                
                for record_id, category, file_path in records:
                    try:
                        # Backup before deletion if required
                        policy = self._get_retention_policy(DataCategory(category))
                        if policy and policy.backup_before_delete:
                            self._backup_data_record(record_id, file_path)
                        
                        # Delete file if exists
                        if file_path and os.path.exists(file_path):
                            os.unlink(file_path)
                        
                        # Remove from database
                        conn.execute("DELETE FROM data_records WHERE record_id = ?", (record_id,))
                        deleted_count += 1
                        
                    except Exception as e:
                        errors.append(f"Failed to delete {record_id}: {str(e)}")
                
                # Update request status
                conn.execute("""
                    UPDATE deletion_requests 
                    SET status = ?, processed_at = ? 
                    WHERE request_id = ?
                """, ("completed", datetime.now().isoformat(), request_id))
                
                logger.info(f"Processed deletion request {request_id}: {deleted_count} records deleted")
                
                return {
                    "success": True,
                    "deleted_count": deleted_count,
                    "errors": errors
                }
                
        except Exception as e:
            logger.error(f"Failed to process deletion request: {e}")
            return {"success": False, "error": str(e)}
    
    def _get_retention_policy(self, category: DataCategory) -> Optional[RetentionPolicy]:
        """Get retention policy for data category"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                SELECT retention_days, auto_delete, requires_approval, backup_before_delete
                FROM retention_policies 
                WHERE category = ?
            """, (category.value,))
            
            row = cursor.fetchone()
            if row:
                return RetentionPolicy(
                    category=category,
                    retention_period=RetentionPeriod(row[0]),
                    auto_delete=bool(row[1]),
                    requires_approval=bool(row[2]),
                    backup_before_delete=bool(row[3])
                )
            return None
    
    def _backup_data_record(self, record_id: str, file_path: str = None):
        """Backup data record before deletion"""
        try:
            backup_path = self.backup_dir / f"deleted_{record_id}_{datetime.now().strftime('%Y%m%d')}"
            backup_path.mkdir(exist_ok=True)
            
            if file_path and os.path.exists(file_path):
                shutil.copy2(file_path, backup_path / os.path.basename(file_path))
            
            # Create metadata file
            with open(backup_path / "metadata.json", 'w') as f:
                json.dump({
                    "record_id": record_id,
                    "deleted_at": datetime.now().isoformat(),
                    "original_path": file_path
                }, f)
            
            logger.info(f"Backed up data record: {record_id}")
            
        except Exception as e:
            logger.error(f"Failed to backup data record {record_id}: {e}")
    
    def cleanup_expired_data(self) -> Dict[str, Any]:
        """Clean up expired data based on retention policies"""
        cleaned_count = 0
        errors = []
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                # Get all data records with their policies
                cursor = conn.execute("""
                    SELECT dr.record_id, dr.category, dr.created_at, dr.file_path,
                           rp.retention_days, rp.auto_delete, rp.backup_before_delete
                    FROM data_records dr
                    JOIN retention_policies rp ON dr.category = rp.category
                    WHERE rp.auto_delete = 1 AND rp.retention_days > 0
                """)
                
                for row in cursor.fetchall():
                    record_id, category, created_at, file_path, retention_days, auto_delete, backup_before_delete = row
                    
                    # Check if expired
                    created_date = datetime.fromisoformat(created_at)
                    expiry_date = created_date + timedelta(days=retention_days)
                    
                    if datetime.now() > expiry_date:
                        try:
                            # Backup if required
                            if backup_before_delete:
                                self._backup_data_record(record_id, file_path)
                            
                            # Delete file
                            if file_path and os.path.exists(file_path):
                                os.unlink(file_path)
                            
                            # Remove from database
                            conn.execute("DELETE FROM data_records WHERE record_id = ?", (record_id,))
                            cleaned_count += 1
                            
                        except Exception as e:
                            errors.append(f"Failed to clean {record_id}: {str(e)}")
            
            logger.info(f"Data cleanup completed: {cleaned_count} records cleaned")
            
            return {
                "success": True,
                "cleaned_count": cleaned_count,
                "errors": errors
            }
            
        except Exception as e:
            logger.error(f"Data cleanup failed: {e}")
            return {"success": False, "error": str(e)}
    
    def get_user_data_summary(self, user_id: str) -> Dict[str, Any]:
        """Get summary of user's data (GDPR data portability)"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                SELECT category, COUNT(*), SUM(LENGTH(COALESCE(metadata, '')))
                FROM data_records 
                WHERE user_id = ?
                GROUP BY category
            """, (user_id,))
            
            data_by_category = {}
            for row in cursor.fetchall():
                category, count, total_size = row
                data_by_category[category] = {
                    "record_count": count,
                    "total_size": total_size or 0
                }
            
            # Get consent status
            cursor = conn.execute("""
                SELECT data_processing_consent, analytics_consent, marketing_consent, consent_date
                FROM consent_records 
                WHERE user_id = ?
            """, (user_id,))
            
            consent_row = cursor.fetchone()
            consent_status = {}
            if consent_row:
                consent_status = {
                    "data_processing": bool(consent_row[0]),
                    "analytics": bool(consent_row[1]),
                    "marketing": bool(consent_row[2]),
                    "consent_date": consent_row[3]
                }
            
            return {
                "user_id": user_id,
                "data_by_category": data_by_category,
                "consent_status": consent_status,
                "total_records": sum(cat["record_count"] for cat in data_by_category.values())
            }
    
    def update_consent(self, user_id: str, data_processing: bool = True,
                      analytics: bool = False, marketing: bool = False) -> bool:
        """Update user consent preferences"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO consent_records 
                    (user_id, data_processing_consent, analytics_consent, marketing_consent, 
                     consent_date, consent_version)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    user_id, data_processing, analytics, marketing,
                    datetime.now().isoformat(), "1.0"
                ))
            
            logger.info(f"Updated consent for user: {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update consent: {e}")
            return False

# Global instance
privacy_manager = PrivacyManager()

if __name__ == "__main__":
    # Test privacy manager
    def test():
        # Register some test data
        privacy_manager.register_data(
            "test_record_1", DataCategory.TRANSCRIPTION, "test_user",
            metadata={"size": 1024, "type": "interview"}
        )
        
        # Get user data summary
        summary = privacy_manager.get_user_data_summary("test_user")
        print(f"User data summary: {summary}")
        
        # Update consent
        privacy_manager.update_consent("test_user", analytics=True)
        
        # Request deletion
        request_id = privacy_manager.request_data_deletion("test_user")
        print(f"Deletion request: {request_id}")
    
    test()

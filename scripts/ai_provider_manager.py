#!/usr/bin/env python3
"""
AI Provider Manager for ROBO-RESEARCHER-2000
Handles multiple AI providers with automatic failover and rate limiting
"""

import os
import time
import json
import logging
import requests
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum
import asyncio
import aiohttp
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ProviderType(Enum):
    OPENROUTER = "openrouter"
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    GOOGLE = "google"
    LOCAL = "local"

@dataclass
class ProviderConfig:
    """Configuration for an AI provider"""
    name: str
    type: ProviderType
    api_key: str
    base_url: str
    models: List[str]
    rate_limit_rpm: int  # Requests per minute
    rate_limit_tpm: int  # Tokens per minute
    priority: int  # Lower number = higher priority
    enabled: bool = True
    timeout: int = 30
    max_retries: int = 3

@dataclass
class UsageStats:
    """Track usage statistics for a provider"""
    requests_count: int = 0
    tokens_used: int = 0
    errors_count: int = 0
    last_request: Optional[datetime] = None
    total_cost: float = 0.0
    
class RateLimiter:
    """Rate limiting implementation"""
    
    def __init__(self, rpm: int, tpm: int):
        self.rpm = rpm  # Requests per minute
        self.tpm = tpm  # Tokens per minute
        self.request_times = []
        self.token_usage = []
        
    def can_make_request(self, estimated_tokens: int = 1000) -> bool:
        """Check if request can be made within rate limits"""
        now = datetime.now()
        minute_ago = now - timedelta(minutes=1)
        
        # Clean old entries
        self.request_times = [t for t in self.request_times if t > minute_ago]
        self.token_usage = [(t, tokens) for t, tokens in self.token_usage if t > minute_ago]
        
        # Check request rate limit
        if len(self.request_times) >= self.rpm:
            return False
            
        # Check token rate limit
        current_tokens = sum(tokens for _, tokens in self.token_usage)
        if current_tokens + estimated_tokens > self.tpm:
            return False
            
        return True
    
    def record_request(self, tokens_used: int):
        """Record a successful request"""
        now = datetime.now()
        self.request_times.append(now)
        self.token_usage.append((now, tokens_used))

class AIProviderManager:
    """Manages multiple AI providers with failover and rate limiting"""
    
    def __init__(self):
        self.providers: Dict[str, ProviderConfig] = {}
        self.rate_limiters: Dict[str, RateLimiter] = {}
        self.usage_stats: Dict[str, UsageStats] = {}
        self.circuit_breakers: Dict[str, Dict] = {}
        self._load_providers()
    
    def _load_providers(self):
        """Load provider configurations from environment variables"""
        
        # OpenRouter (primary)
        if os.getenv('OPENROUTER_API_KEY'):
            self.add_provider(ProviderConfig(
                name="openrouter",
                type=ProviderType.OPENROUTER,
                api_key=os.getenv('OPENROUTER_API_KEY'),
                base_url="https://openrouter.ai/api/v1",
                models=["anthropic/claude-3-sonnet", "openai/gpt-4o-mini", "google/gemini-pro"],
                rate_limit_rpm=60,
                rate_limit_tpm=100000,
                priority=1
            ))
        
        # OpenAI (fallback)
        if os.getenv('OPENAI_API_KEY'):
            self.add_provider(ProviderConfig(
                name="openai",
                type=ProviderType.OPENAI,
                api_key=os.getenv('OPENAI_API_KEY'),
                base_url="https://api.openai.com/v1",
                models=["gpt-4o-mini", "gpt-3.5-turbo"],
                rate_limit_rpm=50,
                rate_limit_tpm=80000,
                priority=2
            ))
        
        # Anthropic (fallback)
        if os.getenv('ANTHROPIC_API_KEY'):
            self.add_provider(ProviderConfig(
                name="anthropic",
                type=ProviderType.ANTHROPIC,
                api_key=os.getenv('ANTHROPIC_API_KEY'),
                base_url="https://api.anthropic.com/v1",
                models=["claude-3-sonnet-20240229", "claude-3-haiku-20240307"],
                rate_limit_rpm=40,
                rate_limit_tpm=60000,
                priority=3
            ))
        
        # Google (fallback)
        if os.getenv('GOOGLE_API_KEY'):
            self.add_provider(ProviderConfig(
                name="google",
                type=ProviderType.GOOGLE,
                api_key=os.getenv('GOOGLE_API_KEY'),
                base_url="https://generativelanguage.googleapis.com/v1",
                models=["gemini-pro", "gemini-pro-vision"],
                rate_limit_rpm=30,
                rate_limit_tpm=50000,
                priority=4
            ))
        
        # Local fallback (if configured)
        if os.getenv('LOCAL_AI_ENDPOINT'):
            self.add_provider(ProviderConfig(
                name="local",
                type=ProviderType.LOCAL,
                api_key="local",
                base_url=os.getenv('LOCAL_AI_ENDPOINT'),
                models=["local-model"],
                rate_limit_rpm=100,
                rate_limit_tpm=200000,
                priority=5
            ))
    
    def add_provider(self, config: ProviderConfig):
        """Add a new provider configuration"""
        self.providers[config.name] = config
        self.rate_limiters[config.name] = RateLimiter(config.rate_limit_rpm, config.rate_limit_tpm)
        self.usage_stats[config.name] = UsageStats()
        self.circuit_breakers[config.name] = {
            'failures': 0,
            'last_failure': None,
            'state': 'closed'  # closed, open, half-open
        }
        logger.info(f"Added AI provider: {config.name} ({config.type.value})")
    
    def get_available_providers(self) -> List[ProviderConfig]:
        """Get list of available providers sorted by priority"""
        available = []
        for provider in sorted(self.providers.values(), key=lambda p: p.priority):
            if provider.enabled and self._is_provider_healthy(provider.name):
                available.append(provider)
        return available
    
    def _is_provider_healthy(self, provider_name: str) -> bool:
        """Check if provider is healthy (circuit breaker logic)"""
        breaker = self.circuit_breakers[provider_name]
        
        if breaker['state'] == 'open':
            # Check if we should try again (after 5 minutes)
            if breaker['last_failure'] and \
               datetime.now() - breaker['last_failure'] > timedelta(minutes=5):
                breaker['state'] = 'half-open'
                return True
            return False
        
        return True
    
    def _record_success(self, provider_name: str):
        """Record successful request"""
        breaker = self.circuit_breakers[provider_name]
        breaker['failures'] = 0
        breaker['state'] = 'closed'
    
    def _record_failure(self, provider_name: str):
        """Record failed request"""
        breaker = self.circuit_breakers[provider_name]
        breaker['failures'] += 1
        breaker['last_failure'] = datetime.now()
        
        # Open circuit breaker after 3 failures
        if breaker['failures'] >= 3:
            breaker['state'] = 'open'
            logger.warning(f"Circuit breaker opened for provider: {provider_name}")
    
    async def make_request(self, prompt: str, model_preference: str = None, 
                          max_tokens: int = 1000, temperature: float = 0.3) -> Dict[str, Any]:
        """Make AI request with automatic failover"""
        
        available_providers = self.get_available_providers()
        if not available_providers:
            raise Exception("No AI providers available")
        
        last_error = None
        
        for provider in available_providers:
            try:
                # Check rate limits
                if not self.rate_limiters[provider.name].can_make_request(max_tokens):
                    logger.info(f"Rate limit exceeded for {provider.name}, trying next provider")
                    continue
                
                # Make request
                response = await self._make_provider_request(
                    provider, prompt, model_preference, max_tokens, temperature
                )
                
                # Record success
                self._record_success(provider.name)
                self.rate_limiters[provider.name].record_request(response.get('tokens_used', max_tokens))
                
                # Update usage stats
                stats = self.usage_stats[provider.name]
                stats.requests_count += 1
                stats.tokens_used += response.get('tokens_used', max_tokens)
                stats.last_request = datetime.now()
                stats.total_cost += response.get('cost', 0.0)
                
                logger.info(f"Successful request using provider: {provider.name}")
                return {
                    'response': response['content'],
                    'provider': provider.name,
                    'model': response.get('model', 'unknown'),
                    'tokens_used': response.get('tokens_used', max_tokens),
                    'cost': response.get('cost', 0.0)
                }
                
            except Exception as e:
                logger.error(f"Provider {provider.name} failed: {str(e)}")
                self._record_failure(provider.name)
                self.usage_stats[provider.name].errors_count += 1
                last_error = e
                continue
        
        # All providers failed
        raise Exception(f"All AI providers failed. Last error: {str(last_error)}")
    
    async def _make_provider_request(self, provider: ProviderConfig, prompt: str, 
                                   model_preference: str, max_tokens: int, 
                                   temperature: float) -> Dict[str, Any]:
        """Make request to specific provider"""
        
        # Select model
        model = model_preference if model_preference in provider.models else provider.models[0]
        
        if provider.type == ProviderType.OPENROUTER:
            return await self._openrouter_request(provider, model, prompt, max_tokens, temperature)
        elif provider.type == ProviderType.OPENAI:
            return await self._openai_request(provider, model, prompt, max_tokens, temperature)
        elif provider.type == ProviderType.ANTHROPIC:
            return await self._anthropic_request(provider, model, prompt, max_tokens, temperature)
        elif provider.type == ProviderType.GOOGLE:
            return await self._google_request(provider, model, prompt, max_tokens, temperature)
        elif provider.type == ProviderType.LOCAL:
            return await self._local_request(provider, model, prompt, max_tokens, temperature)
        else:
            raise Exception(f"Unsupported provider type: {provider.type}")
    
    async def _openrouter_request(self, provider: ProviderConfig, model: str, 
                                prompt: str, max_tokens: int, temperature: float) -> Dict[str, Any]:
        """Make request to OpenRouter API"""
        headers = {
            'Authorization': f'Bearer {provider.api_key}',
            'Content-Type': 'application/json',
            'HTTP-Referer': 'https://robo-researcher-2000.local',
            'X-Title': 'ROBO-RESEARCHER-2000'
        }
        
        data = {
            'model': model,
            'messages': [{'role': 'user', 'content': prompt}],
            'max_tokens': max_tokens,
            'temperature': temperature
        }
        
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=provider.timeout)) as session:
            async with session.post(f"{provider.base_url}/chat/completions", 
                                   headers=headers, json=data) as response:
                if response.status != 200:
                    raise Exception(f"OpenRouter API error: {response.status}")
                
                result = await response.json()
                return {
                    'content': result['choices'][0]['message']['content'],
                    'model': result['model'],
                    'tokens_used': result['usage']['total_tokens'],
                    'cost': self._calculate_cost(result['usage']['total_tokens'], model)
                }
    
    async def _openai_request(self, provider: ProviderConfig, model: str, 
                            prompt: str, max_tokens: int, temperature: float) -> Dict[str, Any]:
        """Make request to OpenAI API"""
        headers = {
            'Authorization': f'Bearer {provider.api_key}',
            'Content-Type': 'application/json'
        }
        
        data = {
            'model': model,
            'messages': [{'role': 'user', 'content': prompt}],
            'max_tokens': max_tokens,
            'temperature': temperature
        }
        
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=provider.timeout)) as session:
            async with session.post(f"{provider.base_url}/chat/completions", 
                                   headers=headers, json=data) as response:
                if response.status != 200:
                    raise Exception(f"OpenAI API error: {response.status}")
                
                result = await response.json()
                return {
                    'content': result['choices'][0]['message']['content'],
                    'model': result['model'],
                    'tokens_used': result['usage']['total_tokens'],
                    'cost': self._calculate_cost(result['usage']['total_tokens'], model)
                }
    
    def _calculate_cost(self, tokens: int, model: str) -> float:
        """Calculate estimated cost based on tokens and model"""
        # Simplified cost calculation - should be updated with actual pricing
        cost_per_1k_tokens = {
            'gpt-4o-mini': 0.0015,
            'gpt-3.5-turbo': 0.001,
            'claude-3-sonnet': 0.003,
            'claude-3-haiku': 0.00025,
            'gemini-pro': 0.0005
        }
        
        base_cost = cost_per_1k_tokens.get(model.split('/')[-1], 0.001)
        return (tokens / 1000) * base_cost
    
    def get_usage_stats(self) -> Dict[str, Dict]:
        """Get usage statistics for all providers"""
        stats = {}
        for name, usage in self.usage_stats.items():
            stats[name] = {
                **asdict(usage),
                'last_request': usage.last_request.isoformat() if usage.last_request else None,
                'circuit_breaker_state': self.circuit_breakers[name]['state'],
                'enabled': self.providers[name].enabled
            }
        return stats

# Global instance
ai_manager = AIProviderManager()

if __name__ == "__main__":
    # Test the AI provider manager
    async def test():
        try:
            response = await ai_manager.make_request(
                "Hello, this is a test message. Please respond briefly.",
                max_tokens=100
            )
            print(f"Response: {response}")
            print(f"Usage stats: {ai_manager.get_usage_stats()}")
        except Exception as e:
            print(f"Error: {e}")
    
    asyncio.run(test())

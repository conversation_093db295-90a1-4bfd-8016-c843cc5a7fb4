#!/usr/bin/env python3
"""
Secure File Upload and Processing Module for ROBO-RESEARCHER-2000
Handles file upload validation, virus scanning, and secure storage
"""

import os
import re
import hashlib
import mimetypes
import tempfile
import subprocess
import logging
from typing import List, Dict, Any, Tuple, Optional
from pathlib import Path
import magic
from werkzeug.utils import secure_filename
from datetime import datetime, timedelta
import sqlite3

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FileSecurityValidator:
    """Validates uploaded files for security"""
    
    def __init__(self):
        self.allowed_extensions = {'.txt', '.doc', '.docx', '.pdf', '.rtf', '.odt'}
        self.allowed_mime_types = {
            'text/plain',
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/rtf',
            'application/vnd.oasis.opendocument.text'
        }
        self.max_file_size = 10 * 1024 * 1024  # 10MB
        self.min_file_size = 100  # 100 bytes
        
        # Malicious patterns to check for
        self.malicious_patterns = [
            rb'<script[^>]*>',
            rb'javascript:',
            rb'vbscript:',
            rb'onload\s*=',
            rb'onerror\s*=',
            rb'eval\s*\(',
            rb'document\.cookie',
            rb'window\.location',
            rb'<iframe[^>]*>',
            rb'<object[^>]*>',
            rb'<embed[^>]*>'
        ]
    
    def validate_file_extension(self, filename: str) -> bool:
        """Validate file extension"""
        ext = Path(filename).suffix.lower()
        return ext in self.allowed_extensions
    
    def validate_file_size(self, file_size: int) -> bool:
        """Validate file size"""
        return self.min_file_size <= file_size <= self.max_file_size
    
    def validate_mime_type(self, file_path: str) -> bool:
        """Validate MIME type using python-magic"""
        try:
            mime_type = magic.from_file(file_path, mime=True)
            return mime_type in self.allowed_mime_types
        except Exception as e:
            logger.error(f"Error checking MIME type: {e}")
            return False
    
    def scan_for_malicious_content(self, file_path: str) -> List[str]:
        """Scan file for malicious patterns"""
        threats_found = []
        
        try:
            with open(file_path, 'rb') as f:
                content = f.read()
                
                for pattern in self.malicious_patterns:
                    if re.search(pattern, content, re.IGNORECASE):
                        threats_found.append(f"Malicious pattern detected: {pattern.decode('utf-8', errors='ignore')}")
        
        except Exception as e:
            logger.error(f"Error scanning file content: {e}")
            threats_found.append(f"Error scanning file: {e}")
        
        return threats_found
    
    def virus_scan(self, file_path: str) -> Dict[str, Any]:
        """Scan file for viruses using ClamAV if available"""
        result = {
            'clean': True,
            'scanner': 'none',
            'details': 'No virus scanner available'
        }
        
        # Try ClamAV
        try:
            cmd_result = subprocess.run(
                ['clamscan', '--no-summary', file_path],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if cmd_result.returncode == 0:
                result['clean'] = True
                result['scanner'] = 'clamav'
                result['details'] = 'File is clean'
            else:
                result['clean'] = False
                result['scanner'] = 'clamav'
                result['details'] = cmd_result.stdout
                
        except (subprocess.TimeoutExpired, FileNotFoundError):
            # ClamAV not available, use basic checks
            threats = self.scan_for_malicious_content(file_path)
            if threats:
                result['clean'] = False
                result['scanner'] = 'pattern_matching'
                result['details'] = '; '.join(threats)
            else:
                result['scanner'] = 'pattern_matching'
                result['details'] = 'Basic pattern scan passed'
        
        return result
    
    def calculate_file_hash(self, file_path: str) -> str:
        """Calculate SHA-256 hash of file"""
        sha256_hash = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                sha256_hash.update(chunk)
        return sha256_hash.hexdigest()

class SecureFileUploader:
    """Handles secure file uploads"""
    
    def __init__(self, upload_dir: str = "data/uploads"):
        self.upload_dir = Path(upload_dir)
        self.upload_dir.mkdir(parents=True, exist_ok=True)
        self.quarantine_dir = Path(upload_dir) / "quarantine"
        self.quarantine_dir.mkdir(parents=True, exist_ok=True)
        
        self.validator = FileSecurityValidator()
        self.db_path = "data/file_uploads.db"
        self._init_database()
    
    def _init_database(self):
        """Initialize file upload tracking database"""
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS file_uploads (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    original_filename TEXT,
                    secure_filename TEXT,
                    file_hash TEXT,
                    file_size INTEGER,
                    mime_type TEXT,
                    upload_timestamp TEXT,
                    user_id TEXT,
                    validation_status TEXT,
                    virus_scan_result TEXT,
                    file_path TEXT,
                    quarantined INTEGER DEFAULT 0
                )
            """)
    
    def upload_file(self, file_obj, user_id: str, original_filename: str) -> Dict[str, Any]:
        """Securely upload and validate a file"""
        
        # Generate secure filename
        secure_name = secure_filename(original_filename)
        if not secure_name:
            return {"success": False, "error": "Invalid filename"}
        
        # Add timestamp to prevent conflicts
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        secure_name = f"{timestamp}_{secure_name}"
        
        # Create temporary file for validation
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            file_obj.save(temp_file.name)
            temp_path = temp_file.name
        
        try:
            # Validate file
            validation_result = self._validate_file(temp_path, original_filename)
            
            if not validation_result['valid']:
                # Move to quarantine
                quarantine_path = self.quarantine_dir / secure_name
                os.rename(temp_path, quarantine_path)
                
                # Record in database
                self._record_upload(
                    original_filename, secure_name, "", 0, "", user_id,
                    "REJECTED", validation_result['details'], str(quarantine_path), True
                )
                
                return {
                    "success": False,
                    "error": "File validation failed",
                    "details": validation_result['details']
                }
            
            # File is valid, move to upload directory
            final_path = self.upload_dir / secure_name
            os.rename(temp_path, final_path)
            
            # Calculate file hash and get file info
            file_hash = self.validator.calculate_file_hash(final_path)
            file_size = final_path.stat().st_size
            mime_type = magic.from_file(str(final_path), mime=True)
            
            # Record successful upload
            upload_id = self._record_upload(
                original_filename, secure_name, file_hash, file_size, mime_type,
                user_id, "ACCEPTED", "File validated successfully", str(final_path), False
            )
            
            logger.info(f"File uploaded successfully: {original_filename} -> {secure_name}")
            
            return {
                "success": True,
                "upload_id": upload_id,
                "secure_filename": secure_name,
                "file_path": str(final_path),
                "file_hash": file_hash,
                "file_size": file_size,
                "mime_type": mime_type
            }
            
        except Exception as e:
            # Clean up temp file
            if os.path.exists(temp_path):
                os.unlink(temp_path)
            
            logger.error(f"Error uploading file: {e}")
            return {"success": False, "error": f"Upload failed: {str(e)}"}
    
    def _validate_file(self, file_path: str, original_filename: str) -> Dict[str, Any]:
        """Comprehensive file validation"""
        validation_errors = []
        
        # Check file extension
        if not self.validator.validate_file_extension(original_filename):
            validation_errors.append("File extension not allowed")
        
        # Check file size
        file_size = os.path.getsize(file_path)
        if not self.validator.validate_file_size(file_size):
            validation_errors.append(f"File size {file_size} bytes is not within allowed range")
        
        # Check MIME type
        if not self.validator.validate_mime_type(file_path):
            validation_errors.append("File type not allowed")
        
        # Virus scan
        virus_result = self.validator.virus_scan(file_path)
        if not virus_result['clean']:
            validation_errors.append(f"Virus scan failed: {virus_result['details']}")
        
        return {
            'valid': len(validation_errors) == 0,
            'details': '; '.join(validation_errors) if validation_errors else 'File validation passed',
            'virus_scan': virus_result
        }
    
    def _record_upload(self, original_filename: str, secure_filename: str, file_hash: str,
                      file_size: int, mime_type: str, user_id: str, status: str,
                      details: str, file_path: str, quarantined: bool) -> int:
        """Record file upload in database"""
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                INSERT INTO file_uploads 
                (original_filename, secure_filename, file_hash, file_size, mime_type,
                 upload_timestamp, user_id, validation_status, virus_scan_result,
                 file_path, quarantined)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                original_filename, secure_filename, file_hash, file_size, mime_type,
                datetime.now().isoformat(), user_id, status, details, file_path, quarantined
            ))
            
            return cursor.lastrowid
    
    def get_upload_history(self, user_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Get upload history for user"""
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                SELECT * FROM file_uploads 
                WHERE user_id = ? 
                ORDER BY upload_timestamp DESC 
                LIMIT ?
            """, (user_id, limit))
            
            uploads = []
            for row in cursor.fetchall():
                uploads.append({
                    'id': row[0],
                    'original_filename': row[1],
                    'secure_filename': row[2],
                    'file_hash': row[3],
                    'file_size': row[4],
                    'mime_type': row[5],
                    'upload_timestamp': row[6],
                    'validation_status': row[8],
                    'quarantined': bool(row[11])
                })
            
            return uploads

# Global instance
secure_uploader = SecureFileUploader()

if __name__ == "__main__":
    # Test file upload security
    print("File upload security system initialized")
    print(f"Allowed extensions: {secure_uploader.validator.allowed_extensions}")
    print(f"Max file size: {secure_uploader.validator.max_file_size / 1024 / 1024:.1f} MB")

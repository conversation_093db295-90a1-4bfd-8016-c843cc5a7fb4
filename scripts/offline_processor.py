#!/usr/bin/env python3
"""
Offline Processing Fallback for ROBO-RESEARCHER-2000
Provides local NLP capabilities when AI APIs are unavailable
"""

import os
import re
import json
import logging
import asyncio
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import nltk
import spacy
from textblob import TextBlob
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.cluster import KMeans
from sklearn.decomposition import LatentDirichletAllocation
import numpy as np
from collections import Counter, defaultdict

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Download required NLTK data
try:
    nltk.download('punkt', quiet=True)
    nltk.download('stopwords', quiet=True)
    nltk.download('vader_lexicon', quiet=True)
    nltk.download('wordnet', quiet=True)
except:
    logger.warning("Could not download NLTK data")

@dataclass
class OfflineAnalysisResult:
    """Result from offline analysis"""
    content: str
    confidence: float
    method: str
    metadata: Dict[str, Any]

class LocalNLPProcessor:
    """Local NLP processing using open-source libraries"""
    
    def __init__(self):
        self.nlp = None
        self.sentiment_analyzer = None
        self.tfidf_vectorizer = None
        self._initialize_models()
    
    def _initialize_models(self):
        """Initialize local NLP models"""
        try:
            # Load spaCy model
            self.nlp = spacy.load("en_core_web_sm")
            logger.info("Loaded spaCy model successfully")
        except OSError:
            logger.warning("spaCy model not found. Install with: python -m spacy download en_core_web_sm")
            self.nlp = None
        
        # Initialize TF-IDF vectorizer
        self.tfidf_vectorizer = TfidfVectorizer(
            max_features=1000,
            stop_words='english',
            ngram_range=(1, 2)
        )
        
        # Initialize sentiment analyzer
        try:
            from nltk.sentiment import SentimentIntensityAnalyzer
            self.sentiment_analyzer = SentimentIntensityAnalyzer()
        except:
            logger.warning("NLTK sentiment analyzer not available")
    
    def extract_entities(self, text: str) -> List[Dict[str, Any]]:
        """Extract named entities from text"""
        entities = []
        
        if self.nlp:
            doc = self.nlp(text)
            for ent in doc.ents:
                entities.append({
                    'text': ent.text,
                    'label': ent.label_,
                    'start': ent.start_char,
                    'end': ent.end_char,
                    'confidence': 0.8  # spaCy doesn't provide confidence scores
                })
        else:
            # Fallback: simple regex-based entity extraction
            entities.extend(self._regex_entity_extraction(text))
        
        return entities
    
    def _regex_entity_extraction(self, text: str) -> List[Dict[str, Any]]:
        """Fallback entity extraction using regex patterns"""
        entities = []
        
        # Email pattern
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        for match in re.finditer(email_pattern, text):
            entities.append({
                'text': match.group(),
                'label': 'EMAIL',
                'start': match.start(),
                'end': match.end(),
                'confidence': 0.9
            })
        
        # Phone pattern
        phone_pattern = r'\b\d{3}-\d{3}-\d{4}\b'
        for match in re.finditer(phone_pattern, text):
            entities.append({
                'text': match.group(),
                'label': 'PHONE',
                'start': match.start(),
                'end': match.end(),
                'confidence': 0.9
            })
        
        return entities
    
    def analyze_sentiment(self, text: str) -> Dict[str, float]:
        """Analyze sentiment of text"""
        if self.sentiment_analyzer:
            scores = self.sentiment_analyzer.polarity_scores(text)
            return {
                'positive': scores['pos'],
                'negative': scores['neg'],
                'neutral': scores['neu'],
                'compound': scores['compound']
            }
        else:
            # Fallback: TextBlob sentiment
            blob = TextBlob(text)
            polarity = blob.sentiment.polarity
            return {
                'positive': max(0, polarity),
                'negative': max(0, -polarity),
                'neutral': 1 - abs(polarity),
                'compound': polarity
            }
    
    def extract_keywords(self, text: str, num_keywords: int = 10) -> List[Tuple[str, float]]:
        """Extract keywords using TF-IDF"""
        try:
            # Fit and transform the text
            tfidf_matrix = self.tfidf_vectorizer.fit_transform([text])
            feature_names = self.tfidf_vectorizer.get_feature_names_out()
            tfidf_scores = tfidf_matrix.toarray()[0]
            
            # Get top keywords
            keyword_scores = list(zip(feature_names, tfidf_scores))
            keyword_scores.sort(key=lambda x: x[1], reverse=True)
            
            return keyword_scores[:num_keywords]
        except:
            # Fallback: simple word frequency
            words = re.findall(r'\b\w+\b', text.lower())
            word_freq = Counter(words)
            return [(word, freq/len(words)) for word, freq in word_freq.most_common(num_keywords)]
    
    def cluster_text(self, segments: List[str], num_clusters: int = 5) -> Dict[str, Any]:
        """Cluster text segments"""
        if len(segments) < num_clusters:
            num_clusters = len(segments)
        
        try:
            # Vectorize text
            tfidf_matrix = self.tfidf_vectorizer.fit_transform(segments)
            
            # Perform clustering
            kmeans = KMeans(n_clusters=num_clusters, random_state=42)
            cluster_labels = kmeans.fit_predict(tfidf_matrix)
            
            # Organize results
            clusters = defaultdict(list)
            for i, label in enumerate(cluster_labels):
                clusters[f"cluster_{label}"].append({
                    'text': segments[i],
                    'index': i
                })
            
            return {
                'clusters': dict(clusters),
                'num_clusters': num_clusters,
                'method': 'kmeans'
            }
        except:
            # Fallback: simple grouping by length
            clusters = defaultdict(list)
            for i, segment in enumerate(segments):
                cluster_key = f"cluster_{len(segment) // 100}"
                clusters[cluster_key].append({
                    'text': segment,
                    'index': i
                })
            
            return {
                'clusters': dict(clusters),
                'num_clusters': len(clusters),
                'method': 'length_based'
            }
    
    def topic_modeling(self, segments: List[str], num_topics: int = 5) -> Dict[str, Any]:
        """Perform topic modeling using LDA"""
        try:
            # Vectorize text
            tfidf_matrix = self.tfidf_vectorizer.fit_transform(segments)
            
            # Perform LDA
            lda = LatentDirichletAllocation(n_components=num_topics, random_state=42)
            lda.fit(tfidf_matrix)
            
            # Extract topics
            feature_names = self.tfidf_vectorizer.get_feature_names_out()
            topics = []
            
            for topic_idx, topic in enumerate(lda.components_):
                top_words_idx = topic.argsort()[-10:][::-1]
                top_words = [feature_names[i] for i in top_words_idx]
                topics.append({
                    'id': topic_idx,
                    'words': top_words,
                    'weights': [topic[i] for i in top_words_idx]
                })
            
            return {
                'topics': topics,
                'num_topics': num_topics,
                'method': 'lda'
            }
        except:
            # Fallback: keyword-based topics
            all_keywords = []
            for segment in segments:
                keywords = self.extract_keywords(segment, 5)
                all_keywords.extend([kw[0] for kw in keywords])
            
            keyword_freq = Counter(all_keywords)
            topics = []
            
            for i in range(min(num_topics, len(keyword_freq))):
                topic_words = [word for word, _ in keyword_freq.most_common(10)[i*2:(i+1)*2]]
                topics.append({
                    'id': i,
                    'words': topic_words,
                    'weights': [1.0] * len(topic_words)
                })
            
            return {
                'topics': topics,
                'num_topics': len(topics),
                'method': 'keyword_frequency'
            }

class OfflineProcessor:
    """Main offline processing system"""
    
    def __init__(self):
        self.nlp_processor = LocalNLPProcessor()
        self.templates = self._load_templates()
    
    def _load_templates(self) -> Dict[str, str]:
        """Load response templates for different analysis types"""
        return {
            'emergent_codes': """
Based on the text analysis, I've identified the following emergent themes:

{themes}

These themes were derived from keyword analysis and text clustering. 
Confidence level: {confidence}%
""",
            'patterns': """
Analysis of the text reveals several behavioral patterns:

{patterns}

These patterns were identified through sentiment analysis and topic modeling.
Confidence level: {confidence}%
""",
            'insights': """
Key insights from the analysis:

{insights}

These insights are based on statistical analysis of the text content.
Confidence level: {confidence}%
""",
            'archetypes': """
Based on the analysis, I can identify the following user archetypes:

{archetypes}

These archetypes are derived from clustering analysis and sentiment patterns.
Confidence level: {confidence}%
""",
            'hmw_questions': """
Based on the identified issues and patterns, here are potential "How Might We" questions:

{questions}

These questions target the most frequently mentioned pain points.
Confidence level: {confidence}%
"""
        }
    
    async def process_emergent_coding(self, segments: List[Dict], existing_codes: List[Dict]) -> OfflineAnalysisResult:
        """Generate emergent codes using local NLP"""
        
        # Extract text from segments
        texts = [seg.get('text', '') for seg in segments]
        combined_text = ' '.join(texts)
        
        # Extract keywords and topics
        keywords = self.nlp_processor.extract_keywords(combined_text, 20)
        topics = self.nlp_processor.topic_modeling(texts, 5)
        
        # Generate emergent codes
        emergent_codes = []
        
        # From keywords
        for keyword, score in keywords[:10]:
            if score > 0.1:  # Threshold for relevance
                emergent_codes.append({
                    'name': keyword.replace('_', ' ').title(),
                    'description': f"Theme related to {keyword}",
                    'frequency': int(score * 100),
                    'source': 'keyword_analysis'
                })
        
        # From topics
        for topic in topics['topics']:
            if len(topic['words']) > 0:
                topic_name = ' '.join(topic['words'][:3]).title()
                emergent_codes.append({
                    'name': f"Topic: {topic_name}",
                    'description': f"Theme encompassing: {', '.join(topic['words'][:5])}",
                    'frequency': int(sum(topic['weights'][:5]) * 10),
                    'source': 'topic_modeling'
                })
        
        # Format response
        themes_text = '\n'.join([
            f"- {code['name']}: {code['description']} (Frequency: {code['frequency']})"
            for code in emergent_codes[:8]
        ])
        
        response = self.templates['emergent_codes'].format(
            themes=themes_text,
            confidence=75
        )
        
        return OfflineAnalysisResult(
            content=json.dumps(emergent_codes),
            confidence=0.75,
            method='local_nlp',
            metadata={
                'keywords_found': len(keywords),
                'topics_found': len(topics['topics']),
                'codes_generated': len(emergent_codes)
            }
        )
    
    async def process_pattern_detection(self, data: Dict) -> OfflineAnalysisResult:
        """Detect patterns using local analysis"""
        
        segments = data.get('codedSegments', [])
        quantitative = data.get('quantitativeAnalysis', {})
        
        patterns = []
        
        # Sentiment patterns
        sentiments = []
        for segment in segments:
            text = segment.get('text', '')
            sentiment = self.nlp_processor.analyze_sentiment(text)
            sentiments.append(sentiment)
        
        # Calculate sentiment trends
        avg_sentiment = np.mean([s['compound'] for s in sentiments])
        if avg_sentiment > 0.1:
            patterns.append({
                'name': 'Positive Sentiment Trend',
                'description': 'Overall positive sentiment detected in user feedback',
                'frequency': 'high',
                'impact': 'medium',
                'evidence': f'Average sentiment score: {avg_sentiment:.2f}'
            })
        elif avg_sentiment < -0.1:
            patterns.append({
                'name': 'Negative Sentiment Trend',
                'description': 'Overall negative sentiment detected in user feedback',
                'frequency': 'high',
                'impact': 'high',
                'evidence': f'Average sentiment score: {avg_sentiment:.2f}'
            })
        
        # Code frequency patterns
        if quantitative.get('frequencyAnalysis'):
            code_freq = quantitative['frequencyAnalysis'].get('codeFrequency', {})
            if code_freq:
                most_common = max(code_freq.items(), key=lambda x: x[1])
                patterns.append({
                    'name': 'Dominant Issue Pattern',
                    'description': f'Recurring mentions of {most_common[0]}',
                    'frequency': 'high',
                    'impact': 'high',
                    'evidence': f'Mentioned {most_common[1]} times'
                })
        
        # Text clustering patterns
        texts = [seg.get('text', '') for seg in segments]
        if texts:
            clusters = self.nlp_processor.cluster_text(texts, 3)
            if len(clusters['clusters']) > 1:
                patterns.append({
                    'name': 'User Segment Diversity',
                    'description': 'Multiple distinct user behavior patterns identified',
                    'frequency': 'medium',
                    'impact': 'medium',
                    'evidence': f'{len(clusters["clusters"])} distinct clusters found'
                })
        
        patterns_text = '\n'.join([
            f"- {p['name']}: {p['description']} (Impact: {p['impact']})"
            for p in patterns
        ])
        
        response = self.templates['patterns'].format(
            patterns=patterns_text,
            confidence=70
        )
        
        return OfflineAnalysisResult(
            content=json.dumps(patterns),
            confidence=0.70,
            method='local_analysis',
            metadata={
                'patterns_found': len(patterns),
                'sentiment_analyzed': len(sentiments),
                'clusters_found': len(clusters.get('clusters', {}))
            }
        )
    
    async def process_insight_generation(self, data: Dict) -> OfflineAnalysisResult:
        """Generate insights using local analysis"""
        
        patterns = data.get('detectedPatterns', [])
        quantitative = data.get('quantitativeAnalysis', {})
        
        insights = {
            'keyFindings': [],
            'userPainPoints': [],
            'opportunities': [],
            'recommendations': []
        }
        
        # Generate key findings from patterns
        for pattern in patterns[:3]:
            insights['keyFindings'].append({
                'title': pattern.get('name', 'Key Finding'),
                'description': pattern.get('description', ''),
                'impact': pattern.get('impact', 'medium'),
                'evidence': pattern.get('evidence', '')
            })
        
        # Generate pain points from negative sentiment
        if quantitative.get('frequencyAnalysis', {}).get('sentimentDistribution'):
            sentiment_dist = quantitative['frequencyAnalysis']['sentimentDistribution']
            if sentiment_dist.get('negative', 0) > sentiment_dist.get('positive', 0):
                insights['userPainPoints'].append({
                    'description': 'Users express more negative than positive sentiment',
                    'severity': 'high',
                    'frequency': sentiment_dist['negative'],
                    'category': 'emotional_response'
                })
        
        # Generate opportunities
        insights['opportunities'].append({
            'title': 'Improve User Experience',
            'description': 'Address the most frequently mentioned issues',
            'impact': 'high',
            'effort': 'medium'
        })
        
        # Generate recommendations
        insights['recommendations'].append({
            'title': 'Focus on Top Pain Points',
            'description': 'Prioritize addressing the most common user complaints',
            'priority': 'high',
            'timeframe': 'short-term'
        })
        
        insights_text = '\n'.join([
            f"- {finding['title']}: {finding['description']}"
            for finding in insights['keyFindings']
        ])
        
        response = self.templates['insights'].format(
            insights=insights_text,
            confidence=65
        )
        
        return OfflineAnalysisResult(
            content=json.dumps(insights),
            confidence=0.65,
            method='statistical_analysis',
            metadata={
                'findings_generated': len(insights['keyFindings']),
                'pain_points_identified': len(insights['userPainPoints']),
                'opportunities_found': len(insights['opportunities'])
            }
        )
    
    def is_available(self) -> bool:
        """Check if offline processing is available"""
        return self.nlp_processor is not None

# Global instance
offline_processor = OfflineProcessor()

if __name__ == "__main__":
    # Test offline processor
    async def test():
        test_segments = [
            {'text': 'The interface is confusing and hard to navigate'},
            {'text': 'I love the new features but the app is slow'},
            {'text': 'Customer support was very helpful and responsive'}
        ]
        
        result = await offline_processor.process_emergent_coding(test_segments, [])
        print(f"Emergent coding result: {result}")
        
        test_data = {
            'codedSegments': test_segments,
            'quantitativeAnalysis': {
                'frequencyAnalysis': {
                    'codeFrequency': {'usability': 5, 'performance': 3},
                    'sentimentDistribution': {'positive': 1, 'negative': 2, 'neutral': 0}
                }
            }
        }
        
        patterns = await offline_processor.process_pattern_detection(test_data)
        print(f"Pattern detection result: {patterns}")
    
    asyncio.run(test())

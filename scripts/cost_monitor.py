#!/usr/bin/env python3
"""
Cost Monitoring and Alerts System for ROBO-RESEARCHER-2000
Tracks AI API usage costs and provides budget management
"""

import os
import json
import time
import smtplib
import logging
import asyncio
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart
import sqlite3
import threading
from collections import defaultdict

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class CostAlert:
    """Cost alert configuration"""
    alert_id: str
    user_id: str
    alert_type: str  # 'budget_threshold', 'daily_limit', 'unusual_usage'
    threshold: float
    period: str  # 'daily', 'weekly', 'monthly'
    enabled: bool = True
    last_triggered: Optional[datetime] = None

@dataclass
class UsageCost:
    """Cost tracking record"""
    timestamp: datetime
    user_id: str
    project_id: str
    provider: str
    model: str
    tokens_used: int
    cost: float
    request_type: str  # 'coding', 'pattern_detection', 'insight_generation', etc.

class CostDatabase:
    """Database for cost tracking"""
    
    def __init__(self, db_path: str = "data/costs.db"):
        self.db_path = db_path
        self.lock = threading.Lock()
        self._init_database()
    
    def _init_database(self):
        """Initialize cost tracking database"""
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS cost_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT,
                    user_id TEXT,
                    project_id TEXT,
                    provider TEXT,
                    model TEXT,
                    tokens_used INTEGER,
                    cost REAL,
                    request_type TEXT
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS cost_alerts (
                    alert_id TEXT PRIMARY KEY,
                    user_id TEXT,
                    alert_type TEXT,
                    threshold REAL,
                    period TEXT,
                    enabled INTEGER,
                    last_triggered TEXT
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS budget_limits (
                    user_id TEXT PRIMARY KEY,
                    daily_limit REAL,
                    weekly_limit REAL,
                    monthly_limit REAL,
                    alert_threshold REAL
                )
            """)
            
            # Create indexes
            conn.execute("CREATE INDEX IF NOT EXISTS idx_cost_timestamp ON cost_records(timestamp)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_cost_user ON cost_records(user_id, timestamp)")
    
    def record_cost(self, cost_record: UsageCost):
        """Record a cost entry"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT INTO cost_records 
                (timestamp, user_id, project_id, provider, model, tokens_used, cost, request_type)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                cost_record.timestamp.isoformat(),
                cost_record.user_id,
                cost_record.project_id,
                cost_record.provider,
                cost_record.model,
                cost_record.tokens_used,
                cost_record.cost,
                cost_record.request_type
            ))
    
    def get_cost_summary(self, user_id: str, days: int = 30) -> Dict[str, Any]:
        """Get cost summary for user"""
        since = datetime.now() - timedelta(days=days)
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                SELECT 
                    SUM(cost) as total_cost,
                    SUM(tokens_used) as total_tokens,
                    COUNT(*) as total_requests,
                    provider,
                    model,
                    request_type
                FROM cost_records 
                WHERE user_id = ? AND timestamp >= ?
                GROUP BY provider, model, request_type
            """, (user_id, since.isoformat()))
            
            records = cursor.fetchall()
            
            # Overall summary
            cursor = conn.execute("""
                SELECT 
                    SUM(cost) as total_cost,
                    SUM(tokens_used) as total_tokens,
                    COUNT(*) as total_requests
                FROM cost_records 
                WHERE user_id = ? AND timestamp >= ?
            """, (user_id, since.isoformat()))
            
            overall = cursor.fetchone()
            
            return {
                'total_cost': overall[0] or 0.0,
                'total_tokens': overall[1] or 0,
                'total_requests': overall[2] or 0,
                'breakdown': [
                    {
                        'provider': record[3],
                        'model': record[4],
                        'request_type': record[5],
                        'cost': record[0],
                        'tokens': record[1],
                        'requests': record[2]
                    }
                    for record in records
                ]
            }
    
    def get_daily_costs(self, user_id: str, days: int = 7) -> List[Dict[str, Any]]:
        """Get daily cost breakdown"""
        since = datetime.now() - timedelta(days=days)
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                SELECT 
                    DATE(timestamp) as date,
                    SUM(cost) as daily_cost,
                    SUM(tokens_used) as daily_tokens,
                    COUNT(*) as daily_requests
                FROM cost_records 
                WHERE user_id = ? AND timestamp >= ?
                GROUP BY DATE(timestamp)
                ORDER BY date
            """, (user_id, since.isoformat()))
            
            return [
                {
                    'date': record[0],
                    'cost': record[1],
                    'tokens': record[2],
                    'requests': record[3]
                }
                for record in cursor.fetchall()
            ]
    
    def set_budget_limits(self, user_id: str, daily: float = None, 
                         weekly: float = None, monthly: float = None, 
                         alert_threshold: float = 0.8):
        """Set budget limits for user"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT OR REPLACE INTO budget_limits 
                (user_id, daily_limit, weekly_limit, monthly_limit, alert_threshold)
                VALUES (?, ?, ?, ?, ?)
            """, (user_id, daily, weekly, monthly, alert_threshold))
    
    def get_budget_limits(self, user_id: str) -> Dict[str, float]:
        """Get budget limits for user"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute(
                "SELECT daily_limit, weekly_limit, monthly_limit, alert_threshold FROM budget_limits WHERE user_id = ?",
                (user_id,)
            )
            row = cursor.fetchone()
            
            if row:
                return {
                    'daily_limit': row[0],
                    'weekly_limit': row[1],
                    'monthly_limit': row[2],
                    'alert_threshold': row[3]
                }
            else:
                return {
                    'daily_limit': 5.0,
                    'weekly_limit': 25.0,
                    'monthly_limit': 100.0,
                    'alert_threshold': 0.8
                }

class AlertManager:
    """Manages cost alerts and notifications"""
    
    def __init__(self, cost_db: CostDatabase):
        self.cost_db = cost_db
        self.smtp_config = self._load_smtp_config()
    
    def _load_smtp_config(self) -> Dict[str, str]:
        """Load SMTP configuration from environment"""
        return {
            'host': os.getenv('SMTP_HOST', 'localhost'),
            'port': int(os.getenv('SMTP_PORT', '587')),
            'username': os.getenv('SMTP_USERNAME', ''),
            'password': os.getenv('SMTP_PASSWORD', ''),
            'from_email': os.getenv('SMTP_FROM_EMAIL', '<EMAIL>')
        }
    
    async def check_budget_alerts(self, user_id: str, user_email: str) -> List[str]:
        """Check if user has exceeded budget thresholds"""
        alerts_triggered = []
        
        budget_limits = self.cost_db.get_budget_limits(user_id)
        
        # Check daily limit
        if budget_limits['daily_limit']:
            daily_costs = self.cost_db.get_daily_costs(user_id, 1)
            if daily_costs:
                today_cost = daily_costs[0]['cost']
                threshold_cost = budget_limits['daily_limit'] * budget_limits['alert_threshold']
                
                if today_cost >= threshold_cost:
                    alert_msg = f"Daily budget alert: ${today_cost:.2f} of ${budget_limits['daily_limit']:.2f} used"
                    alerts_triggered.append(alert_msg)
                    await self._send_alert_email(user_email, "Daily Budget Alert", alert_msg)
        
        # Check weekly limit
        if budget_limits['weekly_limit']:
            weekly_summary = self.cost_db.get_cost_summary(user_id, 7)
            weekly_cost = weekly_summary['total_cost']
            threshold_cost = budget_limits['weekly_limit'] * budget_limits['alert_threshold']
            
            if weekly_cost >= threshold_cost:
                alert_msg = f"Weekly budget alert: ${weekly_cost:.2f} of ${budget_limits['weekly_limit']:.2f} used"
                alerts_triggered.append(alert_msg)
                await self._send_alert_email(user_email, "Weekly Budget Alert", alert_msg)
        
        # Check monthly limit
        if budget_limits['monthly_limit']:
            monthly_summary = self.cost_db.get_cost_summary(user_id, 30)
            monthly_cost = monthly_summary['total_cost']
            threshold_cost = budget_limits['monthly_limit'] * budget_limits['alert_threshold']
            
            if monthly_cost >= threshold_cost:
                alert_msg = f"Monthly budget alert: ${monthly_cost:.2f} of ${budget_limits['monthly_limit']:.2f} used"
                alerts_triggered.append(alert_msg)
                await self._send_alert_email(user_email, "Monthly Budget Alert", alert_msg)
        
        return alerts_triggered
    
    async def check_unusual_usage(self, user_id: str, user_email: str) -> List[str]:
        """Check for unusual usage patterns"""
        alerts = []
        
        # Get recent usage
        recent_costs = self.cost_db.get_daily_costs(user_id, 7)
        if len(recent_costs) < 2:
            return alerts
        
        # Calculate average daily cost
        avg_daily_cost = sum(day['cost'] for day in recent_costs[:-1]) / (len(recent_costs) - 1)
        today_cost = recent_costs[-1]['cost']
        
        # Alert if today's cost is 3x average
        if today_cost > avg_daily_cost * 3 and avg_daily_cost > 0:
            alert_msg = f"Unusual usage detected: Today's cost ${today_cost:.2f} is {today_cost/avg_daily_cost:.1f}x your average"
            alerts.append(alert_msg)
            await self._send_alert_email(user_email, "Unusual Usage Alert", alert_msg)
        
        return alerts
    
    async def _send_alert_email(self, to_email: str, subject: str, message: str):
        """Send alert email"""
        if not self.smtp_config['username']:
            logger.warning("SMTP not configured, skipping email alert")
            return
        
        try:
            msg = MimeMultipart()
            msg['From'] = self.smtp_config['from_email']
            msg['To'] = to_email
            msg['Subject'] = f"ROBO-RESEARCHER-2000: {subject}"
            
            body = f"""
            <h2>Cost Alert - ROBO-RESEARCHER-2000</h2>
            <p>{message}</p>
            <p>Please review your usage and adjust your settings if necessary.</p>
            <p>You can view detailed cost breakdowns in your dashboard.</p>
            <hr>
            <p><small>This is an automated alert from ROBO-RESEARCHER-2000</small></p>
            """
            
            msg.attach(MimeText(body, 'html'))
            
            server = smtplib.SMTP(self.smtp_config['host'], self.smtp_config['port'])
            server.starttls()
            server.login(self.smtp_config['username'], self.smtp_config['password'])
            server.send_message(msg)
            server.quit()
            
            logger.info(f"Alert email sent to {to_email}")
            
        except Exception as e:
            logger.error(f"Failed to send alert email: {e}")

class CostMonitor:
    """Main cost monitoring system"""
    
    def __init__(self):
        self.cost_db = CostDatabase()
        self.alert_manager = AlertManager(self.cost_db)
        self.pricing_models = self._load_pricing_models()
    
    def _load_pricing_models(self) -> Dict[str, Dict[str, float]]:
        """Load pricing models for different providers"""
        return {
            'openrouter': {
                'anthropic/claude-3-sonnet': 0.003,
                'openai/gpt-4o-mini': 0.0015,
                'google/gemini-pro': 0.0005
            },
            'openai': {
                'gpt-4o-mini': 0.0015,
                'gpt-3.5-turbo': 0.001
            },
            'anthropic': {
                'claude-3-sonnet-20240229': 0.003,
                'claude-3-haiku-20240307': 0.00025
            },
            'google': {
                'gemini-pro': 0.0005
            }
        }
    
    def calculate_cost(self, provider: str, model: str, tokens: int) -> float:
        """Calculate cost for API usage"""
        provider_pricing = self.pricing_models.get(provider, {})
        cost_per_1k = provider_pricing.get(model, 0.001)  # Default fallback
        return (tokens / 1000) * cost_per_1k
    
    async def record_usage(self, user_id: str, project_id: str, provider: str, 
                          model: str, tokens_used: int, request_type: str) -> float:
        """Record usage and return cost"""
        
        cost = self.calculate_cost(provider, model, tokens_used)
        
        cost_record = UsageCost(
            timestamp=datetime.now(),
            user_id=user_id,
            project_id=project_id,
            provider=provider,
            model=model,
            tokens_used=tokens_used,
            cost=cost,
            request_type=request_type
        )
        
        self.cost_db.record_cost(cost_record)
        
        # Check for alerts (async)
        asyncio.create_task(self._check_alerts(user_id))
        
        return cost
    
    async def _check_alerts(self, user_id: str):
        """Check and trigger alerts for user"""
        try:
            # Get user email (would need to be passed or looked up)
            user_email = f"{user_id}@example.com"  # Placeholder
            
            budget_alerts = await self.alert_manager.check_budget_alerts(user_id, user_email)
            usage_alerts = await self.alert_manager.check_unusual_usage(user_id, user_email)
            
            if budget_alerts or usage_alerts:
                logger.info(f"Alerts triggered for user {user_id}: {budget_alerts + usage_alerts}")
                
        except Exception as e:
            logger.error(f"Error checking alerts for user {user_id}: {e}")
    
    def get_cost_dashboard(self, user_id: str) -> Dict[str, Any]:
        """Get comprehensive cost dashboard data"""
        
        # Current usage
        daily_summary = self.cost_db.get_cost_summary(user_id, 1)
        weekly_summary = self.cost_db.get_cost_summary(user_id, 7)
        monthly_summary = self.cost_db.get_cost_summary(user_id, 30)
        
        # Budget limits
        budget_limits = self.cost_db.get_budget_limits(user_id)
        
        # Daily breakdown
        daily_costs = self.cost_db.get_daily_costs(user_id, 30)
        
        # Calculate budget utilization
        budget_utilization = {}
        if budget_limits['daily_limit']:
            budget_utilization['daily'] = (daily_summary['total_cost'] / budget_limits['daily_limit']) * 100
        if budget_limits['weekly_limit']:
            budget_utilization['weekly'] = (weekly_summary['total_cost'] / budget_limits['weekly_limit']) * 100
        if budget_limits['monthly_limit']:
            budget_utilization['monthly'] = (monthly_summary['total_cost'] / budget_limits['monthly_limit']) * 100
        
        return {
            'current_usage': {
                'daily': daily_summary,
                'weekly': weekly_summary,
                'monthly': monthly_summary
            },
            'budget_limits': budget_limits,
            'budget_utilization': budget_utilization,
            'daily_breakdown': daily_costs,
            'cost_trends': self._calculate_trends(daily_costs),
            'top_cost_drivers': self._get_top_cost_drivers(monthly_summary)
        }
    
    def _calculate_trends(self, daily_costs: List[Dict]) -> Dict[str, Any]:
        """Calculate cost trends"""
        if len(daily_costs) < 2:
            return {'trend': 'insufficient_data'}
        
        recent_avg = sum(day['cost'] for day in daily_costs[-3:]) / min(3, len(daily_costs))
        older_avg = sum(day['cost'] for day in daily_costs[-7:-3]) / max(1, len(daily_costs[-7:-3]))
        
        if older_avg == 0:
            trend = 'new_user'
        elif recent_avg > older_avg * 1.2:
            trend = 'increasing'
        elif recent_avg < older_avg * 0.8:
            trend = 'decreasing'
        else:
            trend = 'stable'
        
        return {
            'trend': trend,
            'recent_average': recent_avg,
            'change_percentage': ((recent_avg - older_avg) / max(older_avg, 0.01)) * 100
        }
    
    def _get_top_cost_drivers(self, monthly_summary: Dict) -> List[Dict]:
        """Get top cost drivers"""
        breakdown = monthly_summary.get('breakdown', [])
        sorted_breakdown = sorted(breakdown, key=lambda x: x['cost'], reverse=True)
        return sorted_breakdown[:5]
    
    def set_user_budget(self, user_id: str, daily: float = None, 
                       weekly: float = None, monthly: float = None):
        """Set budget limits for user"""
        self.cost_db.set_budget_limits(user_id, daily, weekly, monthly)
        logger.info(f"Updated budget limits for user {user_id}")

# Global instance
cost_monitor = CostMonitor()

if __name__ == "__main__":
    # Test cost monitoring
    async def test():
        # Record some test usage
        cost = await cost_monitor.record_usage(
            "test_user", "test_project", "openrouter", 
            "anthropic/claude-3-sonnet", 1000, "coding"
        )
        print(f"Recorded cost: ${cost:.4f}")
        
        # Set budget
        cost_monitor.set_user_budget("test_user", daily=5.0, monthly=50.0)
        
        # Get dashboard
        dashboard = cost_monitor.get_cost_dashboard("test_user")
        print(f"Dashboard: {json.dumps(dashboard, indent=2, default=str)}")
    
    asyncio.run(test())

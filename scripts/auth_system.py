#!/usr/bin/env python3
"""
Authentication System for ROBO-RESEARCHER-2000
Provides secure user authentication, session management, and role-based access control
"""

import os
import jwt
import bcrypt
import secrets
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import sqlite3
import threading
from functools import wraps
from flask import request, jsonify, session

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class User:
    """User data model"""
    user_id: str
    username: str
    email: str
    password_hash: str
    role: str = "user"  # user, admin, enterprise
    created_at: datetime = None
    last_login: datetime = None
    is_active: bool = True
    email_verified: bool = False
    
@dataclass
class Session:
    """Session data model"""
    session_id: str
    user_id: str
    created_at: datetime
    expires_at: datetime
    ip_address: str
    user_agent: str
    is_active: bool = True

class AuthDatabase:
    """Database for authentication data"""
    
    def __init__(self, db_path: str = "data/auth.db"):
        self.db_path = db_path
        self.lock = threading.Lock()
        self._init_database()
    
    def _init_database(self):
        """Initialize authentication database"""
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS users (
                    user_id TEXT PRIMARY KEY,
                    username TEXT UNIQUE NOT NULL,
                    email TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    role TEXT DEFAULT 'user',
                    created_at TEXT,
                    last_login TEXT,
                    is_active INTEGER DEFAULT 1,
                    email_verified INTEGER DEFAULT 0
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS sessions (
                    session_id TEXT PRIMARY KEY,
                    user_id TEXT,
                    created_at TEXT,
                    expires_at TEXT,
                    ip_address TEXT,
                    user_agent TEXT,
                    is_active INTEGER DEFAULT 1,
                    FOREIGN KEY (user_id) REFERENCES users (user_id)
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS login_attempts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT,
                    ip_address TEXT,
                    success INTEGER,
                    timestamp TEXT
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS password_resets (
                    token TEXT PRIMARY KEY,
                    user_id TEXT,
                    created_at TEXT,
                    expires_at TEXT,
                    used INTEGER DEFAULT 0,
                    FOREIGN KEY (user_id) REFERENCES users (user_id)
                )
            """)
            
            # Create indexes
            conn.execute("CREATE INDEX IF NOT EXISTS idx_sessions_user ON sessions(user_id)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_login_attempts_ip ON login_attempts(ip_address, timestamp)")
    
    def create_user(self, user: User) -> bool:
        """Create a new user"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT INTO users 
                    (user_id, username, email, password_hash, role, created_at, is_active, email_verified)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    user.user_id, user.username, user.email, user.password_hash,
                    user.role, user.created_at.isoformat(), user.is_active, user.email_verified
                ))
                return True
        except sqlite3.IntegrityError:
            return False
    
    def get_user_by_username(self, username: str) -> Optional[User]:
        """Get user by username"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute(
                "SELECT * FROM users WHERE username = ? AND is_active = 1",
                (username,)
            )
            row = cursor.fetchone()
            
            if row:
                return User(
                    user_id=row[0],
                    username=row[1],
                    email=row[2],
                    password_hash=row[3],
                    role=row[4],
                    created_at=datetime.fromisoformat(row[5]) if row[5] else None,
                    last_login=datetime.fromisoformat(row[6]) if row[6] else None,
                    is_active=bool(row[7]),
                    email_verified=bool(row[8])
                )
            return None
    
    def get_user_by_id(self, user_id: str) -> Optional[User]:
        """Get user by ID"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute(
                "SELECT * FROM users WHERE user_id = ? AND is_active = 1",
                (user_id,)
            )
            row = cursor.fetchone()
            
            if row:
                return User(
                    user_id=row[0],
                    username=row[1],
                    email=row[2],
                    password_hash=row[3],
                    role=row[4],
                    created_at=datetime.fromisoformat(row[5]) if row[5] else None,
                    last_login=datetime.fromisoformat(row[6]) if row[6] else None,
                    is_active=bool(row[7]),
                    email_verified=bool(row[8])
                )
            return None
    
    def update_last_login(self, user_id: str):
        """Update user's last login timestamp"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute(
                "UPDATE users SET last_login = ? WHERE user_id = ?",
                (datetime.now().isoformat(), user_id)
            )
    
    def create_session(self, session: Session) -> bool:
        """Create a new session"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT INTO sessions 
                    (session_id, user_id, created_at, expires_at, ip_address, user_agent, is_active)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    session.session_id, session.user_id, session.created_at.isoformat(),
                    session.expires_at.isoformat(), session.ip_address, session.user_agent, session.is_active
                ))
                return True
        except sqlite3.Error:
            return False
    
    def get_session(self, session_id: str) -> Optional[Session]:
        """Get session by ID"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute(
                "SELECT * FROM sessions WHERE session_id = ? AND is_active = 1",
                (session_id,)
            )
            row = cursor.fetchone()
            
            if row:
                return Session(
                    session_id=row[0],
                    user_id=row[1],
                    created_at=datetime.fromisoformat(row[2]),
                    expires_at=datetime.fromisoformat(row[3]),
                    ip_address=row[4],
                    user_agent=row[5],
                    is_active=bool(row[6])
                )
            return None
    
    def invalidate_session(self, session_id: str):
        """Invalidate a session"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute(
                "UPDATE sessions SET is_active = 0 WHERE session_id = ?",
                (session_id,)
            )
    
    def record_login_attempt(self, username: str, ip_address: str, success: bool):
        """Record login attempt"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT INTO login_attempts (username, ip_address, success, timestamp)
                VALUES (?, ?, ?, ?)
            """, (username, ip_address, success, datetime.now().isoformat()))
    
    def get_failed_attempts(self, ip_address: str, minutes: int = 15) -> int:
        """Get number of failed attempts from IP in last N minutes"""
        since = datetime.now() - timedelta(minutes=minutes)
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                SELECT COUNT(*) FROM login_attempts 
                WHERE ip_address = ? AND success = 0 AND timestamp >= ?
            """, (ip_address, since.isoformat()))
            
            return cursor.fetchone()[0]

class AuthManager:
    """Main authentication manager"""
    
    def __init__(self):
        self.db = AuthDatabase()
        self.jwt_secret = os.getenv('JWT_SECRET', secrets.token_urlsafe(32))
        self.session_timeout = timedelta(hours=24)
        self.max_failed_attempts = 5
        
        # Create default admin user if none exists
        self._create_default_admin()
    
    def _create_default_admin(self):
        """Create default admin user"""
        admin_user = self.db.get_user_by_username("admin")
        if not admin_user:
            admin_password = os.getenv('ADMIN_PASSWORD', 'robo-researcher-2000')
            self.register_user("admin", "<EMAIL>", admin_password, "admin")
            logger.info("Created default admin user")
    
    def hash_password(self, password: str) -> str:
        """Hash password using bcrypt"""
        salt = bcrypt.gensalt()
        return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')
    
    def verify_password(self, password: str, password_hash: str) -> bool:
        """Verify password against hash"""
        return bcrypt.checkpw(password.encode('utf-8'), password_hash.encode('utf-8'))
    
    def register_user(self, username: str, email: str, password: str, role: str = "user") -> Dict[str, Any]:
        """Register a new user"""
        
        # Validate input
        if len(username) < 3 or len(password) < 8:
            return {"success": False, "error": "Username must be 3+ chars, password 8+ chars"}
        
        if "@" not in email:
            return {"success": False, "error": "Invalid email format"}
        
        # Check if user exists
        if self.db.get_user_by_username(username):
            return {"success": False, "error": "Username already exists"}
        
        # Create user
        user_id = secrets.token_urlsafe(16)
        password_hash = self.hash_password(password)
        
        user = User(
            user_id=user_id,
            username=username,
            email=email,
            password_hash=password_hash,
            role=role,
            created_at=datetime.now()
        )
        
        if self.db.create_user(user):
            logger.info(f"User registered: {username}")
            return {"success": True, "user_id": user_id}
        else:
            return {"success": False, "error": "Failed to create user"}
    
    def authenticate_user(self, username: str, password: str, ip_address: str) -> Dict[str, Any]:
        """Authenticate user and create session"""
        
        # Check for too many failed attempts
        failed_attempts = self.db.get_failed_attempts(ip_address)
        if failed_attempts >= self.max_failed_attempts:
            return {"success": False, "error": "Too many failed attempts. Try again later."}
        
        # Get user
        user = self.db.get_user_by_username(username)
        if not user:
            self.db.record_login_attempt(username, ip_address, False)
            return {"success": False, "error": "Invalid credentials"}
        
        # Verify password
        if not self.verify_password(password, user.password_hash):
            self.db.record_login_attempt(username, ip_address, False)
            return {"success": False, "error": "Invalid credentials"}
        
        # Create session
        session_id = secrets.token_urlsafe(32)
        session = Session(
            session_id=session_id,
            user_id=user.user_id,
            created_at=datetime.now(),
            expires_at=datetime.now() + self.session_timeout,
            ip_address=ip_address,
            user_agent=request.headers.get('User-Agent', '') if request else ''
        )
        
        if self.db.create_session(session):
            self.db.update_last_login(user.user_id)
            self.db.record_login_attempt(username, ip_address, True)
            
            # Create JWT token
            token = self.create_jwt_token(user.user_id, session_id)
            
            logger.info(f"User authenticated: {username}")
            return {
                "success": True,
                "token": token,
                "session_id": session_id,
                "user": {
                    "user_id": user.user_id,
                    "username": user.username,
                    "email": user.email,
                    "role": user.role
                }
            }
        else:
            return {"success": False, "error": "Failed to create session"}
    
    def create_jwt_token(self, user_id: str, session_id: str) -> str:
        """Create JWT token"""
        payload = {
            'user_id': user_id,
            'session_id': session_id,
            'exp': datetime.utcnow() + self.session_timeout,
            'iat': datetime.utcnow()
        }
        return jwt.encode(payload, self.jwt_secret, algorithm='HS256')
    
    def verify_jwt_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Verify JWT token"""
        try:
            payload = jwt.decode(token, self.jwt_secret, algorithms=['HS256'])
            
            # Check if session is still valid
            session = self.db.get_session(payload['session_id'])
            if not session or not session.is_active or session.expires_at < datetime.now():
                return None
            
            return payload
        except jwt.ExpiredSignatureError:
            return None
        except jwt.InvalidTokenError:
            return None
    
    def logout_user(self, session_id: str) -> bool:
        """Logout user by invalidating session"""
        self.db.invalidate_session(session_id)
        return True
    
    def require_auth(self, required_role: str = None):
        """Decorator for requiring authentication"""
        def decorator(f):
            @wraps(f)
            def decorated_function(*args, **kwargs):
                # Get token from header
                auth_header = request.headers.get('Authorization')
                if not auth_header or not auth_header.startswith('Bearer '):
                    return jsonify({'error': 'Authentication required'}), 401
                
                token = auth_header.split(' ')[1]
                payload = self.verify_jwt_token(token)
                
                if not payload:
                    return jsonify({'error': 'Invalid or expired token'}), 401
                
                # Get user
                user = self.db.get_user_by_id(payload['user_id'])
                if not user:
                    return jsonify({'error': 'User not found'}), 401
                
                # Check role if required
                if required_role:
                    role_hierarchy = {'user': 1, 'admin': 2, 'enterprise': 3}
                    user_level = role_hierarchy.get(user.role, 0)
                    required_level = role_hierarchy.get(required_role, 999)
                    
                    if user_level < required_level:
                        return jsonify({'error': 'Insufficient permissions'}), 403
                
                # Add user to request context
                request.current_user = user
                return f(*args, **kwargs)
            
            return decorated_function
        return decorator
    
    def get_user_sessions(self, user_id: str) -> List[Dict[str, Any]]:
        """Get active sessions for user"""
        with sqlite3.connect(self.db.db_path) as conn:
            cursor = conn.execute("""
                SELECT session_id, created_at, expires_at, ip_address, user_agent
                FROM sessions 
                WHERE user_id = ? AND is_active = 1 AND expires_at > ?
                ORDER BY created_at DESC
            """, (user_id, datetime.now().isoformat()))
            
            sessions = []
            for row in cursor.fetchall():
                sessions.append({
                    'session_id': row[0],
                    'created_at': row[1],
                    'expires_at': row[2],
                    'ip_address': row[3],
                    'user_agent': row[4]
                })
            
            return sessions
    
    def change_password(self, user_id: str, old_password: str, new_password: str) -> Dict[str, Any]:
        """Change user password"""
        user = self.db.get_user_by_id(user_id)
        if not user:
            return {"success": False, "error": "User not found"}
        
        if not self.verify_password(old_password, user.password_hash):
            return {"success": False, "error": "Current password is incorrect"}
        
        if len(new_password) < 8:
            return {"success": False, "error": "New password must be at least 8 characters"}
        
        new_hash = self.hash_password(new_password)
        
        with sqlite3.connect(self.db.db_path) as conn:
            conn.execute(
                "UPDATE users SET password_hash = ? WHERE user_id = ?",
                (new_hash, user_id)
            )
        
        logger.info(f"Password changed for user: {user.username}")
        return {"success": True}

# Global instance
auth_manager = AuthManager()

if __name__ == "__main__":
    # Test authentication system
    def test():
        # Register test user
        result = auth_manager.register_user("testuser", "<EMAIL>", "testpassword123")
        print(f"Registration: {result}")
        
        # Authenticate user
        auth_result = auth_manager.authenticate_user("testuser", "testpassword123", "127.0.0.1")
        print(f"Authentication: {auth_result}")
        
        if auth_result['success']:
            # Verify token
            token_payload = auth_manager.verify_jwt_token(auth_result['token'])
            print(f"Token verification: {token_payload}")
            
            # Get sessions
            sessions = auth_manager.get_user_sessions(auth_result['user']['user_id'])
            print(f"User sessions: {sessions}")
    
    test()

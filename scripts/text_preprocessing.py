"""
ROBO-RESEARCHER-2000 Text Preprocessing Modu<PERSON>
Handles cleaning, normalization, and anonymization of transcription text.
"""

import re
import json
import logging
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import nltk
import spacy
from nltk.corpus import stopwords
from nltk.tokenize import sent_tokenize, word_tokenize

# Download required NLTK data
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')

try:
    nltk.data.find('corpora/stopwords')
except LookupError:
    nltk.download('stopwords')

@dataclass
class PreprocessingConfig:
    """Configuration for text preprocessing"""
    language: str = 'en'  # Default to English, supports 'en' and 'es'
    remove_filler_words: bool = True
    anonymize_names: bool = True
    normalize_whitespace: bool = True
    remove_timestamps: bool = True
    min_sentence_length: int = 10
    preserve_speaker_labels: bool = True

class TextPreprocessor:
    """Main text preprocessing class for UX research transcriptions"""
    
    def __init__(self, config: PreprocessingConfig = None):
        self.config = config or PreprocessingConfig()
        self.logger = logging.getLogger(__name__)
        
        # Load spaCy model
        try:
            if self.config.language == 'es':
                self.nlp = spacy.load('es_core_news_sm')
            else:
                self.nlp = spacy.load('en_core_web_sm')
        except OSError:
            self.logger.warning(f"spaCy model not found for {self.config.language}, using basic processing")
            self.nlp = None
        
        # Load stopwords
        try:
            self.stopwords = set(stopwords.words('spanish' if self.config.language == 'es' else 'english'))
        except LookupError:
            self.stopwords = set()
        
        # Define filler words patterns
        self.filler_patterns = self._get_filler_patterns()
        
        # Name anonymization mapping
        self.name_mapping = {}
        self.name_counter = 1
    
    def _get_filler_patterns(self) -> List[str]:
        """Get filler word patterns based on language"""
        if self.config.language == 'es':
            return [
                r'\b(eh|ehm|mm|mmm|este|bueno|o sea|como que|tipo|pues|entonces)\b',
                r'\b(verdad|¿verdad\?|¿no\?|¿sí\?)\b',
                r'\b(digamos|supongamos|por ejemplo)\b'
            ]
        else:
            return [
                r'\b(uh|uhm|um|hmm|like|you know|I mean|sort of|kind of)\b',
                r'\b(right\?|okay\?|yeah\?)\b',
                r'\b(basically|actually|literally)\b'
            ]
    
    def preprocess_transcription(self, text: str, metadata: Dict = None) -> Dict:
        """
        Main preprocessing pipeline for transcription text
        
        Args:
            text: Raw transcription text
            metadata: Optional metadata about the transcription
            
        Returns:
            Dict with processed text and processing information
        """
        self.logger.info("Starting text preprocessing")
        
        original_length = len(text)
        processing_steps = []
        
        # Step 1: Basic cleaning
        text = self._basic_cleaning(text)
        processing_steps.append({
            'step': 'basic_cleaning',
            'length_before': original_length,
            'length_after': len(text)
        })
        
        # Step 2: Remove timestamps
        if self.config.remove_timestamps:
            text = self._remove_timestamps(text)
            processing_steps.append({
                'step': 'remove_timestamps',
                'length_after': len(text)
            })
        
        # Step 3: Remove filler words
        if self.config.remove_filler_words:
            text = self._remove_filler_words(text)
            processing_steps.append({
                'step': 'remove_filler_words',
                'length_after': len(text)
            })
        
        # Step 4: Anonymize names
        if self.config.anonymize_names:
            text, anonymization_map = self._anonymize_names(text)
            processing_steps.append({
                'step': 'anonymize_names',
                'length_after': len(text),
                'names_anonymized': len(anonymization_map)
            })
        
        # Step 5: Normalize whitespace
        if self.config.normalize_whitespace:
            text = self._normalize_whitespace(text)
            processing_steps.append({
                'step': 'normalize_whitespace',
                'length_after': len(text)
            })
        
        # Step 6: Extract speaker segments
        segments = self._extract_speaker_segments(text)
        
        result = {
            'processed_text': text,
            'segments': segments,
            'processing_steps': processing_steps,
            'anonymization_map': getattr(self, '_last_anonymization_map', {}),
            'statistics': {
                'original_length': original_length,
                'processed_length': len(text),
                'reduction_percentage': round((1 - len(text) / original_length) * 100, 2),
                'segment_count': len(segments)
            },
            'metadata': metadata or {}
        }
        
        self.logger.info(f"Preprocessing completed. Reduced text by {result['statistics']['reduction_percentage']}%")
        return result
    
    def _basic_cleaning(self, text: str) -> str:
        """Basic text cleaning operations"""
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove special characters but preserve punctuation
        text = re.sub(r'[^\w\s\.\,\?\!\:\;\-\(\)\[\]\"\']+', '', text)
        
        # Fix common transcription errors
        text = re.sub(r'\.{3,}', '...', text)  # Multiple dots
        text = re.sub(r'\?{2,}', '?', text)    # Multiple question marks
        text = re.sub(r'\!{2,}', '!', text)    # Multiple exclamation marks
        
        return text.strip()
    
    def _remove_timestamps(self, text: str) -> str:
        """Remove timestamp patterns from text"""
        # Common timestamp patterns
        patterns = [
            r'\[\d{1,2}:\d{2}:\d{2}\]',  # [HH:MM:SS]
            r'\(\d{1,2}:\d{2}:\d{2}\)',  # (HH:MM:SS)
            r'\d{1,2}:\d{2}:\d{2}',      # HH:MM:SS
            r'\[\d{1,2}:\d{2}\]',        # [MM:SS]
            r'\(\d{1,2}:\d{2}\)',        # (MM:SS)
        ]
        
        for pattern in patterns:
            text = re.sub(pattern, '', text)
        
        return text
    
    def _remove_filler_words(self, text: str) -> str:
        """Remove filler words and verbal tics"""
        for pattern in self.filler_patterns:
            text = re.sub(pattern, '', text, flags=re.IGNORECASE)
        
        # Remove repeated words (e.g., "muy muy muy bueno" -> "muy bueno")
        text = re.sub(r'\b(\w+)\s+\1\s+\1+\b', r'\1', text, flags=re.IGNORECASE)
        
        return text
    
    def _anonymize_names(self, text: str) -> Tuple[str, Dict]:
        """Anonymize personal names in the text"""
        anonymization_map = {}
        
        if self.nlp:
            # Use spaCy NER for name detection
            doc = self.nlp(text)
            
            for ent in doc.ents:
                if ent.label_ in ['PER', 'PERSON']:  # Person entities
                    name = ent.text
                    if name not in self.name_mapping:
                        self.name_mapping[name] = f"Participante_{self.name_counter}"
                        self.name_counter += 1
                    
                    anonymized_name = self.name_mapping[name]
                    text = text.replace(name, anonymized_name)
                    anonymization_map[name] = anonymized_name
        else:
            # Fallback: simple pattern matching for common names
            # This is a basic implementation - in production, use a more comprehensive approach
            name_patterns = [
                r'\b[A-Z][a-z]+\s+[A-Z][a-z]+\b',  # First Last
                r'\b[A-Z][a-z]+\b(?=\s+dice|dijo|comenta|menciona)'  # Name before speech verbs
            ]
            
            for pattern in name_patterns:
                matches = re.finditer(pattern, text)
                for match in matches:
                    name = match.group()
                    if name not in self.name_mapping:
                        self.name_mapping[name] = f"Participante_{self.name_counter}"
                        self.name_counter += 1
                    
                    anonymized_name = self.name_mapping[name]
                    text = text.replace(name, anonymized_name)
                    anonymization_map[name] = anonymized_name
        
        self._last_anonymization_map = anonymization_map
        return text, anonymization_map
    
    def _normalize_whitespace(self, text: str) -> str:
        """Normalize whitespace and line breaks"""
        # Replace multiple spaces with single space
        text = re.sub(r' +', ' ', text)
        
        # Replace multiple line breaks with double line break
        text = re.sub(r'\n+', '\n\n', text)
        
        # Remove trailing/leading whitespace
        text = text.strip()
        
        return text
    
    def _extract_speaker_segments(self, text: str) -> List[Dict]:
        """Extract segments by speaker if speaker labels are present"""
        segments = []
        
        # Pattern for speaker labels (e.g., "Entrevistador:", "P1:", "Usuario:")
        speaker_pattern = r'^([A-Za-z0-9_]+):\s*(.+?)(?=\n[A-Za-z0-9_]+:|$)'
        
        matches = re.finditer(speaker_pattern, text, re.MULTILINE | re.DOTALL)
        
        matches_list = list(re.finditer(speaker_pattern, text, re.MULTILINE | re.DOTALL))

        if matches_list:
            for i, match in enumerate(matches_list):
                speaker = match.group(1)
                content = match.group(2).strip()

                if len(content) >= self.config.min_sentence_length:
                    segments.append({
                        'segment_id': i + 1,
                        'speaker': speaker,
                        'content': content,
                        'word_count': len(content.split()),
                        'char_count': len(content)
                    })
        else:
            # No speaker labels found, treat as single segment
            if len(text) >= self.config.min_sentence_length:
                segments.append({
                    'segment_id': 1,
                    'speaker': 'Unknown',
                    'content': text,
                    'word_count': len(text.split()),
                    'char_count': len(text)
                })
        
        return segments
    
    def get_text_statistics(self, text: str) -> Dict:
        """Get detailed statistics about the text"""
        sentences = sent_tokenize(text)
        words = word_tokenize(text)
        
        return {
            'character_count': len(text),
            'word_count': len(words),
            'sentence_count': len(sentences),
            'average_words_per_sentence': round(len(words) / len(sentences), 2) if sentences else 0,
            'unique_words': len(set(word.lower() for word in words if word.isalpha())),
            'vocabulary_richness': round(len(set(word.lower() for word in words if word.isalpha())) / len(words), 3) if words else 0
        }

def preprocess_transcription_for_n8n(transcription_text: str, config_dict: Dict = None) -> Dict:
    """
    Wrapper function for n8n integration
    
    Args:
        transcription_text: Raw transcription text
        config_dict: Configuration dictionary
        
    Returns:
        Processed transcription data
    """
    # Convert config dict to PreprocessingConfig
    config = PreprocessingConfig()
    if config_dict:
        for key, value in config_dict.items():
            if hasattr(config, key):
                setattr(config, key, value)
    
    # Create preprocessor and process text
    preprocessor = TextPreprocessor(config)
    result = preprocessor.preprocess_transcription(transcription_text)
    
    return result

if __name__ == "__main__":
    # Example usage
    sample_text = """
    [00:01:23] Entrevistador: Hola María, ¿cómo estás?
    [00:01:25] María: Eh, bien, bien. Este... muy bien, gracias.
    [00:01:30] Entrevistador: Perfecto. Entonces, eh, cuéntame sobre tu experiencia con la aplicación.
    [00:01:35] María: Bueno, o sea, la verdad es que... mm... fue un poco confusa al principio, ¿verdad?
    """
    
    config = PreprocessingConfig(
        language='es',
        remove_filler_words=True,
        anonymize_names=True
    )
    
    preprocessor = TextPreprocessor(config)
    result = preprocessor.preprocess_transcription(sample_text)
    
    print(json.dumps(result, indent=2, ensure_ascii=False))

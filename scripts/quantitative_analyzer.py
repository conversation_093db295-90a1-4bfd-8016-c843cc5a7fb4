"""
ROBO-RESEARCHER-2000 Quantitative Analysis Module
Performs statistical analysis on coded qualitative data.
"""

import json
import logging
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from collections import Counter, defaultdict
import pandas as pd
import numpy as np
from scipy import stats
from scipy.stats import chi2_contingency
import matplotlib.pyplot as plt
import seaborn as sns
from io import BytesIO
import base64

@dataclass
class QuantitativeConfig:
    """Configuration for quantitative analysis"""
    min_frequency_threshold: int = 2
    significance_level: float = 0.05
    correlation_threshold: float = 0.3
    generate_visualizations: bool = True
    include_statistical_tests: bool = True

class QuantitativeAnalyzer:
    """Performs quantitative analysis on coded qualitative data"""
    
    def __init__(self, config: QuantitativeConfig = None):
        self.config = config or QuantitativeConfig()
        self.logger = logging.getLogger(__name__)
        
        # Set up matplotlib for headless operation
        plt.switch_backend('Agg')
        sns.set_style("whitegrid")
        sns.set_palette("husl")
    
    def analyze_coded_data(self, coded_segments: List[Dict], metadata: Dict = None) -> Dict:
        """
        Perform comprehensive quantitative analysis on coded data
        
        Args:
            coded_segments: List of coded segments
            metadata: Optional metadata about the study
            
        Returns:
            Comprehensive analysis results
        """
        self.logger.info("Starting quantitative analysis")
        
        if not coded_segments:
            return {'error': 'No coded segments provided'}
        
        # Convert to DataFrame for easier analysis
        df = self._create_dataframe(coded_segments)
        
        # Perform different types of analysis
        results = {
            'basic_statistics': self._calculate_basic_statistics(df),
            'code_frequency_analysis': self._analyze_code_frequencies(df),
            'intensity_analysis': self._analyze_intensity_patterns(df),
            'emotion_analysis': self._analyze_emotion_patterns(df),
            'participant_analysis': self._analyze_participant_patterns(df),
            'correlation_analysis': self._analyze_correlations(df),
            'metadata': metadata or {}
        }
        
        # Add statistical tests if enabled
        if self.config.include_statistical_tests:
            results['statistical_tests'] = self._perform_statistical_tests(df)
        
        # Generate visualizations if enabled
        if self.config.generate_visualizations:
            results['visualizations'] = self._generate_visualizations(df)
        
        # Add insights and recommendations
        results['insights'] = self._generate_insights(results)
        
        self.logger.info("Quantitative analysis completed")
        return results
    
    def _create_dataframe(self, coded_segments: List[Dict]) -> pd.DataFrame:
        """Convert coded segments to pandas DataFrame"""
        rows = []
        
        for segment in coded_segments:
            # Create one row per code (exploded format)
            for code in segment.get('codes', []):
                rows.append({
                    'segment_id': segment.get('segment_id'),
                    'participant_id': segment.get('participant_id'),
                    'code': code,
                    'intensity': segment.get('intensity', 1.0),
                    'emotion': segment.get('emotion', 'neutral'),
                    'text_length': len(segment.get('text', '')),
                    'word_count': len(segment.get('text', '').split()),
                    'context': segment.get('context', 'general')
                })
        
        return pd.DataFrame(rows)
    
    def _calculate_basic_statistics(self, df: pd.DataFrame) -> Dict:
        """Calculate basic descriptive statistics"""
        return {
            'total_segments': df['segment_id'].nunique(),
            'total_codes': df['code'].nunique(),
            'total_participants': df['participant_id'].nunique(),
            'total_code_instances': len(df),
            'average_codes_per_segment': round(len(df) / df['segment_id'].nunique(), 2),
            'average_intensity': round(df['intensity'].mean(), 2),
            'intensity_std': round(df['intensity'].std(), 2),
            'average_text_length': round(df['text_length'].mean(), 1),
            'average_word_count': round(df['word_count'].mean(), 1)
        }
    
    def _analyze_code_frequencies(self, df: pd.DataFrame) -> Dict:
        """Analyze code frequency patterns"""
        code_counts = df['code'].value_counts()
        
        # Filter by minimum frequency threshold
        significant_codes = code_counts[code_counts >= self.config.min_frequency_threshold]
        
        # Calculate percentages
        total_codes = len(df)
        code_percentages = (significant_codes / total_codes * 100).round(1)
        
        # Participant coverage (how many participants mentioned each code)
        participant_coverage = df.groupby('code')['participant_id'].nunique()
        total_participants = df['participant_id'].nunique()
        coverage_percentages = (participant_coverage / total_participants * 100).round(1)
        
        return {
            'top_codes': {
                code: {
                    'frequency': int(count),
                    'percentage': float(code_percentages.get(code, 0)),
                    'participant_coverage': int(participant_coverage.get(code, 0)),
                    'coverage_percentage': float(coverage_percentages.get(code, 0))
                }
                for code, count in significant_codes.head(15).items()
            },
            'rare_codes': {
                code: int(count)
                for code, count in code_counts[code_counts < self.config.min_frequency_threshold].items()
            },
            'frequency_distribution': {
                'high_frequency (>10)': len(code_counts[code_counts > 10]),
                'medium_frequency (5-10)': len(code_counts[(code_counts >= 5) & (code_counts <= 10)]),
                'low_frequency (2-4)': len(code_counts[(code_counts >= 2) & (code_counts < 5)]),
                'single_mention (1)': len(code_counts[code_counts == 1])
            }
        }
    
    def _analyze_intensity_patterns(self, df: pd.DataFrame) -> Dict:
        """Analyze intensity patterns across codes and participants"""
        # Intensity by code
        intensity_by_code = df.groupby('code')['intensity'].agg(['mean', 'std', 'count']).round(2)
        intensity_by_code = intensity_by_code[intensity_by_code['count'] >= self.config.min_frequency_threshold]
        
        # Intensity by participant
        intensity_by_participant = df.groupby('participant_id')['intensity'].agg(['mean', 'std', 'count']).round(2)
        
        # High intensity codes (above average)
        avg_intensity = df['intensity'].mean()
        high_intensity_codes = intensity_by_code[intensity_by_code['mean'] > avg_intensity].sort_values('mean', ascending=False)
        
        return {
            'overall_intensity': {
                'mean': round(df['intensity'].mean(), 2),
                'median': round(df['intensity'].median(), 2),
                'std': round(df['intensity'].std(), 2),
                'min': float(df['intensity'].min()),
                'max': float(df['intensity'].max())
            },
            'intensity_by_code': {
                code: {
                    'mean_intensity': float(row['mean']),
                    'std_intensity': float(row['std']),
                    'frequency': int(row['count'])
                }
                for code, row in intensity_by_code.head(10).iterrows()
            },
            'high_intensity_codes': {
                code: float(row['mean'])
                for code, row in high_intensity_codes.head(5).iterrows()
            },
            'intensity_by_participant': {
                participant: {
                    'mean_intensity': float(row['mean']),
                    'code_count': int(row['count'])
                }
                for participant, row in intensity_by_participant.iterrows()
            }
        }
    
    def _analyze_emotion_patterns(self, df: pd.DataFrame) -> Dict:
        """Analyze emotional patterns in the data"""
        # Overall emotion distribution
        emotion_counts = df['emotion'].value_counts()
        emotion_percentages = (emotion_counts / len(df) * 100).round(1)
        
        # Emotion by code
        emotion_by_code = pd.crosstab(df['code'], df['emotion'], normalize='index') * 100
        emotion_by_code = emotion_by_code.round(1)
        
        # Emotion by participant
        emotion_by_participant = pd.crosstab(df['participant_id'], df['emotion'], normalize='index') * 100
        emotion_by_participant = emotion_by_participant.round(1)
        
        # Codes with strong emotional associations
        emotional_codes = {}
        for emotion in ['positive', 'negative']:
            if emotion in emotion_by_code.columns:
                top_codes = emotion_by_code[emotion].sort_values(ascending=False).head(5)
                emotional_codes[emotion] = {
                    code: float(percentage)
                    for code, percentage in top_codes.items()
                    if percentage > 50  # Only codes with >50% of this emotion
                }
        
        return {
            'emotion_distribution': {
                emotion: {
                    'count': int(count),
                    'percentage': float(emotion_percentages[emotion])
                }
                for emotion, count in emotion_counts.items()
            },
            'emotional_codes': emotional_codes,
            'participant_emotion_profiles': {
                participant: {
                    emotion: float(percentage)
                    for emotion, percentage in row.items()
                    if percentage > 0
                }
                for participant, row in emotion_by_participant.iterrows()
            }
        }
    
    def _analyze_participant_patterns(self, df: pd.DataFrame) -> Dict:
        """Analyze patterns across participants"""
        # Participant activity levels
        participant_activity = df.groupby('participant_id').agg({
            'code': 'count',
            'intensity': 'mean',
            'segment_id': 'nunique'
        }).round(2)
        
        participant_activity.columns = ['total_codes', 'avg_intensity', 'segments_coded']
        
        # Unique codes per participant
        unique_codes_per_participant = df.groupby('participant_id')['code'].nunique()
        
        # Most active participants
        most_active = participant_activity.sort_values('total_codes', ascending=False)
        
        # Participant diversity (how many unique codes they use)
        code_diversity = (unique_codes_per_participant / participant_activity['total_codes']).round(2)
        
        return {
            'participant_summary': {
                participant: {
                    'total_codes': int(row['total_codes']),
                    'unique_codes': int(unique_codes_per_participant[participant]),
                    'avg_intensity': float(row['avg_intensity']),
                    'segments_coded': int(row['segments_coded']),
                    'code_diversity': float(code_diversity.get(participant, 0))
                }
                for participant, row in participant_activity.iterrows()
            },
            'most_active_participants': {
                participant: int(row['total_codes'])
                for participant, row in most_active.head(5).iterrows()
            },
            'participation_statistics': {
                'avg_codes_per_participant': round(participant_activity['total_codes'].mean(), 1),
                'std_codes_per_participant': round(participant_activity['total_codes'].std(), 1),
                'most_codes_by_single_participant': int(participant_activity['total_codes'].max()),
                'least_codes_by_single_participant': int(participant_activity['total_codes'].min())
            }
        }
    
    def _analyze_correlations(self, df: pd.DataFrame) -> Dict:
        """Analyze correlations between different variables"""
        correlations = {}
        
        # Intensity vs text length correlation
        if len(df) > 10:  # Need sufficient data
            intensity_length_corr = stats.pearsonr(df['intensity'], df['text_length'])
            intensity_words_corr = stats.pearsonr(df['intensity'], df['word_count'])
            
            correlations['intensity_correlations'] = {
                'intensity_vs_text_length': {
                    'correlation': round(intensity_length_corr[0], 3),
                    'p_value': round(intensity_length_corr[1], 3),
                    'significant': intensity_length_corr[1] < self.config.significance_level
                },
                'intensity_vs_word_count': {
                    'correlation': round(intensity_words_corr[0], 3),
                    'p_value': round(intensity_words_corr[1], 3),
                    'significant': intensity_words_corr[1] < self.config.significance_level
                }
            }
        
        # Code co-occurrence analysis
        code_cooccurrence = self._calculate_code_cooccurrence(df)
        if code_cooccurrence:
            correlations['code_cooccurrence'] = code_cooccurrence
        
        return correlations
    
    def _calculate_code_cooccurrence(self, df: pd.DataFrame) -> Dict:
        """Calculate code co-occurrence patterns"""
        # Group by segment to find codes that appear together
        segment_codes = df.groupby('segment_id')['code'].apply(list).reset_index()
        
        cooccurrence_counts = defaultdict(int)
        total_pairs = 0
        
        for codes in segment_codes['code']:
            if len(codes) > 1:
                for i, code1 in enumerate(codes):
                    for j, code2 in enumerate(codes):
                        if i < j:  # Avoid duplicates and self-pairs
                            pair = tuple(sorted([code1, code2]))
                            cooccurrence_counts[pair] += 1
                            total_pairs += 1
        
        if total_pairs == 0:
            return {}
        
        # Calculate co-occurrence strength
        cooccurrence_results = []
        for (code1, code2), count in cooccurrence_counts.items():
            if count >= 2:  # Minimum co-occurrence threshold
                strength = count / total_pairs
                cooccurrence_results.append({
                    'code1': code1,
                    'code2': code2,
                    'count': count,
                    'strength': round(strength, 3)
                })
        
        # Sort by strength
        cooccurrence_results.sort(key=lambda x: x['strength'], reverse=True)
        
        return {
            'top_cooccurrences': cooccurrence_results[:10],
            'total_cooccurrence_pairs': len(cooccurrence_counts),
            'total_pair_instances': total_pairs
        }
    
    def _perform_statistical_tests(self, df: pd.DataFrame) -> Dict:
        """Perform statistical significance tests"""
        tests = {}
        
        # Chi-square test for emotion vs code independence
        if len(df) > 20 and df['emotion'].nunique() > 1:
            try:
                contingency_table = pd.crosstab(df['code'], df['emotion'])
                chi2, p_value, dof, expected = chi2_contingency(contingency_table)
                
                tests['emotion_code_independence'] = {
                    'chi2_statistic': round(chi2, 3),
                    'p_value': round(p_value, 3),
                    'degrees_of_freedom': dof,
                    'significant': p_value < self.config.significance_level,
                    'interpretation': 'Emotions and codes are independent' if p_value >= self.config.significance_level else 'Emotions and codes are dependent'
                }
            except Exception as e:
                self.logger.warning(f"Chi-square test failed: {e}")
        
        # ANOVA for intensity differences across participants
        if df['participant_id'].nunique() > 2:
            try:
                participant_groups = [group['intensity'].values for name, group in df.groupby('participant_id')]
                f_stat, p_value = stats.f_oneway(*participant_groups)
                
                tests['intensity_participant_anova'] = {
                    'f_statistic': round(f_stat, 3),
                    'p_value': round(p_value, 3),
                    'significant': p_value < self.config.significance_level,
                    'interpretation': 'No significant intensity differences between participants' if p_value >= self.config.significance_level else 'Significant intensity differences between participants'
                }
            except Exception as e:
                self.logger.warning(f"ANOVA test failed: {e}")
        
        return tests
    
    def _generate_visualizations(self, df: pd.DataFrame) -> Dict:
        """Generate visualization data (base64 encoded images)"""
        visualizations = {}
        
        try:
            # Code frequency bar chart
            code_counts = df['code'].value_counts().head(10)
            
            plt.figure(figsize=(12, 6))
            code_counts.plot(kind='bar')
            plt.title('Top 10 Code Frequencies')
            plt.xlabel('Codes')
            plt.ylabel('Frequency')
            plt.xticks(rotation=45, ha='right')
            plt.tight_layout()
            
            buffer = BytesIO()
            plt.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
            buffer.seek(0)
            visualizations['code_frequency_chart'] = base64.b64encode(buffer.getvalue()).decode()
            plt.close()
            
            # Intensity distribution histogram
            plt.figure(figsize=(10, 6))
            plt.hist(df['intensity'], bins=20, alpha=0.7, edgecolor='black')
            plt.title('Intensity Distribution')
            plt.xlabel('Intensity Score')
            plt.ylabel('Frequency')
            plt.axvline(df['intensity'].mean(), color='red', linestyle='--', label=f'Mean: {df["intensity"].mean():.2f}')
            plt.legend()
            plt.tight_layout()
            
            buffer = BytesIO()
            plt.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
            buffer.seek(0)
            visualizations['intensity_distribution'] = base64.b64encode(buffer.getvalue()).decode()
            plt.close()
            
            # Emotion pie chart
            emotion_counts = df['emotion'].value_counts()
            
            plt.figure(figsize=(8, 8))
            plt.pie(emotion_counts.values, labels=emotion_counts.index, autopct='%1.1f%%', startangle=90)
            plt.title('Emotion Distribution')
            plt.axis('equal')
            
            buffer = BytesIO()
            plt.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
            buffer.seek(0)
            visualizations['emotion_pie_chart'] = base64.b64encode(buffer.getvalue()).decode()
            plt.close()
            
        except Exception as e:
            self.logger.warning(f"Visualization generation failed: {e}")
            visualizations['error'] = str(e)
        
        return visualizations
    
    def _generate_insights(self, results: Dict) -> List[Dict]:
        """Generate actionable insights from the analysis"""
        insights = []
        
        # Code frequency insights
        basic_stats = results.get('basic_statistics', {})
        code_freq = results.get('code_frequency_analysis', {})
        
        if basic_stats.get('average_codes_per_segment', 0) > 3:
            insights.append({
                'type': 'high_coding_density',
                'title': 'High Coding Density Detected',
                'description': f'Segments have an average of {basic_stats["average_codes_per_segment"]} codes each, indicating rich, complex data.',
                'recommendation': 'Consider grouping related codes into higher-level categories for clearer analysis.',
                'priority': 'medium'
            })
        
        # Intensity insights
        intensity_analysis = results.get('intensity_analysis', {})
        avg_intensity = intensity_analysis.get('overall_intensity', {}).get('mean', 0)
        
        if avg_intensity > 3.5:
            insights.append({
                'type': 'high_emotional_intensity',
                'title': 'High Emotional Intensity',
                'description': f'Average intensity score is {avg_intensity}, indicating strong emotional responses.',
                'recommendation': 'Focus on high-intensity segments for critical user experience issues.',
                'priority': 'high'
            })
        
        # Participation insights
        participant_analysis = results.get('participant_analysis', {})
        participation_stats = participant_analysis.get('participation_statistics', {})
        
        std_codes = participation_stats.get('std_codes_per_participant', 0)
        avg_codes = participation_stats.get('avg_codes_per_participant', 0)
        
        if std_codes > avg_codes * 0.5:
            insights.append({
                'type': 'uneven_participation',
                'title': 'Uneven Participant Contribution',
                'description': 'Large variation in participant contribution levels detected.',
                'recommendation': 'Review data collection process to ensure balanced participation.',
                'priority': 'medium'
            })
        
        return insights

def analyze_quantitative_data_for_n8n(coded_segments: List[Dict], config_dict: Dict = None) -> Dict:
    """
    Wrapper function for n8n integration
    
    Args:
        coded_segments: List of coded segments
        config_dict: Configuration dictionary
        
    Returns:
        Quantitative analysis results
    """
    config = QuantitativeConfig()
    if config_dict:
        for key, value in config_dict.items():
            if hasattr(config, key):
                setattr(config, key, value)
    
    analyzer = QuantitativeAnalyzer(config)
    results = analyzer.analyze_coded_data(coded_segments)
    
    return results

if __name__ == "__main__":
    # Example usage
    sample_coded_segments = [
        {
            'segment_id': '1',
            'participant_id': 'P001',
            'codes': ['frustration', 'navigation'],
            'intensity': 4.0,
            'emotion': 'negative',
            'text': 'Me frustré mucho cuando intenté buscar el producto y no aparecía nada.',
            'context': 'search task'
        },
        {
            'segment_id': '2',
            'participant_id': 'P001',
            'codes': ['satisfaction', 'design'],
            'intensity': 3.5,
            'emotion': 'positive',
            'text': 'La aplicación es bastante fácil de usar en general, me gusta el diseño.',
            'context': 'general usage'
        }
    ]
    
    result = analyze_quantitative_data_for_n8n(sample_coded_segments)
    print(json.dumps(result, indent=2, ensure_ascii=False))

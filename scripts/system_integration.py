#!/usr/bin/env python3
"""
System Integration Module for ROBO-RESEARCHER-2000
Integrates all security, monitoring, and processing systems
"""

import os
import json
import logging
import asyncio
from typing import Dict, Any, Optional
from datetime import datetime
from pathlib import Path

# Import all system components
from .auth_system import auth_manager
from .file_security import secure_uploader
from .encryption_manager import encryption_manager
from .audit_logger import audit_logger
from .privacy_manager import privacy_manager
from .error_handler import error_handler, ErrorCategory, ErrorSeverity
from .ai_provider_manager import ai_manager
from .quota_manager import quota_manager
from .cost_monitor import cost_monitor
from .offline_processor import offline_processor

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SystemIntegrator:
    """Main system integration class"""
    
    def __init__(self):
        self.initialized = False
        self.system_status = {}
        self._initialize_systems()
    
    def _initialize_systems(self):
        """Initialize all system components"""
        try:
            # Initialize directories
            self._ensure_directories()
            
            # Test system components
            self.system_status = {
                'auth_system': self._test_auth_system(),
                'file_security': self._test_file_security(),
                'encryption': self._test_encryption(),
                'audit_logging': self._test_audit_logging(),
                'privacy_manager': self._test_privacy_manager(),
                'error_handler': self._test_error_handler(),
                'ai_providers': self._test_ai_providers(),
                'quota_manager': self._test_quota_manager(),
                'cost_monitor': self._test_cost_monitor(),
                'offline_processor': self._test_offline_processor()
            }
            
            self.initialized = True
            logger.info("System integration completed successfully")
            
        except Exception as e:
            error_handler.log_error(
                ErrorCategory.SYSTEM,
                f"System initialization failed: {str(e)}",
                ErrorSeverity.CRITICAL,
                exception=e
            )
            raise
    
    def _ensure_directories(self):
        """Ensure all required directories exist"""
        directories = [
            "data",
            "data/uploads",
            "data/uploads/quarantine",
            "data/backups",
            "data/audit_logs",
            "data/temp",
            "logs"
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    def _test_auth_system(self) -> Dict[str, Any]:
        """Test authentication system"""
        try:
            # Test user creation and authentication
            test_result = auth_manager.register_user(
                "system_test", "<EMAIL>", "test_password_123"
            )
            
            if test_result["success"]:
                # Clean up test user
                with auth_manager.db.lock:
                    import sqlite3
                    with sqlite3.connect(auth_manager.db.db_path) as conn:
                        conn.execute("DELETE FROM users WHERE username = ?", ("system_test",))
            
            return {"status": "operational", "details": "Authentication system working"}
            
        except Exception as e:
            return {"status": "error", "details": str(e)}
    
    def _test_file_security(self) -> Dict[str, Any]:
        """Test file security system"""
        try:
            # Test file validation
            validator = secure_uploader.validator
            test_result = validator.validate_file_extension("test.txt")
            
            return {
                "status": "operational" if test_result else "warning",
                "details": "File security system initialized"
            }
            
        except Exception as e:
            return {"status": "error", "details": str(e)}
    
    def _test_encryption(self) -> Dict[str, Any]:
        """Test encryption system"""
        try:
            # Test encryption/decryption
            test_data = "System integration test"
            encrypt_result = encryption_manager.encrypt_text(test_data, "system_test")
            
            if encrypt_result["success"]:
                decrypt_result = encryption_manager.decrypt_text(encrypt_result["data_id"])
                if decrypt_result["success"] and decrypt_result["text"] == test_data:
                    # Clean up test data
                    encryption_manager.delete_encrypted_data(encrypt_result["data_id"])
                    return {"status": "operational", "details": "Encryption system working"}
            
            return {"status": "error", "details": "Encryption test failed"}
            
        except Exception as e:
            return {"status": "error", "details": str(e)}
    
    def _test_audit_logging(self) -> Dict[str, Any]:
        """Test audit logging system"""
        try:
            # Test audit logging
            audit_logger.log_system_event("system_integration_test", {"test": True})
            return {"status": "operational", "details": "Audit logging system working"}
            
        except Exception as e:
            return {"status": "error", "details": str(e)}
    
    def _test_privacy_manager(self) -> Dict[str, Any]:
        """Test privacy manager"""
        try:
            # Test data registration
            privacy_manager.register_data(
                "system_test", privacy_manager.DataCategory.TEMPORARY_DATA, "system_test"
            )
            return {"status": "operational", "details": "Privacy manager working"}
            
        except Exception as e:
            return {"status": "error", "details": str(e)}
    
    def _test_error_handler(self) -> Dict[str, Any]:
        """Test error handler"""
        try:
            # Test error logging
            error_handler.log_error(
                ErrorCategory.SYSTEM,
                "System integration test error",
                ErrorSeverity.LOW,
                {"test": True}
            )
            return {"status": "operational", "details": "Error handler working"}
            
        except Exception as e:
            return {"status": "error", "details": str(e)}
    
    def _test_ai_providers(self) -> Dict[str, Any]:
        """Test AI provider system"""
        try:
            providers = ai_manager.get_available_providers()
            return {
                "status": "operational" if providers else "warning",
                "details": f"{len(providers)} AI providers available"
            }
            
        except Exception as e:
            return {"status": "error", "details": str(e)}
    
    def _test_quota_manager(self) -> Dict[str, Any]:
        """Test quota manager"""
        try:
            stats = quota_manager.get_system_stats()
            return {"status": "operational", "details": "Quota manager working"}
            
        except Exception as e:
            return {"status": "error", "details": str(e)}
    
    def _test_cost_monitor(self) -> Dict[str, Any]:
        """Test cost monitor"""
        try:
            stats = cost_monitor.get_cost_dashboard("system_test")
            return {"status": "operational", "details": "Cost monitor working"}
            
        except Exception as e:
            return {"status": "error", "details": str(e)}
    
    def _test_offline_processor(self) -> Dict[str, Any]:
        """Test offline processor"""
        try:
            available = offline_processor.is_available()
            return {
                "status": "operational" if available else "warning",
                "details": "Offline processor available" if available else "Limited offline capabilities"
            }
            
        except Exception as e:
            return {"status": "error", "details": str(e)}
    
    async def process_secure_workflow(self, user_id: str, session_id: str, 
                                    file_data: Any, project_name: str) -> Dict[str, Any]:
        """Process workflow with full security integration"""
        
        # Set audit context
        audit_logger.set_context(user_id=user_id, session_id=session_id)
        
        try:
            # Step 1: Authenticate and authorize
            audit_logger.log_user_action("workflow_start", f"project:{project_name}")
            
            # Step 2: Secure file upload
            upload_result = secure_uploader.upload_file(file_data, user_id, f"{project_name}.txt")
            if not upload_result["success"]:
                raise Exception(f"File upload failed: {upload_result['error']}")
            
            audit_logger.log_user_action("file_upload", upload_result["secure_filename"], 
                                       {"file_size": upload_result["file_size"]})
            
            # Step 3: Check quotas
            quota_check = await quota_manager.check_quota(user_id, project_name)
            if not quota_check["allowed"]:
                raise Exception("Quota exceeded")
            
            # Step 4: Encrypt sensitive data
            file_content = open(upload_result["file_path"], 'r').read()
            encrypt_result = encryption_manager.encrypt_text(file_content, "transcription")
            if not encrypt_result["success"]:
                raise Exception("Encryption failed")
            
            # Step 5: Register data for privacy tracking
            privacy_manager.register_data(
                encrypt_result["data_id"],
                privacy_manager.DataCategory.TRANSCRIPTION,
                user_id,
                metadata={"project": project_name, "original_filename": f"{project_name}.txt"}
            )
            
            # Step 6: Process with AI (with cost monitoring)
            try:
                ai_result = await quota_manager.make_request(
                    user_id, project_name,
                    "Analyze this transcription for UX insights",
                    max_tokens=2000
                )
                
                # Record cost
                await cost_monitor.record_usage(
                    user_id, project_name, ai_result["provider"],
                    ai_result["model"], ai_result["tokens_used"], "analysis"
                )
                
            except Exception as e:
                # Fallback to offline processing
                logger.warning(f"AI processing failed, using offline fallback: {e}")
                
                decrypt_result = encryption_manager.decrypt_text(encrypt_result["data_id"])
                if decrypt_result["success"]:
                    ai_result = await offline_processor.process_insight_generation({
                        "transcription": decrypt_result["text"]
                    })
                else:
                    raise Exception("Both AI and offline processing failed")
            
            # Step 7: Encrypt results
            results_encrypt = encryption_manager.encrypt_json(ai_result, "analysis_results")
            if not results_encrypt["success"]:
                raise Exception("Results encryption failed")
            
            # Step 8: Register results data
            privacy_manager.register_data(
                results_encrypt["data_id"],
                privacy_manager.DataCategory.ANALYSIS_RESULTS,
                user_id,
                metadata={"project": project_name, "analysis_type": "ux_insights"}
            )
            
            # Step 9: Log successful completion
            audit_logger.log_user_action("workflow_complete", f"project:{project_name}", 
                                       {"results_id": results_encrypt["data_id"]})
            
            return {
                "success": True,
                "project_name": project_name,
                "transcription_id": encrypt_result["data_id"],
                "results_id": results_encrypt["data_id"],
                "processing_method": "ai" if "provider" in ai_result else "offline"
            }
            
        except Exception as e:
            # Log error and attempt recovery
            error_id = error_handler.log_error(
                ErrorCategory.PROCESSING,
                f"Workflow failed: {str(e)}",
                ErrorSeverity.HIGH,
                {"user_id": user_id, "project": project_name},
                user_id=user_id,
                session_id=session_id,
                exception=e
            )
            
            audit_logger.log_user_action("workflow_error", f"project:{project_name}", 
                                       {"error_id": error_id}, success=False)
            
            return {
                "success": False,
                "error": str(e),
                "error_id": error_id
            }
    
    def get_system_health(self) -> Dict[str, Any]:
        """Get comprehensive system health status"""
        
        health_data = {
            "timestamp": datetime.now().isoformat(),
            "overall_status": "operational",
            "systems": self.system_status,
            "metrics": {
                "error_rate": error_handler.get_error_stats(1)["total_errors"],
                "active_users": len(quota_manager.active_requests),
                "encryption_stats": encryption_manager.get_encryption_stats(),
                "audit_health": audit_logger.get_system_health(1)
            }
        }
        
        # Determine overall status
        error_systems = [name for name, status in self.system_status.items() 
                        if status.get("status") == "error"]
        
        if error_systems:
            health_data["overall_status"] = "degraded"
            health_data["critical_issues"] = error_systems
        
        return health_data
    
    def cleanup_system(self):
        """Perform system cleanup and maintenance"""
        try:
            # Clean up expired data
            privacy_manager.cleanup_expired_data()
            
            # Rotate audit logs
            audit_logger.rotate_logs()
            
            # Clean up temporary files
            temp_dir = Path("data/temp")
            if temp_dir.exists():
                for file in temp_dir.glob("*"):
                    if file.is_file() and file.stat().st_mtime < (datetime.now().timestamp() - 86400):
                        file.unlink()
            
            logger.info("System cleanup completed")
            
        except Exception as e:
            error_handler.log_error(
                ErrorCategory.SYSTEM,
                f"System cleanup failed: {str(e)}",
                ErrorSeverity.MEDIUM,
                exception=e
            )

# Global instance
system_integrator = SystemIntegrator()

if __name__ == "__main__":
    # Test system integration
    def test():
        print("Testing system integration...")
        
        health = system_integrator.get_system_health()
        print(f"System health: {health['overall_status']}")
        
        for system, status in health["systems"].items():
            print(f"  {system}: {status['status']} - {status['details']}")
        
        # Perform cleanup
        system_integrator.cleanup_system()
        print("System integration test completed")
    
    test()

#!/bin/bash

# n8n Owner Account Setup Script
# This script automatically creates the owner account for n8n to skip the setup wizard

set -e

# Configuration
N8N_CONTAINER="robo-researcher-n8n"
OWNER_EMAIL="<EMAIL>"
OWNER_PASSWORD="robo-researcher-2000"
OWNER_FIRST_NAME="ROBO"
OWNER_LAST_NAME="RESEARCHER"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if n8n container is running
check_n8n_container() {
    if ! docker ps --filter "name=$N8N_CONTAINER" --filter "status=running" | grep -q "$N8N_CONTAINER"; then
        print_error "n8n container is not running!"
        return 1
    fi
    return 0
}

# Function to wait for n8n to be ready
wait_for_n8n() {
    print_status "Waiting for n8n to be ready..."
    local retries=0
    local max_retries=30
    
    while [ $retries -lt $max_retries ]; do
        if curl -s http://localhost:5678/healthz > /dev/null 2>&1; then
            print_success "n8n is ready"
            return 0
        fi
        
        printf "\r   Waiting... (%d/%d)" "$((retries + 1))" "$max_retries"
        sleep 2
        ((retries++))
    done
    
    echo ""
    print_error "n8n did not become ready in time"
    return 1
}

# Function to check if owner account already exists
check_owner_exists() {
    print_status "Checking if owner account already exists..."
    
    # Try to login with the owner credentials
    local auth_response=$(curl -s -X POST http://localhost:5678/api/v1/login \
        -H "Content-Type: application/json" \
        -d "{\"email\":\"$OWNER_EMAIL\",\"password\":\"$OWNER_PASSWORD\"}" \
        2>/dev/null || echo "")
    
    if [[ $auth_response == *"token"* ]]; then
        print_success "Owner account already exists and is accessible"
        return 0
    fi
    
    return 1
}

# Function to create owner account using n8n CLI
create_owner_account() {
    print_status "Creating owner account using n8n CLI..."
    
    # Create owner account using n8n CLI inside the container
    local create_result=$(docker exec "$N8N_CONTAINER" n8n user:create \
        --email="$OWNER_EMAIL" \
        --password="$OWNER_PASSWORD" \
        --firstName="$OWNER_FIRST_NAME" \
        --lastName="$OWNER_LAST_NAME" \
        --role=owner \
        2>&1 || echo "")
    
    if [[ $create_result == *"successfully created"* ]] || [[ $create_result == *"already exists"* ]]; then
        print_success "Owner account created successfully"
        return 0
    else
        print_warning "CLI creation result: $create_result"
        return 1
    fi
}

# Function to setup owner account via API
setup_owner_via_api() {
    print_status "Setting up owner account via API..."
    
    # Check if setup endpoint is available
    local setup_check=$(curl -s http://localhost:5678/api/v1/setup 2>/dev/null || echo "")
    
    if [[ $setup_check == *"not found"* ]]; then
        print_warning "Setup API endpoint not available"
        return 1
    fi
    
    # Try to create owner via setup API
    local setup_response=$(curl -s -X POST http://localhost:5678/api/v1/setup \
        -H "Content-Type: application/json" \
        -d "{
            \"email\":\"$OWNER_EMAIL\",
            \"password\":\"$OWNER_PASSWORD\",
            \"firstName\":\"$OWNER_FIRST_NAME\",
            \"lastName\":\"$OWNER_LAST_NAME\"
        }" 2>/dev/null || echo "")
    
    if [[ $setup_response == *"token"* ]] || [[ $setup_response == *"success"* ]]; then
        print_success "Owner account setup via API successful"
        return 0
    else
        print_warning "API setup result: $setup_response"
        return 1
    fi
}

# Function to verify owner account access
verify_owner_access() {
    print_status "Verifying owner account access..."
    
    local auth_response=$(curl -s -X POST http://localhost:5678/api/v1/login \
        -H "Content-Type: application/json" \
        -d "{\"email\":\"$OWNER_EMAIL\",\"password\":\"$OWNER_PASSWORD\"}" \
        2>/dev/null || echo "")
    
    if [[ $auth_response == *"token"* ]]; then
        print_success "✅ Owner account is accessible"
        print_success "✅ Login credentials: $OWNER_EMAIL / $OWNER_PASSWORD"
        return 0
    else
        print_error "❌ Owner account verification failed"
        print_error "Response: $auth_response"
        return 1
    fi
}

# Main execution
main() {
    echo "🔧 n8n Owner Account Setup"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo ""
    
    # Check if container is running
    if ! check_n8n_container; then
        exit 1
    fi
    
    # Wait for n8n to be ready
    if ! wait_for_n8n; then
        exit 1
    fi
    
    echo ""
    
    # Check if owner already exists
    if check_owner_exists; then
        echo ""
        print_success "🎉 Owner account is already configured and working!"
        exit 0
    fi
    
    echo ""
    print_status "Owner account not found. Creating new owner account..."
    
    # Try multiple methods to create the owner account
    local success=false
    
    # Method 1: Try CLI creation
    if create_owner_account; then
        success=true
    fi
    
    # Method 2: Try API setup (if CLI failed)
    if [ "$success" = false ]; then
        if setup_owner_via_api; then
            success=true
        fi
    fi
    
    # Wait a moment for changes to take effect
    if [ "$success" = true ]; then
        print_status "Waiting for account setup to complete..."
        sleep 5
    fi
    
    echo ""
    
    # Verify the setup worked
    if verify_owner_access; then
        echo ""
        print_success "🎉 n8n owner account setup completed successfully!"
        echo ""
        echo "📋 Access Information:"
        echo "   🌐 URL: http://localhost:5678"
        echo "   📧 Email: $OWNER_EMAIL"
        echo "   🔐 Password: $OWNER_PASSWORD"
        echo ""
    else
        echo ""
        print_error "❌ Owner account setup failed!"
        echo ""
        echo "🔧 Manual Setup Required:"
        echo "   1. Open: http://localhost:5678"
        echo "   2. Complete the setup wizard manually"
        echo "   3. Use the credentials above"
        echo ""
        exit 1
    fi
}

# Run main function
main "$@"

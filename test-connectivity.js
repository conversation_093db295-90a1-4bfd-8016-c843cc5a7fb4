#!/usr/bin/env node

/**
 * ROBO-RESEARCHER-2000 Connectivity Test Suite
 * Tests all components and their connectivity
 */

const http = require('http');
const https = require('https');

class ConnectivityTester {
    constructor() {
        this.services = {
            'n8n': { url: 'http://localhost:5678/healthz', method: 'GET' },
            'Wiki.js': { url: 'http://localhost:3002', method: 'GET' },
            'MinIO': { url: 'http://localhost:9002', method: 'GET' },
            'Client': { url: 'http://localhost:8080', method: 'GET' },
            'Client (dev)': { url: 'http://localhost:8081', method: 'GET' }
        };
        
        this.webhooks = {
            'Main Webhook': { 
                url: 'http://localhost:5678/webhook/robo-researcher', 
                method: 'POST',
                data: JSON.stringify({
                    test: true,
                    projectName: 'Connectivity Test',
                    email: '<EMAIL>'
                })
            },
            'Test Webhook': { 
                url: 'http://localhost:5678/webhook/test-robo-researcher', 
                method: 'POST',
                data: JSON.stringify({ test: 'connectivity' })
            }
        };
        
        this.results = {
            services: {},
            webhooks: {},
            overall: 'UNKNOWN'
        };
    }

    async testService(name, config) {
        return new Promise((resolve) => {
            const url = new URL(config.url);
            const options = {
                hostname: url.hostname,
                port: url.port,
                path: url.pathname,
                method: config.method,
                timeout: 5000,
                headers: {
                    'Content-Type': 'application/json',
                    'User-Agent': 'ROBO-RESEARCHER-2000-Test'
                }
            };

            const req = http.request(options, (res) => {
                let data = '';
                res.on('data', chunk => data += chunk);
                res.on('end', () => {
                    resolve({
                        status: 'SUCCESS',
                        code: res.statusCode,
                        message: `HTTP ${res.statusCode}`,
                        data: data.substring(0, 100)
                    });
                });
            });

            req.on('error', (err) => {
                resolve({
                    status: 'ERROR',
                    code: 0,
                    message: err.message,
                    data: null
                });
            });

            req.on('timeout', () => {
                req.destroy();
                resolve({
                    status: 'TIMEOUT',
                    code: 0,
                    message: 'Request timeout',
                    data: null
                });
            });

            if (config.data) {
                req.write(config.data);
            }
            
            req.end();
        });
    }

    async testWebhook(name, config) {
        return this.testService(name, config);
    }

    async runTests() {
        console.log('🤖 ROBO-RESEARCHER-2000 Connectivity Test Suite');
        console.log('=' .repeat(60));
        
        // Test services
        console.log('\n📡 Testing Services...');
        for (const [name, config] of Object.entries(this.services)) {
            process.stdout.write(`  ${name.padEnd(15)} ... `);
            const result = await this.testService(name, config);
            this.results.services[name] = result;
            
            if (result.status === 'SUCCESS') {
                console.log(`✅ ${result.message}`);
            } else {
                console.log(`❌ ${result.message}`);
            }
        }

        // Test webhooks
        console.log('\n🔗 Testing Webhooks...');
        for (const [name, config] of Object.entries(this.webhooks)) {
            process.stdout.write(`  ${name.padEnd(15)} ... `);
            const result = await this.testWebhook(name, config);
            this.results.webhooks[name] = result;
            
            if (result.status === 'SUCCESS') {
                console.log(`✅ ${result.message}`);
            } else {
                console.log(`❌ ${result.message}`);
            }
        }

        // Overall assessment
        this.assessOverall();
        this.printSummary();
    }

    assessOverall() {
        const serviceResults = Object.values(this.results.services);
        const webhookResults = Object.values(this.results.webhooks);
        
        const servicesOk = serviceResults.filter(r => r.status === 'SUCCESS').length;
        const webhooksOk = webhookResults.filter(r => r.status === 'SUCCESS').length;
        
        const totalServices = serviceResults.length;
        const totalWebhooks = webhookResults.length;
        
        if (servicesOk === totalServices && webhooksOk >= 0) {
            this.results.overall = 'EXCELLENT';
        } else if (servicesOk >= totalServices * 0.8) {
            this.results.overall = 'GOOD';
        } else if (servicesOk >= totalServices * 0.5) {
            this.results.overall = 'FAIR';
        } else {
            this.results.overall = 'POOR';
        }
    }

    printSummary() {
        console.log('\n📊 Test Summary');
        console.log('=' .repeat(60));
        
        const serviceResults = Object.values(this.results.services);
        const webhookResults = Object.values(this.results.webhooks);
        
        const servicesOk = serviceResults.filter(r => r.status === 'SUCCESS').length;
        const webhooksOk = webhookResults.filter(r => r.status === 'SUCCESS').length;
        
        console.log(`Services:  ${servicesOk}/${serviceResults.length} ✅`);
        console.log(`Webhooks:  ${webhooksOk}/${webhookResults.length} ✅`);
        console.log(`Overall:   ${this.results.overall}`);
        
        if (this.results.overall === 'EXCELLENT') {
            console.log('\n🎉 All systems operational! Ready for production.');
        } else if (this.results.overall === 'GOOD') {
            console.log('\n✅ System is mostly operational. Minor issues detected.');
        } else {
            console.log('\n⚠️  System has connectivity issues. Check failed services.');
        }
        
        console.log('\n💡 Next steps:');
        console.log('  1. Ensure all Docker containers are running');
        console.log('  2. Check n8n workflows are active');
        console.log('  3. Verify environment variables are set');
        console.log('  4. Test with actual file upload');
    }
}

// Run tests
if (require.main === module) {
    const tester = new ConnectivityTester();
    tester.runTests().catch(console.error);
}

module.exports = ConnectivityTester;

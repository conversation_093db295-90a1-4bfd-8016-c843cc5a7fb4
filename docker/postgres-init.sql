-- ROBO-RESEARCHER-2000 PostgreSQL Initialization Script
-- Creates databases for n8n and Wiki.js

-- Create databases if they don't exist
SELECT 'CREATE DATABASE wikijs'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'wikijs')\gexec

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE n8n TO n8n;
GRANT ALL PRIVILEGES ON DATABASE wikijs TO n8n;

-- Create extensions for n8n
\c n8n;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Create extensions for wikijs
\c wikijs;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Switch back to default database
\c postgres;

-- Log completion
SELECT 'ROBO-RESEARCHER-2000 PostgreSQL initialization completed' AS status;

# ===========================================
# ROBO-RESEARCHER-2000 Complete Production Configuration
# Enterprise-Grade UX Research Automation Platform
#
# ✅ Complete 17-Step Workflow Implementation
# ✅ Enterprise Security & Authentication
# ✅ Multi-Provider AI Fallback System
# ✅ Comprehensive Monitoring & Audit Logging
# ✅ GDPR Compliance & Privacy Controls
# ✅ Cost Monitoring & Budget Management
# ✅ Error Handling & Recovery Systems
#
# Last Updated: 2025-01-22
# ===========================================

# n8n Configuration
N8N_HOST=localhost
N8N_PORT=5678
N8N_PROTOCOL=http
N8N_WEBHOOK_URL=http://localhost:5678/webhook/robo-researcher
N8N_BASIC_AUTH_ACTIVE=true
N8N_BASIC_AUTH_USER=admin
N8N_BASIC_AUTH_PASSWORD=robo-researcher-2000

# MinIO Configuration (Updated ports to avoid conflicts)
MINIO_ENDPOINT=localhost:9002
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_BUCKET_NAME=robo-researcher-data
MINIO_USE_SSL=false
MINIO_CONSOLE_URL=http://localhost:9003

# Wiki.js Configuration (Updated port to avoid conflicts)
WIKIJS_URL=http://localhost:3002
WIKIJS_API_TOKEN=eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************.bUgArC0BR9QgK1AQHgsKxQt-4ZfvMeob0SKNaYe08_uPd9wT194Z902z1X5H5UQjCtk92eRnSyqldwuSL8ins17eWOSCJMpUVrhGmTg2CBSAk8Z73grRctfFobUzYTIrDqeXO5yP0YXtnq4O0FcbpfKGgBPJLwujUF0LahnXQTi97Y2tD86eThCBjZGsNyfPjVcXVdpafmEJjnhdXv1rTY1wnVHIY5nJ_Bu2BjM4mK82BUQB60EzQChBndYGMAMqp45BJlaeZwidVMHpUx_ZfnZixmIutvoDZc8rCheuPrD_yeZO_jkPuBGB2I_ScoKcWAV2E43-FyeP8VHHKnTJrQ

# Redis Configuration (Updated port to avoid conflicts)
REDIS_HOST=localhost
REDIS_PORT=6380
REDIS_PASSWORD=

# PostgreSQL Configuration (Updated port to avoid conflicts)
POSTGRES_HOST=localhost
POSTGRES_PORT=5433
POSTGRES_DB=n8n
POSTGRES_USER=n8n
POSTGRES_PASSWORD=n8n_password

# OpenRouter AI Configuration - REQUIRED
OPENROUTER_API_KEY=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
OPENROUTER_MODEL=anthropic/claude-3-sonnet

# SMTP Configuration - REQUIRED for notifications
SMTP_HOST=mail.stargety.com
SMTP_PORT=465
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_smtp_password_here
SMTP_FROM_NAME=ROBO-RESEARCHER-2000
SMTP_USE_TLS=true

# Client Configuration
CLIENT_PORT=8080
CLIENT_URL=http://localhost:8080

# Analysis Configuration
DEFAULT_LANGUAGE=es
MAX_SEGMENTS_PER_ANALYSIS=1000
ENABLE_SENTIMENT_ANALYSIS=true
ENABLE_ENTITY_EXTRACTION=true

# ===========================================
# SECURITY & AUTHENTICATION CONFIGURATION
# ===========================================

# JWT and Authentication
JWT_SECRET=robo-researcher-jwt-secret-2025-secure-key-change-in-production
ADMIN_PASSWORD=robo-researcher-2000
SESSION_TIMEOUT_HOURS=24
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION_MINUTES=15

# API Rate Limiting
API_RATE_LIMIT=100
DAILY_REQUEST_LIMIT=1000
DAILY_TOKEN_LIMIT=500000
MONTHLY_COST_LIMIT=100.0

# File Upload Security
MAX_FILE_SIZE_MB=10
ALLOWED_FILE_TYPES=txt,doc,docx,pdf,rtf,odt
ENABLE_VIRUS_SCANNING=true
QUARANTINE_SUSPICIOUS_FILES=true

# Data Encryption
ENCRYPTION_KEY_PATH=/home/<USER>/.n8n/data/encryption.key
ENABLE_DATA_ENCRYPTION=true
AUTO_ENCRYPT_SENSITIVE_DATA=true

# ===========================================
# MONITORING & LOGGING CONFIGURATION
# ===========================================

# Audit Logging
ENABLE_AUDIT_LOGGING=true
AUDIT_LOG_RETENTION_DAYS=365
AUDIT_LOG_LEVEL=INFO
ENABLE_SECURITY_ALERTS=true

# Cost Monitoring
ENABLE_COST_MONITORING=true
COST_ALERT_THRESHOLD=0.8
ENABLE_BUDGET_ALERTS=true
COST_TRACKING_PRECISION=4

# Error Handling
ENABLE_ERROR_RECOVERY=true
MAX_RETRY_ATTEMPTS=3
CIRCUIT_BREAKER_THRESHOLD=5
CIRCUIT_BREAKER_TIMEOUT=60

# Privacy & GDPR
ENABLE_PRIVACY_CONTROLS=true
DATA_RETENTION_DAYS=365
AUTO_DELETE_EXPIRED_DATA=true
GDPR_COMPLIANCE_MODE=true

# ===========================================
# AI PROVIDER CONFIGURATION
# ===========================================

# Multiple AI Provider Support (Fallback chain)
# Primary: OpenRouter, Fallback: OpenAI, Anthropic, Google, Local
ENABLE_AI_FALLBACK=true
AI_PROVIDER_TIMEOUT=30
AI_REQUEST_RETRY_COUNT=3

# OpenAI (Fallback Provider)
OPENAI_API_KEY=your_openai_key_here
OPENAI_MODEL=gpt-4o-mini
OPENAI_MAX_TOKENS=4000

# Anthropic (Fallback Provider)
ANTHROPIC_API_KEY=your_anthropic_key_here
ANTHROPIC_MODEL=claude-3-haiku-20240307
ANTHROPIC_MAX_TOKENS=4000

# Google (Fallback Provider)
GOOGLE_API_KEY=your_google_key_here
GOOGLE_MODEL=gemini-pro
GOOGLE_MAX_TOKENS=4000

# Local AI (Ultimate Fallback)
LOCAL_AI_ENDPOINT=http://localhost:11434
LOCAL_AI_MODEL=llama2
ENABLE_OFFLINE_PROCESSING=true

# ===========================================
# SYSTEM CONFIGURATION
# ===========================================

# Development & Production
DEBUG=false
LOG_LEVEL=INFO
ENVIRONMENT=production
GENERIC_TIMEZONE=America/Mexico_City
NODE_ENV=production

# Docker Configuration
COMPOSE_PROJECT_NAME=robo-researcher-2000
DOCKER_NETWORK=robo-researcher-network

# System Health
ENABLE_HEALTH_CHECKS=true
HEALTH_CHECK_INTERVAL=30
SYSTEM_MAINTENANCE_HOUR=2

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_ENCRYPTION=true

# ===========================================
# FEATURE FLAGS
# ===========================================

# Core Features
ENABLE_17_STEP_WORKFLOW=true
ENABLE_HUMAN_REVIEW_CHECKPOINTS=true
ENABLE_SEMI_AUTOMATED_MODE=true

# Advanced Features
ENABLE_PATTERN_DETECTION=true
ENABLE_ARCHETYPE_CREATION=true
ENABLE_HMW_GENERATION=true
ENABLE_OPPORTUNITY_PRIORITIZATION=true

# Integration Features
ENABLE_WIKI_INTEGRATION=true
ENABLE_EMAIL_NOTIFICATIONS=true
ENABLE_MINIO_STORAGE=true

# Security Features
ENABLE_FILE_UPLOAD_SECURITY=true
ENABLE_COMPREHENSIVE_AUDIT=true
ENABLE_DATA_PRIVACY_CONTROLS=true
